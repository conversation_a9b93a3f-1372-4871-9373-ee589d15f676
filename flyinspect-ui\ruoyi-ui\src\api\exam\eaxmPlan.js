import request from '@/utils/request'

// 查询检测计划列表
export function listEaxmPlan(query) {
  return request({
    url: '/exam/eaxmPlan/list',
    method: 'get',
    params: query
  })
}

// 查询检测计划详细
export function getEaxmPlan(examPlanId) {
  return request({
    url: '/exam/eaxmPlan/' + examPlanId,
    method: 'get'
  })
}

// 新增检测计划
export function addEaxmPlan(data) {
  return request({
    url: '/exam/eaxmPlan',
    method: 'post',
    data: data
  })
}

// 修改检测计划
export function updateEaxmPlan(data) {
  return request({
    url: '/exam/eaxmPlan',
    method: 'put',
    data: data
  })
}

// 删除检测计划
export function delEaxmPlan(examPlanId) {
  return request({
    url: '/exam/eaxmPlan/' + examPlanId,
    method: 'delete'
  })
}

// 查询检查对象列表
export function queryTaskObj(query) {
  return request({
    url: '/exam/eaxmPlan/queryTaskObj',
    method: 'get',
    params: query
  })
}

// 批量导入检查对象
export function batchInsertTaskObj(data) {
  return request({
    url: '/exam/eaxmPlan/batchInsertTaskObj',
    method: 'post',
    data: data
  })
}

// 全部导入
export function allInsertTaskObj(data) {
  return request({
    url: '/exam/eaxmPlan/allInsertTaskObj',
    method: 'post',
    data: data
  })
}

// 删除查询名录
export function batchDeleteTaskObj(examPlanId) {
  return request({
    url: '/exam/eaxmPlan/batchDeleteTaskObj/' + examPlanId,
    method: 'delete'
  })
}


