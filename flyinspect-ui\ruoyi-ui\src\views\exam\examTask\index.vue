<template>
  <div >
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="检查任务id" prop="examTaskId">
        <el-input
          v-model="queryParams.examTaskId"
          placeholder="请输入检查任务id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检查对象名录id" prop="examObjListId">
        <el-input
          v-model="queryParams.examObjListId"
          placeholder="请输入检查对象名录id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检查人员名录id" prop="examPsnListId">
        <el-input
          v-model="queryParams.examPsnListId"
          placeholder="请输入检查人员名录id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="抽取顺序" prop="selcSeq">
        <el-input
          v-model="queryParams.selcSeq"
          placeholder="请输入抽取顺序"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="有效标志" prop="valiFlag">
        <el-input
          v-model="queryParams.valiFlag"
          placeholder="请输入有效标志"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数据唯一记录号" prop="rid">
        <el-input
          v-model="queryParams.rid"
          placeholder="请输入数据唯一记录号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人id" prop="crterId">
        <el-input
          v-model="queryParams.crterId"
          placeholder="请输入创建人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人姓名" prop="crterName">
        <el-input
          v-model="queryParams.crterName"
          placeholder="请输入创建人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数据创建时间" prop="crteTime">
        <el-date-picker clearable
          v-model="queryParams.crteTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择数据创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="创建机构编号" prop="crteOptinsNo">
        <el-input
          v-model="queryParams.crteOptinsNo"
          placeholder="请输入创建机构编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="经办人id" prop="opterId">
        <el-input
          v-model="queryParams.opterId"
          placeholder="请输入经办人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="经办人姓名" prop="opterName">
        <el-input
          v-model="queryParams.opterName"
          placeholder="请输入经办人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="经办时间" prop="optTime">
        <el-date-picker clearable
          v-model="queryParams.optTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择经办时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="经办机构编号" prop="optinsNo">
        <el-input
          v-model="queryParams.optinsNo"
          placeholder="请输入经办机构编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数据更新时间" prop="updtTime">
        <el-date-picker clearable
          v-model="queryParams.updtTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择数据更新时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['exam:examTask:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['exam:examTask:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['exam:examTask:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['exam:examTask:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="examTaskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="检查任务结果id" align="center" prop="examTaskRsltId" />
      <el-table-column label="检查任务id" align="center" prop="examTaskId" />
      <el-table-column label="检查对象名录id" align="center" prop="examObjListId" />
      <el-table-column label="检查人员名录id" align="center" prop="examPsnListId" />
      <el-table-column label="抽取顺序" align="center" prop="selcSeq" />
      <el-table-column label="有效标志" align="center" prop="valiFlag" />
      <el-table-column label="数据唯一记录号" align="center" prop="rid" />
      <el-table-column label="创建人id" align="center" prop="crterId" />
      <el-table-column label="创建人姓名" align="center" prop="crterName" />
      <el-table-column label="数据创建时间" align="center" prop="crteTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.crteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建机构编号" align="center" prop="crteOptinsNo" />
      <el-table-column label="经办人id" align="center" prop="opterId" />
      <el-table-column label="经办人姓名" align="center" prop="opterName" />
      <el-table-column label="经办时间" align="center" prop="optTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.optTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="经办机构编号" align="center" prop="optinsNo" />
      <el-table-column label="数据更新时间" align="center" prop="updtTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updtTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['exam:examTask:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['exam:examTask:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改检查任务结果对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="检查任务id" prop="examTaskId">
          <el-input v-model="form.examTaskId" placeholder="请输入检查任务id" />
        </el-form-item>
        <el-form-item label="检查对象名录id" prop="examObjListId">
          <el-input v-model="form.examObjListId" placeholder="请输入检查对象名录id" />
        </el-form-item>
        <el-form-item label="检查人员名录id" prop="examPsnListId">
          <el-input v-model="form.examPsnListId" placeholder="请输入检查人员名录id" />
        </el-form-item>
        <el-form-item label="抽取顺序" prop="selcSeq">
          <el-input v-model="form.selcSeq" placeholder="请输入抽取顺序" />
        </el-form-item>
        <el-form-item label="有效标志" prop="valiFlag">
          <el-input v-model="form.valiFlag" placeholder="请输入有效标志" />
        </el-form-item>
        <el-form-item label="数据唯一记录号" prop="rid">
          <el-input v-model="form.rid" placeholder="请输入数据唯一记录号" />
        </el-form-item>
        <el-form-item label="创建人id" prop="crterId">
          <el-input v-model="form.crterId" placeholder="请输入创建人id" />
        </el-form-item>
        <el-form-item label="创建人姓名" prop="crterName">
          <el-input v-model="form.crterName" placeholder="请输入创建人姓名" />
        </el-form-item>
        <el-form-item label="数据创建时间" prop="crteTime">
          <el-date-picker clearable
            v-model="form.crteTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择数据创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="创建机构编号" prop="crteOptinsNo">
          <el-input v-model="form.crteOptinsNo" placeholder="请输入创建机构编号" />
        </el-form-item>
        <el-form-item label="经办人id" prop="opterId">
          <el-input v-model="form.opterId" placeholder="请输入经办人id" />
        </el-form-item>
        <el-form-item label="经办人姓名" prop="opterName">
          <el-input v-model="form.opterName" placeholder="请输入经办人姓名" />
        </el-form-item>
        <el-form-item label="经办时间" prop="optTime">
          <el-date-picker clearable
            v-model="form.optTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择经办时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="经办机构编号" prop="optinsNo">
          <el-input v-model="form.optinsNo" placeholder="请输入经办机构编号" />
        </el-form-item>
        <el-form-item label="数据更新时间" prop="updtTime">
          <el-date-picker clearable
            v-model="form.updtTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择数据更新时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listExamTask, getExamTask, delExamTask, addExamTask, updateExamTask } from "@/api/exam/examTask";

export default {
  name: "ExamTask",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检查任务结果表格数据
      examTaskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        examTaskId: null,
        examObjListId: null,
        examPsnListId: null,
        selcSeq: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        examTaskId: [
          { required: true, message: "检查任务id不能为空", trigger: "blur" }
        ],
        examObjListId: [
          { required: true, message: "检查对象名录id不能为空", trigger: "blur" }
        ],
        valiFlag: [
          { required: true, message: "有效标志不能为空", trigger: "blur" }
        ],
        rid: [
          { required: true, message: "数据唯一记录号不能为空", trigger: "blur" }
        ],
        crterId: [
          { required: true, message: "创建人id不能为空", trigger: "blur" }
        ],
        crterName: [
          { required: true, message: "创建人姓名不能为空", trigger: "blur" }
        ],
        crteTime: [
          { required: true, message: "数据创建时间不能为空", trigger: "blur" }
        ],
        crteOptinsNo: [
          { required: true, message: "创建机构编号不能为空", trigger: "blur" }
        ],
        opterId: [
          { required: true, message: "经办人id不能为空", trigger: "blur" }
        ],
        opterName: [
          { required: true, message: "经办人姓名不能为空", trigger: "blur" }
        ],
        optTime: [
          { required: true, message: "经办时间不能为空", trigger: "blur" }
        ],
        optinsNo: [
          { required: true, message: "经办机构编号不能为空", trigger: "blur" }
        ],
        updtTime: [
          { required: true, message: "数据更新时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询检查任务结果列表 */
    getList() {
      this.loading = true;
      listExamTask(this.queryParams).then(response => {
        this.examTaskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        examTaskRsltId: null,
        examTaskId: null,
        examObjListId: null,
        examPsnListId: null,
        selcSeq: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.examTaskRsltId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检查任务结果";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const examTaskRsltId = row.examTaskRsltId || this.ids
      getExamTask(examTaskRsltId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改检查任务结果";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.examTaskRsltId != null) {
            updateExamTask(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addExamTask(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const examTaskRsltIds = row.examTaskRsltId || this.ids;
      this.$modal.confirm('是否确认删除检查任务结果编号为"' + examTaskRsltIds + '"的数据项？').then(function() {
        return delExamTask(examTaskRsltIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('exam/examTask/export', {
        ...this.queryParams
      }, `examTask_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
