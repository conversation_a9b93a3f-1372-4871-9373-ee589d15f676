{"remainingRequest": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\dataAnalysis\\volaTask\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\dataAnalysis\\volaTask\\index.vue", "mtime": 1750389719298}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0Vm9sYVRhc2ssIGdldFZvbGFUYXNrLCBkZWxWb2xhVGFzaywgYWRkVm9sYVRhc2ssIHVwZGF0ZVZvbGFUYXNrIH0gZnJvbSAiQC9hcGkvZGF0YUFuYWx5c2lzL3ZvbGFUYXNrIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiVm9sYVRhc2siLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6LCD5bqm5Lu75Yqh5L+h5oGv6KGo5qC85pWw5o2uDQogICAgICB2b2xhVGFza0xpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5b2T5YmN5q2l6aqkDQogICAgICBjdXJyZW50U3RlcDogMCwNCiAgICAgIC8vIOepv+aireahhuaVsOaNrg0KICAgICAgdHJhbnNmZXJEYXRhOiBbXSwNCiAgICAgIC8vIOepv+aireahhumAieS4reeahOWAvA0KICAgICAgdHJhbnNmZXJWYWx1ZTogW10sDQogICAgICAvLyDmiYDmnInlj6/pgInop4TliJnliJfooagNCiAgICAgIGFsbFJ1bGVzOiBbDQogICAgICAgIHsga2V5OiAnMScsIGxhYmVsOiAn6KeE5YiZMe+8muWMu+S/neebruW9leWklueUqOiNr+ajgOafpScgfSwNCiAgICAgICAgeyBrZXk6ICcyJywgbGFiZWw6ICfop4TliJky77ya6LaF6YeP55So6I2v5qOA5p+lJyB9LA0KICAgICAgICB7IGtleTogJzMnLCBsYWJlbDogJ+inhOWImTPvvJrph43lpI3nlKjoja/mo4Dmn6UnIH0sDQogICAgICAgIHsga2V5OiAnNCcsIGxhYmVsOiAn6KeE5YiZNO+8mumFjeS8jeemgeW/jOajgOafpScgfSwNCiAgICAgICAgeyBrZXk6ICc1JywgbGFiZWw6ICfop4TliJk177ya6YCC5bqU55eH5qOA5p+lJyB9LA0KICAgICAgICB7IGtleTogJzYnLCBsYWJlbDogJ+inhOWImTbvvJror4rnlpfpobnnm67lkIjnkIbmgKfmo4Dmn6UnIH0sDQogICAgICAgIHsga2V5OiAnNycsIGxhYmVsOiAn6KeE5YiZN++8muWMu+eWl+acjeWKoeS7t+agvOajgOafpScgfSwNCiAgICAgICAgeyBrZXk6ICc4JywgbGFiZWw6ICfop4TliJk477ya5L2P6Zmi5aSp5pWw5ZCI55CG5oCn5qOA5p+lJyB9DQogICAgICBdLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHZvbGFUYXNrSW5mb05hbWU6IG51bGwsDQogICAgICAgIG1lZGluc0luZm86IG51bGwsDQogICAgICAgIG1lZGluc0x2OiBudWxsLA0KICAgICAgICBtZWRUeXBlOiBudWxsLA0KICAgICAgICBzZXRsVGltZVN0YXJ0OiBudWxsLA0KICAgICAgICBzZXRsVGltZUVuZDogbnVsbCwNCiAgICAgICAgcnVsZUlkczogbnVsbCwNCiAgICAgICAgcnVsZU5hbWVzOiBudWxsLA0KICAgICAgICB0YXNrVGltZTogbnVsbCwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICB2b2xhVGFza0luZm9OYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiwg+W6puS7u+WKoeWQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIG1lZGluc0luZm86IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi562b5p+l5Yy755aX5py65p6E6ZuG5ZCI5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgc2V0bFRpbWVTdGFydDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnu5PnrpflvIDlp4vml7bpl7TkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBzZXRsVGltZUVuZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnu5Pnrpfnu5PmnZ/ml7bpl7TkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICAvLyDms6jmhI/vvJpydWxlSWRz44CBcnVsZU5hbWVz44CBdGFza1RpbWUg55Sx56m/5qKt5qGG5ZKM57O757uf6Ieq5Yqo55Sf5oiQ77yM5LiN6ZyA6KaB5Zyo56ys5LiA5q2l6aqM6K+BDQogICAgICAgIC8vIGRlbEZsYWfjgIFjcmVhdGVCeeOAgWNyZWF0ZVRpbWXjgIF1cGRhdGVCeeOAgXVwZGF0ZVRpbWUg55Sx57O757uf6Ieq5Yqo5aSE55CG77yM5LiN6ZyA6KaB6aqM6K+BDQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6LosIPluqbku7vliqHkv6Hmga/liJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RWb2xhVGFzayh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy52b2xhVGFza0xpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMuY3VycmVudFN0ZXAgPSAwOw0KICAgICAgdGhpcy50cmFuc2ZlclZhbHVlID0gW107DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICAvLyDkuIvkuIDmraUNCiAgICBuZXh0U3RlcCgpIHsNCiAgICAgIGlmICh0aGlzLmN1cnJlbnRTdGVwID09PSAwKSB7DQogICAgICAgIC8vIOmqjOivgeesrOS4gOatpeihqOWNlQ0KICAgICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgICAgdGhpcy5jdXJyZW50U3RlcCA9IDE7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOS4iuS4gOatpQ0KICAgIHByZXZTdGVwKCkgew0KICAgICAgaWYgKHRoaXMuY3VycmVudFN0ZXAgPiAwKSB7DQogICAgICAgIHRoaXMuY3VycmVudFN0ZXAtLTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5jdXJyZW50U3RlcCA9IDA7DQogICAgICB0aGlzLnRyYW5zZmVyVmFsdWUgPSBbXTsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgdm9sYVRhc2tJbmZvSWQ6IG51bGwsDQogICAgICAgIHZvbGFUYXNrSW5mb05hbWU6IG51bGwsDQogICAgICAgIG1lZGluc0luZm86IG51bGwsDQogICAgICAgIG1lZGluc0x2OiBudWxsLA0KICAgICAgICBtZWRUeXBlOiBudWxsLA0KICAgICAgICBzZXRsVGltZVN0YXJ0OiBudWxsLA0KICAgICAgICBzZXRsVGltZUVuZDogbnVsbCwNCiAgICAgICAgcnVsZUlkczogbnVsbCwNCiAgICAgICAgcnVsZU5hbWVzOiBudWxsLA0KICAgICAgICB0YXNrVGltZTogbnVsbCwNCiAgICAgICAgZGVsRmxhZzogbnVsbCwNCiAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICB1cGRhdGVUaW1lOiBudWxsLA0KICAgICAgICByZW1hcms6IG51bGwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLnZvbGFUYXNrSW5mb0lkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6LCD5bqm5Lu75Yqh5L+h5oGvIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCB2b2xhVGFza0luZm9JZCA9IHJvdy52b2xhVGFza0luZm9JZCB8fCB0aGlzLmlkcw0KICAgICAgZ2V0Vm9sYVRhc2sodm9sYVRhc2tJbmZvSWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAvLyDlm57mmL7nqb/moq3moYbmlbDmja4NCiAgICAgICAgaWYgKHRoaXMuZm9ybS5ydWxlSWRzKSB7DQogICAgICAgICAgdGhpcy50cmFuc2ZlclZhbHVlID0gdGhpcy5mb3JtLnJ1bGVJZHMuc3BsaXQoJywnKTsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueiwg+W6puS7u+WKoeS/oeaBryI7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgLy8g5aSE55CG56m/5qKt5qGG6YCJ5Lit55qE6KeE5YiZDQogICAgICBpZiAodGhpcy50cmFuc2ZlclZhbHVlLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dXYXJuaW5nKCLor7foh7PlsJHpgInmi6nkuIDkuKrmo4Dmn6Xop4TliJkiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDlsIbpgInkuK3nmoTop4TliJlJROWSjOWQjeensOiuvue9ruWIsOihqOWNleS4rQ0KICAgICAgdGhpcy5mb3JtLnJ1bGVJZHMgPSB0aGlzLnRyYW5zZmVyVmFsdWUuam9pbignLCcpOw0KICAgICAgY29uc3Qgc2VsZWN0ZWRSdWxlTmFtZXMgPSB0aGlzLmFsbFJ1bGVzDQogICAgICAgIC5maWx0ZXIocnVsZSA9PiB0aGlzLnRyYW5zZmVyVmFsdWUuaW5jbHVkZXMocnVsZS5rZXkpKQ0KICAgICAgICAubWFwKHJ1bGUgPT4gcnVsZS5sYWJlbCkNCiAgICAgICAgLmpvaW4oJywnKTsNCiAgICAgIHRoaXMuZm9ybS5ydWxlTmFtZXMgPSBzZWxlY3RlZFJ1bGVOYW1lczsNCg0KICAgICAgLy8g6K6+572u5b2T5YmN5pe26Ze05Li65bu656uL5Lu75Yqh5pe26Ze0DQogICAgICB0aGlzLmZvcm0udGFza1RpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXTsNCg0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS52b2xhVGFza0luZm9JZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVWb2xhVGFzayh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuY3VycmVudFN0ZXAgPSAwOw0KICAgICAgICAgICAgICB0aGlzLnRyYW5zZmVyVmFsdWUgPSBbXTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkVm9sYVRhc2sodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRTdGVwID0gMDsNCiAgICAgICAgICAgICAgdGhpcy50cmFuc2ZlclZhbHVlID0gW107DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCB2b2xhVGFza0luZm9JZHMgPSByb3cudm9sYVRhc2tJbmZvSWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTosIPluqbku7vliqHkv6Hmga/nvJblj7fkuLoiJyArIHZvbGFUYXNrSW5mb0lkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbFZvbGFUYXNrKHZvbGFUYXNrSW5mb0lkcyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoJ2RhdGFBbmFseXNpcy92b2xhVGFzay9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0sIGB2b2xhVGFza18ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuUA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dataAnalysis/volaTask", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\r\n      <el-form-item label=\"调度任务名称\" prop=\"volaTaskInfoName\">\r\n        <el-input\r\n          v-model=\"queryParams.volaTaskInfoName\"\r\n          placeholder=\"请输入调度任务名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"医疗机构等级\" prop=\"medinsLv\">\r\n        <el-input\r\n          v-model=\"queryParams.medinsLv\"\r\n          placeholder=\"请输入医疗机构等级\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"结算开始时间\" prop=\"setlTimeStart\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.setlTimeStart\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择结算开始时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"结算结束时间\" prop=\"setlTimeEnd\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.setlTimeEnd\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择结算结束时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"规则ID集合\" prop=\"ruleIds\">\r\n        <el-input\r\n          v-model=\"queryParams.ruleIds\"\r\n          placeholder=\"请输入规则ID集合\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"建立任务时间\" prop=\"taskTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.taskTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择建立任务时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"volaTaskList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      fit\r\n      table-layout=\"auto\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column\r\n        label=\"调度任务名称\"\r\n        align=\"center\"\r\n        prop=\"volaTaskInfoName\"\r\n        min-width=\"150\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"筛查医疗机构集合\"\r\n        align=\"center\"\r\n        prop=\"medinsInfo\"\r\n        min-width=\"180\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"医疗机构等级\"\r\n        align=\"center\"\r\n        prop=\"medinsLv\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"医疗类别\"\r\n        align=\"center\"\r\n        prop=\"medType\"\r\n        width=\"100\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"结算开始时间\"\r\n        align=\"center\"\r\n        prop=\"setlTimeStart\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.setlTimeStart, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"结算结束时间\"\r\n        align=\"center\"\r\n        prop=\"setlTimeEnd\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.setlTimeEnd, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"规则ID集合\"\r\n        align=\"center\"\r\n        prop=\"ruleIds\"\r\n        min-width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"规则名称集合\"\r\n        align=\"center\"\r\n        prop=\"ruleNames\"\r\n        min-width=\"150\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"建立任务时间\"\r\n        align=\"center\"\r\n        prop=\"taskTime\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.taskTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"备注\"\r\n        align=\"center\"\r\n        prop=\"remark\"\r\n        min-width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        width=\"120\"\r\n        fixed=\"right\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['dataAnalysis:volaTask:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['dataAnalysis:volaTask:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改调度任务信息对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <!-- 步骤条 -->\r\n      <el-steps\r\n        :active=\"currentStep\"\r\n        finish-status=\"success\"\r\n        align-center\r\n        class=\"custom-steps\">\r\n        <el-step title=\"基本信息\" description=\"填写调度任务基本信息\">\r\n          <i slot=\"icon\" class=\"el-icon-edit-outline\"></i>\r\n        </el-step>\r\n        <el-step title=\"规则选择\" description=\"选择检查规则\">\r\n          <i slot=\"icon\" class=\"el-icon-setting\"></i>\r\n        </el-step>\r\n      </el-steps>\r\n\r\n      <!-- 第一步：基本信息 -->\r\n      <div v-show=\"currentStep === 0\">\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"调度任务名称\" prop=\"volaTaskInfoName\">\r\n                <el-input v-model=\"form.volaTaskInfoName\" placeholder=\"请输入调度任务名称\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"医疗机构等级\" prop=\"medinsLv\">\r\n                <el-input v-model=\"form.medinsLv\" placeholder=\"请输入医疗机构等级\" />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"筛查医疗机构集合\" prop=\"medinsInfo\">\r\n                <el-input v-model=\"form.medinsInfo\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入筛查医疗机构集合\" />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"结算开始时间\" prop=\"setlTimeStart\">\r\n                <el-date-picker\r\n                  clearable\r\n                  v-model=\"form.setlTimeStart\"\r\n                  type=\"date\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  placeholder=\"请选择结算开始时间\"\r\n                  style=\"width: 100%\">\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"结算结束时间\" prop=\"setlTimeEnd\">\r\n                <el-date-picker\r\n                  clearable\r\n                  v-model=\"form.setlTimeEnd\"\r\n                  type=\"date\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  placeholder=\"请选择结算结束时间\"\r\n                  style=\"width: 100%\">\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 第二步：规则选择 -->\r\n      <div v-show=\"currentStep === 1\">\r\n        <div style=\"margin-bottom: 20px;\">\r\n          <h4>选择检查规则</h4>\r\n          <p style=\"color: #666; font-size: 14px;\">请从左侧选择需要的检查规则，移动到右侧</p>\r\n        </div>\r\n        <el-transfer\r\n          v-model=\"transferValue\"\r\n          :data=\"allRules\"\r\n          :titles=\"['可选规则', '已选规则']\"\r\n          :button-texts=\"['移除', '添加']\"\r\n          :format=\"{\r\n            noChecked: '${total}',\r\n            hasChecked: '${checked}/${total}'\r\n          }\"\r\n          filterable\r\n          filter-placeholder=\"搜索规则\"\r\n          style=\"text-align: left; display: inline-block\">\r\n        </el-transfer>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n        <el-button v-if=\"currentStep > 0\" @click=\"prevStep\">上一步</el-button>\r\n        <el-button v-if=\"currentStep < 1\" type=\"primary\" @click=\"nextStep\">下一步</el-button>\r\n        <el-button v-if=\"currentStep === 1\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listVolaTask, getVolaTask, delVolaTask, addVolaTask, updateVolaTask } from \"@/api/dataAnalysis/volaTask\";\r\n\r\nexport default {\r\n  name: \"VolaTask\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 调度任务信息表格数据\r\n      volaTaskList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 当前步骤\r\n      currentStep: 0,\r\n      // 穿梭框数据\r\n      transferData: [],\r\n      // 穿梭框选中的值\r\n      transferValue: [],\r\n      // 所有可选规则列表\r\n      allRules: [\r\n        { key: '1', label: '规则1：医保目录外用药检查' },\r\n        { key: '2', label: '规则2：超量用药检查' },\r\n        { key: '3', label: '规则3：重复用药检查' },\r\n        { key: '4', label: '规则4：配伍禁忌检查' },\r\n        { key: '5', label: '规则5：适应症检查' },\r\n        { key: '6', label: '规则6：诊疗项目合理性检查' },\r\n        { key: '7', label: '规则7：医疗服务价格检查' },\r\n        { key: '8', label: '规则8：住院天数合理性检查' }\r\n      ],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        volaTaskInfoName: null,\r\n        medinsInfo: null,\r\n        medinsLv: null,\r\n        medType: null,\r\n        setlTimeStart: null,\r\n        setlTimeEnd: null,\r\n        ruleIds: null,\r\n        ruleNames: null,\r\n        taskTime: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        volaTaskInfoName: [\r\n          { required: true, message: \"调度任务名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        medinsInfo: [\r\n          { required: true, message: \"筛查医疗机构集合不能为空\", trigger: \"blur\" }\r\n        ],\r\n        setlTimeStart: [\r\n          { required: true, message: \"结算开始时间不能为空\", trigger: \"blur\" }\r\n        ],\r\n        setlTimeEnd: [\r\n          { required: true, message: \"结算结束时间不能为空\", trigger: \"blur\" }\r\n        ],\r\n        // 注意：ruleIds、ruleNames、taskTime 由穿梭框和系统自动生成，不需要在第一步验证\r\n        // delFlag、createBy、createTime、updateBy、updateTime 由系统自动处理，不需要验证\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询调度任务信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listVolaTask(this.queryParams).then(response => {\r\n        this.volaTaskList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.currentStep = 0;\r\n      this.transferValue = [];\r\n      this.reset();\r\n    },\r\n    // 下一步\r\n    nextStep() {\r\n      if (this.currentStep === 0) {\r\n        // 验证第一步表单\r\n        this.$refs[\"form\"].validate(valid => {\r\n          if (valid) {\r\n            this.currentStep = 1;\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 上一步\r\n    prevStep() {\r\n      if (this.currentStep > 0) {\r\n        this.currentStep--;\r\n      }\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.currentStep = 0;\r\n      this.transferValue = [];\r\n      this.form = {\r\n        volaTaskInfoId: null,\r\n        volaTaskInfoName: null,\r\n        medinsInfo: null,\r\n        medinsLv: null,\r\n        medType: null,\r\n        setlTimeStart: null,\r\n        setlTimeEnd: null,\r\n        ruleIds: null,\r\n        ruleNames: null,\r\n        taskTime: null,\r\n        delFlag: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.volaTaskInfoId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加调度任务信息\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const volaTaskInfoId = row.volaTaskInfoId || this.ids\r\n      getVolaTask(volaTaskInfoId).then(response => {\r\n        this.form = response.data;\r\n        // 回显穿梭框数据\r\n        if (this.form.ruleIds) {\r\n          this.transferValue = this.form.ruleIds.split(',');\r\n        }\r\n        this.open = true;\r\n        this.title = \"修改调度任务信息\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      // 处理穿梭框选中的规则\r\n      if (this.transferValue.length === 0) {\r\n        this.$modal.msgWarning(\"请至少选择一个检查规则\");\r\n        return;\r\n      }\r\n\r\n      // 将选中的规则ID和名称设置到表单中\r\n      this.form.ruleIds = this.transferValue.join(',');\r\n      const selectedRuleNames = this.allRules\r\n        .filter(rule => this.transferValue.includes(rule.key))\r\n        .map(rule => rule.label)\r\n        .join(',');\r\n      this.form.ruleNames = selectedRuleNames;\r\n\r\n      // 设置当前时间为建立任务时间\r\n      this.form.taskTime = new Date().toISOString().split('T')[0];\r\n\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.volaTaskInfoId != null) {\r\n            updateVolaTask(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.currentStep = 0;\r\n              this.transferValue = [];\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addVolaTask(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.currentStep = 0;\r\n              this.transferValue = [];\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const volaTaskInfoIds = row.volaTaskInfoId || this.ids;\r\n      this.$modal.confirm('是否确认删除调度任务信息编号为\"' + volaTaskInfoIds + '\"的数据项？').then(function() {\r\n        return delVolaTask(volaTaskInfoIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('dataAnalysis/volaTask/export', {\r\n        ...this.queryParams\r\n      }, `volaTask_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 表格自适应样式 */\r\n.el-table {\r\n  width: 100%;\r\n}\r\n\r\n/* 确保表格在小屏幕上的显示 */\r\n@media (max-width: 768px) {\r\n  .el-table {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .el-table .cell {\r\n    padding-left: 5px;\r\n    padding-right: 5px;\r\n  }\r\n}\r\n\r\n/* 表格行高优化 */\r\n.el-table .el-table__row {\r\n  height: auto;\r\n}\r\n\r\n/* 操作按钮样式优化 */\r\n.el-table .el-button--mini {\r\n  margin: 0 2px;\r\n}\r\n\r\n/* 步骤表单样式 */\r\n.el-steps {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.el-transfer {\r\n  text-align: center;\r\n}\r\n\r\n.el-transfer-panel {\r\n  width: 300px;\r\n}\r\n\r\n/* 对话框内容区域样式 */\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n  margin-left: 10px;\r\n}\r\n\r\n/* 表单布局优化 */\r\n.el-form .el-row {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.el-form .el-form-item {\r\n  margin-bottom: 20px;\r\n}\r\n</style>\r\n"]}]}