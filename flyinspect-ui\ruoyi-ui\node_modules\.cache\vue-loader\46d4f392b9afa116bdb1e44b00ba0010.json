{"remainingRequest": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\dataAnalysis\\volaTask\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\dataAnalysis\\volaTask\\index.vue", "mtime": 1750391922086}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0Vm9sYVRhc2ssIGdldFZvbGFUYXNrLCBkZWxWb2xhVGFzaywgYWRkVm9sYVRhc2ssIHVwZGF0ZVZvbGFUYXNrIH0gZnJvbSAiQC9hcGkvZGF0YUFuYWx5c2lzL3ZvbGFUYXNrIjsNCmltcG9ydCByZXF1ZXN0IGZyb20gJ0AvdXRpbHMvcmVxdWVzdCc7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlZvbGFUYXNrIiwNCiAgZGljdHM6IFsnbGVnZW50X2x2J10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDosIPluqbku7vliqHkv6Hmga/ooajmoLzmlbDmja4NCiAgICAgIHZvbGFUYXNrTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDlvZPliY3mraXpqqQNCiAgICAgIGN1cnJlbnRTdGVwOiAwLA0KICAgICAgLy8g56m/5qKt5qGG5pWw5o2uDQogICAgICB0cmFuc2ZlckRhdGE6IFtdLA0KICAgICAgLy8g56m/5qKt5qGG6YCJ5Lit55qE5YC8DQogICAgICB0cmFuc2ZlclZhbHVlOiBbXSwNCiAgICAgIC8vIOaJgOacieWPr+mAieinhOWImeWIl+ihqA0KICAgICAgYWxsUnVsZXM6IFsNCiAgICAgICAgeyBrZXk6ICcxJywgbGFiZWw6ICfop4TliJkx77ya5Yy75L+d55uu5b2V5aSW55So6I2v5qOA5p+lJyB9LA0KICAgICAgICB7IGtleTogJzInLCBsYWJlbDogJ+inhOWImTLvvJrotoXph4/nlKjoja/mo4Dmn6UnIH0sDQogICAgICAgIHsga2V5OiAnMycsIGxhYmVsOiAn6KeE5YiZM++8mumHjeWkjeeUqOiNr+ajgOafpScgfSwNCiAgICAgICAgeyBrZXk6ICc0JywgbGFiZWw6ICfop4TliJk077ya6YWN5LyN56aB5b+M5qOA5p+lJyB9LA0KICAgICAgICB7IGtleTogJzUnLCBsYWJlbDogJ+inhOWImTXvvJrpgILlupTnl4fmo4Dmn6UnIH0sDQogICAgICAgIHsga2V5OiAnNicsIGxhYmVsOiAn6KeE5YiZNu+8muiviueWl+mhueebruWQiOeQhuaAp+ajgOafpScgfSwNCiAgICAgICAgeyBrZXk6ICc3JywgbGFiZWw6ICfop4TliJk377ya5Yy755aX5pyN5Yqh5Lu35qC85qOA5p+lJyB9LA0KICAgICAgICB7IGtleTogJzgnLCBsYWJlbDogJ+inhOWImTjvvJrkvY/pmaLlpKnmlbDlkIjnkIbmgKfmo4Dmn6UnIH0NCiAgICAgIF0sDQogICAgICAvLyDljLvnlpfmnLrmnoTliJfooagNCiAgICAgIG1lZGljYWxJbnN0aXR1dGlvbkxpc3Q6IFtdLA0KICAgICAgLy8g5Yy755aX5py65p6E5YiX6KGo5Yqg6L2954q25oCBDQogICAgICBtZWRMaXN0TG9hZGluZzogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgdm9sYVRhc2tJbmZvTmFtZTogbnVsbCwNCiAgICAgICAgbWVkaW5zSW5mbzogbnVsbCwNCiAgICAgICAgbWVkaW5zTHY6IG51bGwsDQogICAgICAgIG1lZFR5cGU6IG51bGwsDQogICAgICAgIHNldGxUaW1lU3RhcnQ6IG51bGwsDQogICAgICAgIHNldGxUaW1lRW5kOiBudWxsLA0KICAgICAgICBydWxlSWRzOiBudWxsLA0KICAgICAgICBydWxlTmFtZXM6IG51bGwsDQogICAgICAgIHRhc2tUaW1lOiBudWxsLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIHZvbGFUYXNrSW5mb05hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6LCD5bqm5Lu75Yqh5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgbWVkaW5zSW5mbzogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnrZvmn6XljLvnlpfmnLrmnoTpm4blkIjkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBzZXR0bGVtZW50VGltZVJhbmdlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIue7k+eul+aXtumXtOiMg+WbtOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0NCiAgICAgICAgXSwNCiAgICAgICAgLy8g5rOo5oSP77yacnVsZUlkc+OAgXJ1bGVOYW1lc+OAgXRhc2tUaW1lIOeUseepv+aireahhuWSjOezu+e7n+iHquWKqOeUn+aIkO+8jOS4jemcgOimgeWcqOesrOS4gOatpemqjOivgQ0KICAgICAgICAvLyBkZWxGbGFn44CBY3JlYXRlQnnjgIFjcmVhdGVUaW1l44CBdXBkYXRlQnnjgIF1cGRhdGVUaW1lIOeUseezu+e7n+iHquWKqOWkhOeQhu+8jOS4jemcgOimgemqjOivgQ0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i6LCD5bqm5Lu75Yqh5L+h5oGv5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0Vm9sYVRhc2sodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMudm9sYVRhc2tMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLmN1cnJlbnRTdGVwID0gMDsNCiAgICAgIHRoaXMudHJhbnNmZXJWYWx1ZSA9IFtdOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g5LiL5LiA5q2lDQogICAgbmV4dFN0ZXAoKSB7DQogICAgICBpZiAodGhpcy5jdXJyZW50U3RlcCA9PT0gMCkgew0KICAgICAgICAvLyDpqozor4HnrKzkuIDmraXooajljZUNCiAgICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICAgIHRoaXMuY3VycmVudFN0ZXAgPSAxOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDkuIrkuIDmraUNCiAgICBwcmV2U3RlcCgpIHsNCiAgICAgIGlmICh0aGlzLmN1cnJlbnRTdGVwID4gMCkgew0KICAgICAgICB0aGlzLmN1cnJlbnRTdGVwLS07DQogICAgICB9DQogICAgfSwNCiAgICAvLyDojrflj5bop4TliJnlkI3np7ANCiAgICBnZXRSdWxlTmFtZShydWxlSWQpIHsNCiAgICAgIGNvbnN0IHJ1bGUgPSB0aGlzLmFsbFJ1bGVzLmZpbmQociA9PiByLmtleSA9PT0gcnVsZUlkKTsNCiAgICAgIHJldHVybiBydWxlID8gcnVsZS5sYWJlbCA6ICcnOw0KICAgIH0sDQogICAgLy8g6I635Y+W5Yy755aX5py65p6E5YiX6KGoDQogICAgZ2V0TWVkaWNhbEluc3RpdHV0aW9uTGlzdCgpIHsNCiAgICAgIGlmICh0aGlzLm1lZGljYWxJbnN0aXR1dGlvbkxpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICByZXR1cm47IC8vIOWmguaenOW3sue7j+acieaVsOaNru+8jOS4jemHjeWkjeivt+axgg0KICAgICAgfQ0KICAgICAgdGhpcy5tZWRMaXN0TG9hZGluZyA9IHRydWU7DQogICAgICByZXF1ZXN0KHsNCiAgICAgICAgdXJsOiAnL2RhdGFBbmFseXNpcy92b2xhVGFzay9tZWRMaXN0JywNCiAgICAgICAgbWV0aG9kOiAnZ2V0Jw0KICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLm1lZGljYWxJbnN0aXR1dGlvbkxpc3QgPSByZXNwb25zZS5kYXRhIHx8IFtdOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKHJlc3BvbnNlLm1zZyB8fCAn6I635Y+W5Yy755aX5py65p6E5YiX6KGo5aSx6LSlJyk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5Yy755aX5py65p6E5YiX6KGo5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+iOt+WPluWMu+eWl+acuuaehOWIl+ihqOWksei0pScpOw0KICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgIHRoaXMubWVkTGlzdExvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5pCc57Si5Yy755aX5py65p6EDQogICAgc2VhcmNoTWVkaWNhbEluc3RpdHV0aW9ucyhxdWVyeSkgew0KICAgICAgaWYgKHF1ZXJ5ICE9PSAnJykgew0KICAgICAgICB0aGlzLm1lZExpc3RMb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgcmVxdWVzdCh7DQogICAgICAgICAgdXJsOiAnL2RhdGFBbmFseXNpcy92b2xhVGFzay9tZWRMaXN0JywNCiAgICAgICAgICBtZXRob2Q6ICdnZXQnLA0KICAgICAgICAgIHBhcmFtczogeyBrZXl3b3JkOiBxdWVyeSB9DQogICAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMubWVkaWNhbEluc3RpdHV0aW9uTGlzdCA9IHJlc3BvbnNlLmRhdGEgfHwgW107DQogICAgICAgICAgfQ0KICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign5pCc57Si5Yy755aX5py65p6E5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgICAgdGhpcy5tZWRMaXN0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZ2V0TWVkaWNhbEluc3RpdHV0aW9uTGlzdCgpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmN1cnJlbnRTdGVwID0gMDsNCiAgICAgIHRoaXMudHJhbnNmZXJWYWx1ZSA9IFtdOw0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICB2b2xhVGFza0luZm9JZDogbnVsbCwNCiAgICAgICAgdm9sYVRhc2tJbmZvTmFtZTogbnVsbCwNCiAgICAgICAgbWVkaW5zSW5mbzogW10sIC8vIOaUueS4uuaVsOe7hOexu+Wei++8jOaUr+aMgeWkmumAiQ0KICAgICAgICBtZWRpbnNMdjogbnVsbCwNCiAgICAgICAgbWVkVHlwZTogbnVsbCwNCiAgICAgICAgc2V0bFRpbWVTdGFydDogbnVsbCwNCiAgICAgICAgc2V0bFRpbWVFbmQ6IG51bGwsDQogICAgICAgIHNldHRsZW1lbnRUaW1lUmFuZ2U6IFtdLCAvLyDmlrDlop7ml7bpl7TojIPlm7TlrZfmrrUNCiAgICAgICAgcnVsZUlkczogbnVsbCwNCiAgICAgICAgcnVsZU5hbWVzOiBudWxsLA0KICAgICAgICB0YXNrVGltZTogbnVsbCwNCiAgICAgICAgZGVsRmxhZzogbnVsbCwNCiAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICB1cGRhdGVUaW1lOiBudWxsLA0KICAgICAgICByZW1hcms6IG51bGwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLnZvbGFUYXNrSW5mb0lkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6LCD5bqm5Lu75Yqh5L+h5oGvIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCB2b2xhVGFza0luZm9JZCA9IHJvdy52b2xhVGFza0luZm9JZCB8fCB0aGlzLmlkcw0KICAgICAgZ2V0Vm9sYVRhc2sodm9sYVRhc2tJbmZvSWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAvLyDlm57mmL7nqb/moq3moYbmlbDmja4NCiAgICAgICAgaWYgKHRoaXMuZm9ybS5ydWxlSWRzKSB7DQogICAgICAgICAgdGhpcy50cmFuc2ZlclZhbHVlID0gdGhpcy5mb3JtLnJ1bGVJZHMuc3BsaXQoJywnKTsNCiAgICAgICAgfQ0KICAgICAgICAvLyDlm57mmL7ljLvnlpfmnLrmnoTmlbDmja4NCiAgICAgICAgaWYgKHRoaXMuZm9ybS5tZWRpbnNJbmZvKSB7DQogICAgICAgICAgLy8g5aaC5p6c5piv5a2X56ym5Liy77yM6L2s5o2i5Li65pWw57uEDQogICAgICAgICAgaWYgKHR5cGVvZiB0aGlzLmZvcm0ubWVkaW5zSW5mbyA9PT0gJ3N0cmluZycpIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5tZWRpbnNJbmZvID0gdGhpcy5mb3JtLm1lZGluc0luZm8uc3BsaXQoJywnKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5mb3JtLm1lZGluc0luZm8gPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICAvLyDlm57mmL7ml7bpl7TojIPlm7TmlbDmja4NCiAgICAgICAgaWYgKHRoaXMuZm9ybS5zZXRsVGltZVN0YXJ0ICYmIHRoaXMuZm9ybS5zZXRsVGltZUVuZCkgew0KICAgICAgICAgIHRoaXMuZm9ybS5zZXR0bGVtZW50VGltZVJhbmdlID0gW3RoaXMuZm9ybS5zZXRsVGltZVN0YXJ0LCB0aGlzLmZvcm0uc2V0bFRpbWVFbmRdOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuZm9ybS5zZXR0bGVtZW50VGltZVJhbmdlID0gW107DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnosIPluqbku7vliqHkv6Hmga8iOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIC8vIOWkhOeQhuepv+aireahhumAieS4reeahOinhOWImQ0KICAgICAgaWYgKHRoaXMudHJhbnNmZXJWYWx1ZS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnV2FybmluZygi6K+36Iez5bCR6YCJ5oup5LiA5Liq5qOA5p+l6KeE5YiZIik7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5aSE55CG5Yy755aX5py65p6E5pWw5o2uDQogICAgICBpZiAoIXRoaXMuZm9ybS5tZWRpbnNJbmZvIHx8IHRoaXMuZm9ybS5tZWRpbnNJbmZvLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dXYXJuaW5nKCLor7foh7PlsJHpgInmi6nkuIDkuKrljLvnlpfmnLrmnoQiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDlpITnkIbml7bpl7TojIPlm7TmlbDmja4NCiAgICAgIGlmICghdGhpcy5mb3JtLnNldHRsZW1lbnRUaW1lUmFuZ2UgfHwgdGhpcy5mb3JtLnNldHRsZW1lbnRUaW1lUmFuZ2UubGVuZ3RoICE9PSAyKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1dhcm5pbmcoIuivt+mAieaLqee7k+eul+aXtumXtOiMg+WbtCIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWIm+W7uuaPkOS6pOaVsOaNrueahOWJr+acrA0KICAgICAgY29uc3Qgc3VibWl0RGF0YSA9IHsgLi4udGhpcy5mb3JtIH07DQoNCiAgICAgIC8vIOWwhuWMu+eWl+acuuaehOaVsOe7hOi9rOaNouS4uumAl+WPt+WIhumalOeahOWtl+espuS4sg0KICAgICAgaWYgKEFycmF5LmlzQXJyYXkoc3VibWl0RGF0YS5tZWRpbnNJbmZvKSkgew0KICAgICAgICBzdWJtaXREYXRhLm1lZGluc0luZm8gPSBzdWJtaXREYXRhLm1lZGluc0luZm8uam9pbignLCcpOw0KICAgICAgfQ0KDQogICAgICAvLyDlpITnkIbml7bpl7TojIPlm7TmlbDmja4NCiAgICAgIGlmIChzdWJtaXREYXRhLnNldHRsZW1lbnRUaW1lUmFuZ2UgJiYgc3VibWl0RGF0YS5zZXR0bGVtZW50VGltZVJhbmdlLmxlbmd0aCA9PT0gMikgew0KICAgICAgICBzdWJtaXREYXRhLnNldGxUaW1lU3RhcnQgPSBzdWJtaXREYXRhLnNldHRsZW1lbnRUaW1lUmFuZ2VbMF07DQogICAgICAgIHN1Ym1pdERhdGEuc2V0bFRpbWVFbmQgPSBzdWJtaXREYXRhLnNldHRsZW1lbnRUaW1lUmFuZ2VbMV07DQogICAgICB9DQogICAgICAvLyDnp7vpmaTml7bpl7TojIPlm7TlrZfmrrXvvIzlm6DkuLrlkI7nq6/kuI3pnIDopoHov5nkuKrlrZfmrrUNCiAgICAgIGRlbGV0ZSBzdWJtaXREYXRhLnNldHRsZW1lbnRUaW1lUmFuZ2U7DQoNCiAgICAgIC8vIOWwhumAieS4reeahOinhOWImUlE5ZKM5ZCN56ew6K6+572u5Yiw6KGo5Y2V5LitDQogICAgICBzdWJtaXREYXRhLnJ1bGVJZHMgPSB0aGlzLnRyYW5zZmVyVmFsdWUuam9pbignLCcpOw0KICAgICAgY29uc3Qgc2VsZWN0ZWRSdWxlTmFtZXMgPSB0aGlzLmFsbFJ1bGVzDQogICAgICAgIC5maWx0ZXIocnVsZSA9PiB0aGlzLnRyYW5zZmVyVmFsdWUuaW5jbHVkZXMocnVsZS5rZXkpKQ0KICAgICAgICAubWFwKHJ1bGUgPT4gcnVsZS5sYWJlbCkNCiAgICAgICAgLmpvaW4oJywnKTsNCiAgICAgIHN1Ym1pdERhdGEucnVsZU5hbWVzID0gc2VsZWN0ZWRSdWxlTmFtZXM7DQoNCiAgICAgIC8vIOiuvue9ruW9k+WJjeaXtumXtOS4uuW7uueri+S7u+WKoeaXtumXtA0KICAgICAgc3VibWl0RGF0YS50YXNrVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdOw0KDQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAoc3VibWl0RGF0YS52b2xhVGFza0luZm9JZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVWb2xhVGFzayhzdWJtaXREYXRhKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRTdGVwID0gMDsNCiAgICAgICAgICAgICAgdGhpcy50cmFuc2ZlclZhbHVlID0gW107DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZFZvbGFUYXNrKHN1Ym1pdERhdGEpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuY3VycmVudFN0ZXAgPSAwOw0KICAgICAgICAgICAgICB0aGlzLnRyYW5zZmVyVmFsdWUgPSBbXTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IHZvbGFUYXNrSW5mb0lkcyA9IHJvdy52b2xhVGFza0luZm9JZCB8fCB0aGlzLmlkczsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOiwg+W6puS7u+WKoeS/oeaBr+e8luWPt+S4uiInICsgdm9sYVRhc2tJbmZvSWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gZGVsVm9sYVRhc2sodm9sYVRhc2tJbmZvSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnZGF0YUFuYWx5c2lzL3ZvbGFUYXNrL2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYHZvbGFUYXNrXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6ZA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dataAnalysis/volaTask", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\r\n      <el-form-item label=\"调度任务名称\" prop=\"volaTaskInfoName\">\r\n        <el-input\r\n          v-model=\"queryParams.volaTaskInfoName\"\r\n          placeholder=\"请输入调度任务名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"医疗机构等级\" prop=\"medinsLv\">\r\n        <el-input\r\n          v-model=\"queryParams.medinsLv\"\r\n          placeholder=\"请输入医疗机构等级\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"结算开始时间\" prop=\"setlTimeStart\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.setlTimeStart\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择结算开始时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"结算结束时间\" prop=\"setlTimeEnd\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.setlTimeEnd\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择结算结束时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"规则ID集合\" prop=\"ruleIds\">\r\n        <el-input\r\n          v-model=\"queryParams.ruleIds\"\r\n          placeholder=\"请输入规则ID集合\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"建立任务时间\" prop=\"taskTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.taskTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择建立任务时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"volaTaskList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      fit\r\n      table-layout=\"auto\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column\r\n        label=\"调度任务名称\"\r\n        align=\"center\"\r\n        prop=\"volaTaskInfoName\"\r\n        min-width=\"150\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"筛查医疗机构集合\"\r\n        align=\"center\"\r\n        prop=\"medinsInfo\"\r\n        min-width=\"180\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"医疗机构等级\"\r\n        align=\"center\"\r\n        prop=\"medinsLv\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"医疗类别\"\r\n        align=\"center\"\r\n        prop=\"medType\"\r\n        width=\"100\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"结算开始时间\"\r\n        align=\"center\"\r\n        prop=\"setlTimeStart\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.setlTimeStart, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"结算结束时间\"\r\n        align=\"center\"\r\n        prop=\"setlTimeEnd\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.setlTimeEnd, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"规则ID集合\"\r\n        align=\"center\"\r\n        prop=\"ruleIds\"\r\n        min-width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"规则名称集合\"\r\n        align=\"center\"\r\n        prop=\"ruleNames\"\r\n        min-width=\"150\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"建立任务时间\"\r\n        align=\"center\"\r\n        prop=\"taskTime\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.taskTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"备注\"\r\n        align=\"center\"\r\n        prop=\"remark\"\r\n        min-width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        width=\"120\"\r\n        fixed=\"right\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['dataAnalysis:volaTask:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['dataAnalysis:volaTask:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改调度任务信息对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"1400px\" append-to-body>\r\n      <!-- 步骤条 -->\r\n      <el-steps\r\n        :active=\"currentStep\"\r\n        finish-status=\"success\"\r\n        align-center\r\n        class=\"custom-steps\">\r\n        <el-step title=\"基本信息\" description=\"填写调度任务基本信息\">\r\n          <i slot=\"icon\" class=\"el-icon-edit-outline\"></i>\r\n        </el-step>\r\n        <el-step title=\"规则选择\" description=\"选择检查规则\">\r\n          <i slot=\"icon\" class=\"el-icon-setting\"></i>\r\n        </el-step>\r\n      </el-steps>\r\n\r\n      <!-- 第一步：基本信息 -->\r\n      <div v-show=\"currentStep === 0\" class=\"step-content\">\r\n        <div class=\"step-header\">\r\n          <h3><i class=\"el-icon-edit-outline\"></i> 基本信息</h3>\r\n          <p>请填写调度任务的基本信息</p>\r\n        </div>\r\n\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"140px\" class=\"step-form\">\r\n          <div class=\"form-section\">\r\n            <div class=\"section-title\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>任务信息</span>\r\n            </div>\r\n            <el-row :gutter=\"24\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"调度任务名称\" prop=\"volaTaskInfoName\">\r\n                  <el-input\r\n                    v-model=\"form.volaTaskInfoName\"\r\n                    placeholder=\"请输入调度任务名称\"\r\n                    prefix-icon=\"el-icon-edit\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"医疗机构等级\" prop=\"medinsLv\">\r\n                  <el-select\r\n                    v-model=\"form.medinsLv\"\r\n                    placeholder=\"请选择医疗机构等级\"\r\n                    clearable\r\n                    style=\"width: 100%\">\r\n                    <el-option\r\n                      v-for=\"dict in dict.type.legent_lv\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.label\"\r\n                      :value=\"dict.value\">\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n\r\n            <el-form-item label=\"筛查医疗机构集合\" prop=\"medinsInfo\">\r\n              <el-select\r\n                v-model=\"form.medinsInfo\"\r\n                multiple\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                placeholder=\"请选择筛查医疗机构\"\r\n                :remote-method=\"searchMedicalInstitutions\"\r\n                :loading=\"medListLoading\"\r\n                style=\"width: 100%\"\r\n                @focus=\"getMedicalInstitutionList\">\r\n                <el-option\r\n                  v-for=\"item in medicalInstitutionList\"\r\n                  :key=\"item.fixmedinsCode\"\r\n                  :label=\"item.fixmedinsName\"\r\n                  :value=\"item.fixmedinsCode\">\r\n                  <span style=\"float: left\">{{ item.fixmedinsName }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ item.fixmedinsCode }}</span>\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <div class=\"form-section\">\r\n            <div class=\"section-title\">\r\n              <i class=\"el-icon-date\"></i>\r\n              <span>结算时间</span>\r\n            </div>\r\n            <el-form-item label=\"结算时间范围\" prop=\"settlementTimeRange\">\r\n              <el-date-picker\r\n                v-model=\"form.settlementTimeRange\"\r\n                type=\"datetimerange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期时间\"\r\n                end-placeholder=\"结束日期时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                format=\"yyyy-MM-dd HH:mm:ss\"\r\n                :default-time=\"['00:00:00', '23:59:59']\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 第二步：规则选择 -->\r\n      <div v-show=\"currentStep === 1\" class=\"step-content\">\r\n        <div class=\"step-header\">\r\n          <h3><i class=\"el-icon-setting\"></i> 规则选择</h3>\r\n          <p>请选择需要应用的检查规则</p>\r\n        </div>\r\n\r\n        <div class=\"transfer-container\">\r\n          <div class=\"transfer-tips\">\r\n            <el-alert\r\n              title=\"操作提示\"\r\n              type=\"info\"\r\n              :closable=\"false\"\r\n              show-icon>\r\n              <div slot=\"description\">\r\n                <p>• 从左侧选择需要的检查规则，点击右箭头添加到已选规则</p>\r\n                <p>• 支持搜索功能，可快速定位所需规则</p>\r\n                <p>• 至少需要选择一个规则才能完成创建</p>\r\n              </div>\r\n            </el-alert>\r\n          </div>\r\n\r\n          <div class=\"transfer-wrapper\">\r\n            <el-transfer\r\n              v-model=\"transferValue\"\r\n              :data=\"allRules\"\r\n              :titles=\"['可选规则', '已选规则']\"\r\n              :button-texts=\"['移除', '添加']\"\r\n              :format=\"{\r\n                noChecked: '共 ${total} 项',\r\n                hasChecked: '已选 ${checked}/${total} 项'\r\n              }\"\r\n              filterable\r\n              filter-placeholder=\"搜索规则名称\"\r\n              :props=\"{\r\n                key: 'key',\r\n                label: 'label'\r\n              }\"\r\n              class=\"enhanced-transfer\">\r\n              <template slot-scope=\"{ option }\">\r\n                <div class=\"transfer-item\">\r\n                  <div class=\"item-icon\">\r\n                    <i class=\"el-icon-document-checked\"></i>\r\n                  </div>\r\n                  <div class=\"item-content\">\r\n                    <div class=\"item-title\">{{ option.label.split('：')[0] }}</div>\r\n                    <div class=\"item-description\">{{ option.label.split('：')[1] || '' }}</div>\r\n                  </div>\r\n                </div>\r\n              </template>\r\n            </el-transfer>\r\n          </div>\r\n\r\n          <div class=\"selected-summary\" v-if=\"transferValue.length > 0\">\r\n            <div class=\"summary-header\">\r\n              <i class=\"el-icon-success\"></i>\r\n              <span>已选择 {{ transferValue.length }} 个规则</span>\r\n            </div>\r\n            <div class=\"summary-tags\">\r\n              <el-tag\r\n                v-for=\"ruleId in transferValue\"\r\n                :key=\"ruleId\"\r\n                type=\"success\"\r\n                size=\"small\"\r\n                style=\"margin: 2px 4px 2px 0;\">\r\n                {{ getRuleName(ruleId) }}\r\n              </el-tag>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n        <el-button v-if=\"currentStep > 0\" @click=\"prevStep\">上一步</el-button>\r\n        <el-button v-if=\"currentStep < 1\" type=\"primary\" @click=\"nextStep\">下一步</el-button>\r\n        <el-button v-if=\"currentStep === 1\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listVolaTask, getVolaTask, delVolaTask, addVolaTask, updateVolaTask } from \"@/api/dataAnalysis/volaTask\";\r\nimport request from '@/utils/request';\r\n\r\nexport default {\r\n  name: \"VolaTask\",\r\n  dicts: ['legent_lv'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 调度任务信息表格数据\r\n      volaTaskList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 当前步骤\r\n      currentStep: 0,\r\n      // 穿梭框数据\r\n      transferData: [],\r\n      // 穿梭框选中的值\r\n      transferValue: [],\r\n      // 所有可选规则列表\r\n      allRules: [\r\n        { key: '1', label: '规则1：医保目录外用药检查' },\r\n        { key: '2', label: '规则2：超量用药检查' },\r\n        { key: '3', label: '规则3：重复用药检查' },\r\n        { key: '4', label: '规则4：配伍禁忌检查' },\r\n        { key: '5', label: '规则5：适应症检查' },\r\n        { key: '6', label: '规则6：诊疗项目合理性检查' },\r\n        { key: '7', label: '规则7：医疗服务价格检查' },\r\n        { key: '8', label: '规则8：住院天数合理性检查' }\r\n      ],\r\n      // 医疗机构列表\r\n      medicalInstitutionList: [],\r\n      // 医疗机构列表加载状态\r\n      medListLoading: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        volaTaskInfoName: null,\r\n        medinsInfo: null,\r\n        medinsLv: null,\r\n        medType: null,\r\n        setlTimeStart: null,\r\n        setlTimeEnd: null,\r\n        ruleIds: null,\r\n        ruleNames: null,\r\n        taskTime: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        volaTaskInfoName: [\r\n          { required: true, message: \"调度任务名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        medinsInfo: [\r\n          { required: true, message: \"筛查医疗机构集合不能为空\", trigger: \"blur\" }\r\n        ],\r\n        settlementTimeRange: [\r\n          { required: true, message: \"结算时间范围不能为空\", trigger: \"change\" }\r\n        ],\r\n        // 注意：ruleIds、ruleNames、taskTime 由穿梭框和系统自动生成，不需要在第一步验证\r\n        // delFlag、createBy、createTime、updateBy、updateTime 由系统自动处理，不需要验证\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询调度任务信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listVolaTask(this.queryParams).then(response => {\r\n        this.volaTaskList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.currentStep = 0;\r\n      this.transferValue = [];\r\n      this.reset();\r\n    },\r\n    // 下一步\r\n    nextStep() {\r\n      if (this.currentStep === 0) {\r\n        // 验证第一步表单\r\n        this.$refs[\"form\"].validate(valid => {\r\n          if (valid) {\r\n            this.currentStep = 1;\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 上一步\r\n    prevStep() {\r\n      if (this.currentStep > 0) {\r\n        this.currentStep--;\r\n      }\r\n    },\r\n    // 获取规则名称\r\n    getRuleName(ruleId) {\r\n      const rule = this.allRules.find(r => r.key === ruleId);\r\n      return rule ? rule.label : '';\r\n    },\r\n    // 获取医疗机构列表\r\n    getMedicalInstitutionList() {\r\n      if (this.medicalInstitutionList.length > 0) {\r\n        return; // 如果已经有数据，不重复请求\r\n      }\r\n      this.medListLoading = true;\r\n      request({\r\n        url: '/dataAnalysis/volaTask/medList',\r\n        method: 'get'\r\n      }).then(response => {\r\n        if (response.code === 200) {\r\n          this.medicalInstitutionList = response.data || [];\r\n        } else {\r\n          this.$modal.msgError(response.msg || '获取医疗机构列表失败');\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取医疗机构列表失败:', error);\r\n        this.$modal.msgError('获取医疗机构列表失败');\r\n      }).finally(() => {\r\n        this.medListLoading = false;\r\n      });\r\n    },\r\n    // 搜索医疗机构\r\n    searchMedicalInstitutions(query) {\r\n      if (query !== '') {\r\n        this.medListLoading = true;\r\n        request({\r\n          url: '/dataAnalysis/volaTask/medList',\r\n          method: 'get',\r\n          params: { keyword: query }\r\n        }).then(response => {\r\n          if (response.code === 200) {\r\n            this.medicalInstitutionList = response.data || [];\r\n          }\r\n        }).catch(error => {\r\n          console.error('搜索医疗机构失败:', error);\r\n        }).finally(() => {\r\n          this.medListLoading = false;\r\n        });\r\n      } else {\r\n        this.getMedicalInstitutionList();\r\n      }\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.currentStep = 0;\r\n      this.transferValue = [];\r\n      this.form = {\r\n        volaTaskInfoId: null,\r\n        volaTaskInfoName: null,\r\n        medinsInfo: [], // 改为数组类型，支持多选\r\n        medinsLv: null,\r\n        medType: null,\r\n        setlTimeStart: null,\r\n        setlTimeEnd: null,\r\n        settlementTimeRange: [], // 新增时间范围字段\r\n        ruleIds: null,\r\n        ruleNames: null,\r\n        taskTime: null,\r\n        delFlag: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.volaTaskInfoId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加调度任务信息\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const volaTaskInfoId = row.volaTaskInfoId || this.ids\r\n      getVolaTask(volaTaskInfoId).then(response => {\r\n        this.form = response.data;\r\n        // 回显穿梭框数据\r\n        if (this.form.ruleIds) {\r\n          this.transferValue = this.form.ruleIds.split(',');\r\n        }\r\n        // 回显医疗机构数据\r\n        if (this.form.medinsInfo) {\r\n          // 如果是字符串，转换为数组\r\n          if (typeof this.form.medinsInfo === 'string') {\r\n            this.form.medinsInfo = this.form.medinsInfo.split(',');\r\n          }\r\n        } else {\r\n          this.form.medinsInfo = [];\r\n        }\r\n        // 回显时间范围数据\r\n        if (this.form.setlTimeStart && this.form.setlTimeEnd) {\r\n          this.form.settlementTimeRange = [this.form.setlTimeStart, this.form.setlTimeEnd];\r\n        } else {\r\n          this.form.settlementTimeRange = [];\r\n        }\r\n        this.open = true;\r\n        this.title = \"修改调度任务信息\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      // 处理穿梭框选中的规则\r\n      if (this.transferValue.length === 0) {\r\n        this.$modal.msgWarning(\"请至少选择一个检查规则\");\r\n        return;\r\n      }\r\n\r\n      // 处理医疗机构数据\r\n      if (!this.form.medinsInfo || this.form.medinsInfo.length === 0) {\r\n        this.$modal.msgWarning(\"请至少选择一个医疗机构\");\r\n        return;\r\n      }\r\n\r\n      // 处理时间范围数据\r\n      if (!this.form.settlementTimeRange || this.form.settlementTimeRange.length !== 2) {\r\n        this.$modal.msgWarning(\"请选择结算时间范围\");\r\n        return;\r\n      }\r\n\r\n      // 创建提交数据的副本\r\n      const submitData = { ...this.form };\r\n\r\n      // 将医疗机构数组转换为逗号分隔的字符串\r\n      if (Array.isArray(submitData.medinsInfo)) {\r\n        submitData.medinsInfo = submitData.medinsInfo.join(',');\r\n      }\r\n\r\n      // 处理时间范围数据\r\n      if (submitData.settlementTimeRange && submitData.settlementTimeRange.length === 2) {\r\n        submitData.setlTimeStart = submitData.settlementTimeRange[0];\r\n        submitData.setlTimeEnd = submitData.settlementTimeRange[1];\r\n      }\r\n      // 移除时间范围字段，因为后端不需要这个字段\r\n      delete submitData.settlementTimeRange;\r\n\r\n      // 将选中的规则ID和名称设置到表单中\r\n      submitData.ruleIds = this.transferValue.join(',');\r\n      const selectedRuleNames = this.allRules\r\n        .filter(rule => this.transferValue.includes(rule.key))\r\n        .map(rule => rule.label)\r\n        .join(',');\r\n      submitData.ruleNames = selectedRuleNames;\r\n\r\n      // 设置当前时间为建立任务时间\r\n      submitData.taskTime = new Date().toISOString().split('T')[0];\r\n\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (submitData.volaTaskInfoId != null) {\r\n            updateVolaTask(submitData).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.currentStep = 0;\r\n              this.transferValue = [];\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addVolaTask(submitData).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.currentStep = 0;\r\n              this.transferValue = [];\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const volaTaskInfoIds = row.volaTaskInfoId || this.ids;\r\n      this.$modal.confirm('是否确认删除调度任务信息编号为\"' + volaTaskInfoIds + '\"的数据项？').then(function() {\r\n        return delVolaTask(volaTaskInfoIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('dataAnalysis/volaTask/export', {\r\n        ...this.queryParams\r\n      }, `volaTask_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 表格自适应样式 */\r\n.el-table {\r\n  width: 100%;\r\n}\r\n\r\n/* 确保表格在小屏幕上的显示 */\r\n@media (max-width: 768px) {\r\n  .el-table {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .el-table .cell {\r\n    padding-left: 5px;\r\n    padding-right: 5px;\r\n  }\r\n}\r\n\r\n/* 表格行高优化 */\r\n.el-table .el-table__row {\r\n  height: auto;\r\n}\r\n\r\n/* 操作按钮样式优化 */\r\n.el-table .el-button--mini {\r\n  margin: 0 2px;\r\n}\r\n\r\n/* 步骤条样式优化 */\r\n.custom-steps {\r\n  margin-bottom: 40px;\r\n  padding: 0 20px;\r\n}\r\n\r\n.custom-steps .el-step__title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.custom-steps .el-step__description {\r\n  font-size: 13px;\r\n  color: #909399;\r\n}\r\n\r\n/* 步骤内容区域 */\r\n.step-content {\r\n  min-height: 400px;\r\n  padding: 0 20px;\r\n}\r\n\r\n.step-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  padding-bottom: 20px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.step-header h3 {\r\n  font-size: 20px;\r\n  color: #303133;\r\n  margin: 0 0 8px 0;\r\n  font-weight: 600;\r\n}\r\n\r\n.step-header h3 i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n.step-header p {\r\n  color: #606266;\r\n  margin: 0;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.step-form {\r\n  max-width: 100%;\r\n}\r\n\r\n.form-section {\r\n  margin-bottom: 30px;\r\n  background: #fafafa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.section-title i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.step-form .el-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.step-form .el-form-item__label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n/* 穿梭框容器样式 */\r\n.transfer-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.transfer-tips {\r\n  width: 100%;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.transfer-wrapper {\r\n  margin-bottom: 24px;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.enhanced-transfer {\r\n  text-align: center;\r\n}\r\n\r\n.enhanced-transfer .el-transfer-panel {\r\n  width: 480px;\r\n  height: 450px;\r\n  border-radius: 12px;\r\n  border: 2px solid #e4e7ed;\r\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  background: #ffffff;\r\n}\r\n\r\n.enhanced-transfer .el-transfer-panel:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.15);\r\n}\r\n\r\n.enhanced-transfer .el-transfer-panel__header {\r\n  background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);\r\n  color: white;\r\n  border-radius: 10px 10px 0 0;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n  padding: 16px 20px;\r\n  border-bottom: none;\r\n}\r\n\r\n.enhanced-transfer .el-transfer-panel__header .el-checkbox {\r\n  color: white;\r\n}\r\n\r\n.enhanced-transfer .el-transfer-panel__header .el-checkbox__input.is-checked .el-checkbox__inner {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  border-color: white;\r\n}\r\n\r\n.enhanced-transfer .el-transfer-panel__header .el-checkbox__input.is-checked + .el-checkbox__label {\r\n  color: white;\r\n}\r\n\r\n.enhanced-transfer .el-transfer-panel__filter {\r\n  padding: 16px 20px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.enhanced-transfer .el-transfer-panel__filter .el-input__inner {\r\n  border-radius: 20px;\r\n  border: 1px solid #dcdfe6;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.enhanced-transfer .el-transfer-panel__filter .el-input__inner:focus {\r\n  border-color: #409eff;\r\n  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.enhanced-transfer .el-transfer-panel__list {\r\n  height: 320px;\r\n  padding: 8px 0;\r\n}\r\n\r\n.enhanced-transfer .el-transfer-panel__item {\r\n  padding: 0;\r\n  border-bottom: none;\r\n  transition: all 0.3s;\r\n  margin: 4px 12px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.enhanced-transfer .el-transfer-panel__item:hover {\r\n  background: #f0f9ff;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.enhanced-transfer .el-transfer-panel__item.is-checked {\r\n  background: #e6f7ff;\r\n  border: 1px solid #91d5ff;\r\n}\r\n\r\n/* 穿梭框项目样式 */\r\n.transfer-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 12px 16px;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.item-icon {\r\n  margin-right: 12px;\r\n  margin-top: 2px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.item-icon i {\r\n  font-size: 18px;\r\n  color: #409eff;\r\n}\r\n\r\n.item-content {\r\n  flex: 1;\r\n  text-align: left;\r\n  min-width: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n.item-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-bottom: 4px;\r\n  line-height: 1.4;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.item-description {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  line-height: 1.3;\r\n  word-break: break-all;\r\n  white-space: normal;\r\n  overflow: hidden;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n/* 穿梭框按钮样式 */\r\n.enhanced-transfer .el-transfer__buttons {\r\n  padding: 0 30px;\r\n}\r\n\r\n.enhanced-transfer .el-transfer__buttons .el-button {\r\n  border-radius: 20px;\r\n  padding: 10px 20px;\r\n  font-weight: 500;\r\n  transition: all 0.3s;\r\n  min-width: 80px;\r\n}\r\n\r\n.enhanced-transfer .el-transfer__buttons .el-button:first-child {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.enhanced-transfer .el-transfer__buttons .el-button:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);\r\n}\r\n\r\n/* 已选规则摘要 */\r\n.selected-summary {\r\n  width: 100%;\r\n  max-width: 600px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n}\r\n\r\n.summary-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.summary-header i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.summary-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 4px;\r\n}\r\n\r\n/* 对话框底部按钮样式 */\r\n.dialog-footer {\r\n  text-align: right;\r\n  padding: 20px 24px;\r\n  background: #fafafa;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n  margin-left: 12px;\r\n  min-width: 80px;\r\n}\r\n\r\n/* 响应式优化 */\r\n@media (max-width: 768px) {\r\n  .step-content {\r\n    padding: 0 10px;\r\n  }\r\n\r\n  .custom-transfer .el-transfer-panel {\r\n    width: 240px;\r\n    height: 300px;\r\n  }\r\n\r\n  .form-section {\r\n    padding: 15px;\r\n  }\r\n}\r\n</style>\r\n"]}]}