import request from '@/utils/request'

// 查询调度任务信息列表
export function listVolaTask(query) {
  return request({
    url: '/dataAnalysis/volaTask/list',
    method: 'get',
    params: query
  })
}

// 查询调度任务信息详细
export function getVolaTask(volaTaskInfoId) {
  return request({
    url: '/dataAnalysis/volaTask/' + volaTaskInfoId,
    method: 'get'
  })
}

// 新增调度任务信息
export function addVolaTask(data) {
  return request({
    url: '/dataAnalysis/volaTask',
    method: 'post',
    data: data
  })
}

// 修改调度任务信息
export function updateVolaTask(data) {
  return request({
    url: '/dataAnalysis/volaTask',
    method: 'put',
    data: data
  })
}

// 删除调度任务信息
export function delVolaTask(volaTaskInfoId) {
  return request({
    url: '/dataAnalysis/volaTask/' + volaTaskInfoId,
    method: 'delete'
  })
}
