import request from '@/utils/request'

// 查询双随机日志记录列表
export function listExamLog(query) {
  return request({
    url: '/exam/examLog/list',
    method: 'get',
    params: query
  })
}

// 查询双随机日志记录详细
export function getExamLog(examInfoLogId) {
  return request({
    url: '/exam/examLog/' + examInfoLogId,
    method: 'get'
  })
}

// 新增双随机日志记录
export function addExamLog(data) {
  return request({
    url: '/exam/examLog',
    method: 'post',
    data: data
  })
}

// 修改双随机日志记录
export function updateExamLog(data) {
  return request({
    url: '/exam/examLog',
    method: 'put',
    data: data
  })
}

// 删除双随机日志记录
export function delExamLog(examInfoLogId) {
  return request({
    url: '/exam/examLog/' + examInfoLogId,
    method: 'delete'
  })
}
