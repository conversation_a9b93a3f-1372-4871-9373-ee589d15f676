<template>
  <div class="app-container">
    <card>
      <search-form v-show="showSearch" :modelValue="queryParams" :fields="searchFields" :show-search="showSearch"
        @search="handleQuery" @reset="resetQuery" @update:model-value="val => queryParams = val" />
    </card>
    <card class="mt10">
      <el-row :gutter="10" class="mb8" style="display: flex;justify-content: flex-end;">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['exam:eaxmPsn:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['exam:eaxmPsn:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['exam:eaxmPsn:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['exam:eaxmPsn:export']">导出</el-button>
        </el-col>
      </el-row>
      <common-table :loading="loading" :data="eaxmPsnList" :total="total" :columns="columns"
        :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @selection-change="handleSelectionChange"
        @pagination="getList">
        <template #operation="{ row }">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(row)"
            v-hasPermi="['exam:eaxmPsn:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(row)"
            v-hasPermi="['exam:eaxmPsn:remove']">删除</el-button>
        </template>
      </common-table>
    </card>

    <!-- 添加或修改检查人员结果对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="auto" inline>
        <el-form-item label="检查任务id" prop="examTaskId">
          <el-input v-model="form.examTaskId" placeholder="请输入检查任务id" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="检查人员结果" prop="examPsnList">
          <el-input v-model="form.examPsnList" type="textarea" placeholder="请输入内容" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="检查人员性质" prop="examPsnTypeList">
          <el-input v-model="form.examPsnTypeList" type="textarea" placeholder="请输入内容" :style="`width: ${inputWidth}px`" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listEaxmPsn, getEaxmPsn, delEaxmPsn, addEaxmPsn, updateEaxmPsn } from "@/api/exam/eaxmPsn";
import CommonTable from "@/components/CommonTable";
import SearchForm from "@/components/SearchForm";
import card from '@/components/card'
import columns from "./columns";
import searchFields from "./searchFields";

export default {
  name: "EaxmPsn",
  components: {
    CommonTable,
    SearchForm,
    card
  },
  data() {
    return {
      // 表单输入框统一宽度
      inputWidth: 240,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检查人员结果表格数据
      eaxmPsnList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表格列配置
      columns: columns,
      // 筛选字段配置
      searchFields: searchFields,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        examTaskId: null,
        examPsnList: null,
        examPsnTypeList: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        examTaskId: [
          { required: true, message: "检查任务id不能为空", trigger: "blur" }
        ],
        examPsnList: [
          { required: true, message: "检查人员结果不能为空", trigger: "blur" }
        ],
        examPsnTypeList: [
          { required: true, message: "检查人员性质不能为空", trigger: "blur" }
        ],
        valiFlag: [
          { required: true, message: "有效标志不能为空", trigger: "blur" }
        ],
        rid: [
          { required: true, message: "数据唯一记录号不能为空", trigger: "blur" }
        ],
        crterId: [
          { required: true, message: "创建人id不能为空", trigger: "blur" }
        ],
        crterName: [
          { required: true, message: "创建人姓名不能为空", trigger: "blur" }
        ],
        crteTime: [
          { required: true, message: "数据创建时间不能为空", trigger: "blur" }
        ],
        crteOptinsNo: [
          { required: true, message: "创建机构编号不能为空", trigger: "blur" }
        ],
        opterId: [
          { required: true, message: "经办人id不能为空", trigger: "blur" }
        ],
        opterName: [
          { required: true, message: "经办人姓名不能为空", trigger: "blur" }
        ],
        optTime: [
          { required: true, message: "经办时间不能为空", trigger: "blur" }
        ],
        optinsNo: [
          { required: true, message: "经办机构编号不能为空", trigger: "blur" }
        ],
        updtTime: [
          { required: true, message: "数据更新时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询检查人员结果列表 */
    getList() {
      this.loading = true;
      listEaxmPsn(this.queryParams).then(response => {
        this.eaxmPsnList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取数据出错:', error);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        examTaskPsnId: null,
        examTaskId: null,
        examPsnList: null,
        examPsnTypeList: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.examTaskPsnId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检查人员结果";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const examTaskPsnId = row.examTaskPsnId || this.ids
      getEaxmPsn(examTaskPsnId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改检查人员结果";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.examTaskPsnId != null) {
            updateEaxmPsn(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEaxmPsn(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const examTaskPsnIds = row.examTaskPsnId || this.ids;
      this.$modal.confirm('是否确认删除检查人员结果编号为"' + examTaskPsnIds + '"的数据项？').then(function() {
        return delEaxmPsn(examTaskPsnIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('exam/eaxmPsn/export', {
        ...this.queryParams
      }, `eaxmPsn_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
