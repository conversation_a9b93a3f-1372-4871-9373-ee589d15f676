import request from '@/utils/request'

// 查询检查人员结果列表
export function listEaxmPsn(query) {
  return request({
    url: '/exam/eaxmPsn/list',
    method: 'get',
    params: query
  })
}

// 查询检查人员结果详细
export function getEaxmPsn(examTaskPsnId) {
  return request({
    url: '/exam/eaxmPsn/' + examTaskPsnId,
    method: 'get'
  })
}

// 新增检查人员结果
export function addEaxmPsn(data) {
  return request({
    url: '/exam/eaxmPsn',
    method: 'post',
    data: data
  })
}

// 修改检查人员结果
export function updateEaxmPsn(data) {
  return request({
    url: '/exam/eaxmPsn',
    method: 'put',
    data: data
  })
}

// 删除检查人员结果
export function delEaxmPsn(examTaskPsnId) {
  return request({
    url: '/exam/eaxmPsn/' + examTaskPsnId,
    method: 'delete'
  })
}
