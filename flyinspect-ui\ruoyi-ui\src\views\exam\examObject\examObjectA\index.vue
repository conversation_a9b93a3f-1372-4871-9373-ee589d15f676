<template>
  <div class="app-container">
    <card>
      <search-form v-show="showSearch" :modelValue="queryParams" :fields="searchFields" :show-search="showSearch"
        @search="handleQuery" @reset="resetQuery" @update:model-value="val => queryParams = val" />
    </card>
    <card class="mt10">
      <el-row :gutter="10" class="mb8" style="display: flex;justify-content: flex-end;">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['exam:examObject:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['exam:examObject:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['exam:examObject:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['exam:examObject:export']">导出</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport"
            v-hasPermi="['exam:examObject:importData']">导入</el-button>
        </el-col>
      </el-row>
      <common-table :loading="loading" :data="examObjectList" :total="total" :columns="columns"
        :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @selection-change="handleSelectionChange"
        @pagination="getList" :dict="dict">
        <template #operation="{ row }">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(row)"
            v-hasPermi="['exam:examObject:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(row)"
            v-hasPermi="['exam:examObject:remove']">删除</el-button>
        </template>
      </common-table>
    </card>

    <!-- 添加或修改检查对象名录管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="auto" inline>
        <el-form-item label="检查库名称" prop="examDpotName">
          <el-input v-model="form.examDpotName" placeholder="请输入检查库名称" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="统一社会信用代码" prop="uscc">
          <el-input v-model="form.uscc" placeholder="请输入统一社会信用代码" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="医保区划" prop="admdvs">
          <el-cascader v-model="form.admdvs" :style="`width: ${inputWidth}px`" :options="admdvsOptions"
            placeholder="请选择医保区划"></el-cascader>
        </el-form-item>
        <el-form-item label="检查对象编码" prop="legentCode">
          <el-input v-model="form.legentCode" placeholder="请输入检查对象编码" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="检查对象名称" prop="legentName">
          <el-input v-model="form.legentName" placeholder="请输入内容" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="检查对象等级" prop="legentLv">
          <el-select v-model="form.legentLv" placeholder="请选择检查对象等级" :style="`width: ${inputWidth}px`">
            <el-option v-for="dict in dict.type.econ_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="经济类型" prop="econType">
          <el-select v-model="form.econType" placeholder="请选择经济类型" :style="`width: ${inputWidth}px`">
            <el-option v-for="dict in dict.type.legent_lv" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="批准成立部门" prop="aprvEstaDept">
          <el-input v-model="form.aprvEstaDept" placeholder="请输入批准成立部门" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="法定代表人姓名" prop="legrepName">
          <el-input v-model="form.legrepName" placeholder="请输入法定代表人姓名" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="法人地址" prop="legentAddr">
          <el-input v-model="form.legentAddr" placeholder="请输入法人地址" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="注册登记代码" prop="regRegCode">
          <el-input v-model="form.regRegCode" placeholder="请输入注册登记代码" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="成立日期" prop="aprvEstaDate">
          <el-date-picker clearable v-model="form.aprvEstaDate" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择成立日期" :style="`width: ${inputWidth}px`">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="经营范围" prop="bizScp">
          <el-input v-model="form.bizScp" placeholder="请输入内容" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="检查对象类型" prop="examObjType">
          <el-select v-model="form.examObjType" placeholder="请选择检查对象类型" :style="`width: ${inputWidth}px`">
            <el-option v-for="dict in dict.type.exam_obj_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检查年月" prop="year">
          <el-date-picker :style="`width: ${inputWidth}px`" v-model="form.year" type="month" value-format="yyyyMM"
            placeholder="选择月">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否慢病" prop="chronicFlag">
          <el-select v-model="form.chronicFlag" placeholder="请选择是否慢病" :style="`width: ${inputWidth}px`">
            <el-option v-for="dict in dict.type.whether_flag" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否特病" prop="specialFlag">
          <el-select v-model="form.specialFlag" placeholder="请选择是否特病" :style="`width: ${inputWidth}px`">
            <el-option v-for="dict in dict.type.whether_flag" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="信用等级" prop="examCreditLv">
          <el-select v-model="form.examCreditLv" placeholder="请选择信用等级" :style="`width: ${inputWidth}px`">
            <el-option v-for="dict in dict.type.exam_credit_lv" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listExamObject, getExamObject, delExamObject, addExamObject, updateExamObject } from "@/api/exam/examObject";
import { admdvsList } from "@/api/system/admdvs";
import CommonTable from "@/components/CommonTable/index.vue";
import SearchForm from "@/components/SearchForm/index.vue";
import card from '@/components/card/index.vue'
import columns from "./columns";
import searchFields from "./searchFields";
import { getToken } from "@/utils/auth";

export default {
  name: "ExamObject",
  components: {
    CommonTable,
    SearchForm,
    card
  },
  dicts: ["whether_flag", "legent_lv", "exam_obj_type", "exam_credit_lv", "econ_type"],
  data() {
    return {
      // 表单输入框统一宽度
      inputWidth: 240,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检查对象名录管理表格数据
      examObjectList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表格列配置
      columns: columns,
      // 筛选字段配置
      searchFields: searchFields(this),
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/exam/examObject/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        examDpotName: null,
        uscc: null,
        admdvs: null,
        legentCode: null,
        legentName: null,
        legentLv: null,
        econType: null,
        aprvEstaDept: null,
        legrepName: null,
        legentAddr: null,
        regRegCode: null,
        aprvEstaDate: null,
        examObjType: null,
        bizScp: null,
        year: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null,
        importFlag: null,
        chronicFlag: null,
        specialFlag: null,
        desiFlag: null,
        examCreditLv: null,
        admdvsName: null,
        selcSeq: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        examDpotName: [
          { required: true, message: "检查库名称不能为空", trigger: "blur" }
        ],
        uscc: [
          { required: true, message: "统一社会信用代码不能为空", trigger: "blur" }
        ],
        admdvs: [
          { required: true, message: "医保区划不能为空", trigger: "blur" }
        ],
        legentCode: [
          { required: true, message: "检查对象编码不能为空", trigger: "blur" }
        ],
        legentName: [
          { required: true, message: "检查对象名称不能为空", trigger: "blur" }
        ],
        legentLv: [
          { required: true, message: "检查对象等级不能为空", trigger: "blur" }
        ],
        examObjType: [
          { required: true, message: "检查对象类型不能为空", trigger: "change" }
        ],
        importFlag: [
          { required: true, message: "导入标志不能为空", trigger: "blur" }
        ]
      },
      admdvsOptions: []
    };
  },
  created() {
    this.getList();
    this.getAdmdvs();
  },
  methods: {
    // 字典加载完成回调
    onDictReady(dict) {
      console.log('字典数据加载完成');
      this.initDictOptions();
    },
    // 初始化字典选项
    initDictOptions() {
      // 检查字段是否存在options属性，没有则添加
      const optionsFields = [
        { index: 3, dict: 'legent_lv', label: '检查对象等级' },
        { index: 6, dict: 'econ_type', label: '经济类型' },
        { index: 7, dict: 'exam_obj_type', label: '检查对象类型' },
        { index: 8, dict: 'whether_flag', label: '是否慢病' },
        { index: 9, dict: 'whether_flag', label: '是否特病' },
        { index: 10, dict: 'exam_credit_lv', label: '信用等级' }
      ];

      optionsFields.forEach(item => {
        if (this.searchFields[item.index]) {
          // 确保options属性存在
          if (!this.searchFields[item.index].options) {
            this.$set(this.searchFields[item.index], 'options', []);
          }
          // 获取字典数据
          const dictData = this.dict.type[item.dict];
          if (dictData && dictData.length > 0) {
            // 赋值字典
            this.searchFields[item.index].options = dictData;
          } else {
            console.warn(`字典数据为空: ${item.dict}`);
          }
        } else {
          console.warn(`未找到索引 ${item.index} 的搜索字段`);
        }
      });
    },
    getAdmdvs() {
      admdvsList({}).then(response => {
        if (response && response.data && Array.isArray(response.data)) {
          const processedData = response.data.map(item => {
            return {
              ...item,
              value: item.value || '',
              label: item.label || '',
              children: Array.isArray(item.children) ? item.children : []
            };
          });
          this.searchFields[0].options = processedData;
          this.admdvsOptions = processedData;
        } else {
          this.searchFields[0].options = [];
          this.admdvsOptions = [];
          console.warn('医保区划数据为空或格式不正确');
        }
      }).catch(error => {
        this.searchFields[0].options = [];
        this.admdvsOptions = [];
        console.error('获取医保区划数据出错:', error);
      });
    },
    /** 查询检查对象名录管理列表 */
    getList() {
      this.loading = true;
      listExamObject(this.queryParams).then(response => {
        this.examObjectList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取数据出错:', error);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        examObjListId: null,
        examDpotName: null,
        uscc: null,
        admdvs: null,
        legentCode: null,
        legentName: null,
        legentLv: null,
        econType: null,
        aprvEstaDept: null,
        legrepName: null,
        legentAddr: null,
        regRegCode: null,
        aprvEstaDate: null,
        examObjType: null,
        bizScp: null,
        year: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null,
        importFlag: null,
        chronicFlag: null,
        specialFlag: null,
        desiFlag: null,
        examCreditLv: null,
        admdvsName: null,
        selcSeq: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      if (params) {
        Object.keys(params).forEach(key => {
          if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
            this.queryParams[key] = params[key];
          }
        });
      }
      if (this.queryParams.admdvs) {
        this.queryParams.admdvs = this.fixedAdmdvs(this.queryParams.admdvs)
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      const pageNum = this.queryParams.pageNum;
      const pageSize = this.queryParams.pageSize;

      this.queryParams = {
        pageNum: pageNum,
        pageSize: pageSize
      };

      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.examObjListId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检查对象名录管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const examObjListId = row.examObjListId || this.ids
      getExamObject(examObjListId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改检查对象名录管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.admdvs = this.fixedAdmdvs(this.form.admdvs)
          if (this.form.examObjListId != null) {
            updateExamObject(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addExamObject(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const examObjListIds = row.examObjListId || this.ids;
      this.$modal.confirm('是否确认删除检查对象名录管理编号为"' + examObjListIds + '"的数据项？').then(function () {
        return delExamObject(examObjListIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('exam/examObject/export', {
        ...this.queryParams
      }, `examObject_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "检查名录对象导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('exam/examObject/importTemplate', {
      }, `exam_template_${new Date().getTime()}.xlsx`)
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    fixedAdmdvs(val) {
      if (typeof val == "string" && val.constructor == String) {
        return val;
      }
      if (val && val.length) {
        return val[val.length - 1];
      }
      return null;
    },
  }
};
</script>
