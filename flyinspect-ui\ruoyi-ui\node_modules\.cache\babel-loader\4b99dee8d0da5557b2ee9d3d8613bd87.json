{"remainingRequest": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\api\\basic\\volaBook.js", "dependencies": [{"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\api\\basic\\volaBook.js", "mtime": 1750383268000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\babel.config.js", "mtime": 1748414362378}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9Xb3JrU3BhY2Uvd29ya3NwYWNlLXJhbmRvbS9mbHlpbnNwZWN0L2ZseWluc3BlY3QtdWkvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmFkZFZvbGFCb29rID0gYWRkVm9sYUJvb2s7CmV4cG9ydHMuZGVsVm9sYUJvb2sgPSBkZWxWb2xhQm9vazsKZXhwb3J0cy5nZXRWb2xhQm9vayA9IGdldFZvbGFCb29rOwpleHBvcnRzLmxpc3RWb2xhQm9vayA9IGxpc3RWb2xhQm9vazsKZXhwb3J0cy51cGRhdGVWb2xhQm9vayA9IHVwZGF0ZVZvbGFCb29rOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i54mp5Lu35Lmm55+l6K+G5L+h5oGv5YiX6KGoCmZ1bmN0aW9uIGxpc3RWb2xhQm9vayhxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2Jhc2ljL3ZvbGFCb29rL2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i54mp5Lu35Lmm55+l6K+G5L+h5oGv6K+m57uGCmZ1bmN0aW9uIGdldFZvbGFCb29rKHZvbGFCb29rSWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9iYXNpYy92b2xhQm9vay8nICsgdm9sYUJvb2tJZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe54mp5Lu35Lmm55+l6K+G5L+h5oGvCmZ1bmN0aW9uIGFkZFZvbGFCb29rKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9iYXNpYy92b2xhQm9vaycsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS554mp5Lu35Lmm55+l6K+G5L+h5oGvCmZ1bmN0aW9uIHVwZGF0ZVZvbGFCb29rKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9iYXNpYy92b2xhQm9vaycsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTnianku7fkuabnn6Xor4bkv6Hmga8KZnVuY3Rpb24gZGVsVm9sYUJvb2sodm9sYUJvb2tJZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2Jhc2ljL3ZvbGFCb29rLycgKyB2b2xhQm9va0lkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listVolaBook", "query", "request", "url", "method", "params", "getVolaBook", "volaBookId", "addVolaBook", "data", "updateVolaBook", "delVolaBook"], "sources": ["E:/WorkSpace/workspace-random/flyinspect/flyinspect-ui/ruoyi-ui/src/api/basic/volaBook.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询物价书知识信息列表\r\nexport function listVolaBook(query) {\r\n  return request({\r\n    url: '/basic/volaBook/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询物价书知识信息详细\r\nexport function getVolaBook(volaBookId) {\r\n  return request({\r\n    url: '/basic/volaBook/' + volaBookId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增物价书知识信息\r\nexport function addVolaBook(data) {\r\n  return request({\r\n    url: '/basic/volaBook',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改物价书知识信息\r\nexport function updateVolaBook(data) {\r\n  return request({\r\n    url: '/basic/volaBook',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除物价书知识信息\r\nexport function delVolaBook(volaBookId) {\r\n  return request({\r\n    url: '/basic/volaBook/' + volaBookId,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,WAAWA,CAACC,UAAU,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,UAAU;IACpCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,cAAcA,CAACD,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,WAAWA,CAACJ,UAAU,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,UAAU;IACpCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}