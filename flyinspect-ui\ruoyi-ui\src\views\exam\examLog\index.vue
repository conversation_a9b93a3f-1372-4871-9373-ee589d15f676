<template>
  <div class="app-container">
    <card>
      <search-form v-show="showSearch" :modelValue="queryParams" :fields="searchFields" :show-search="showSearch"
        @search="handleQuery" @reset="resetQuery" @update:model-value="val => queryParams = val" />
    </card>
    <card class="mt10">
      <el-row :gutter="10" class="mb8" style="display: flex;justify-content: flex-end;">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['exam:examLog:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['exam:examLog:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['exam:examLog:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['exam:examLog:export']">导出</el-button>
        </el-col>
      </el-row>
      <common-table :loading="loading" :data="examLogList" :total="total" :columns="columns"
        :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @selection-change="handleSelectionChange"
        @pagination="getList">
        <template #operation="{ row }">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(row)"
            v-hasPermi="['exam:examLog:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(row)"
            v-hasPermi="['exam:examLog:remove']">删除</el-button>
        </template>
      </common-table>
    </card>

    <!-- 添加或修改双随机日志记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="auto" inline>
        <el-form-item label="检查日志id" prop="examInfoLogId">
          <el-input v-model="form.examInfoLogId" placeholder="请输入检查日志id" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="检查任务id" prop="examTaskId">
          <el-input v-model="form.examTaskId" placeholder="请输入检查任务id" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="行政区划" prop="admdvs">
          <el-input v-model="form.admdvs" placeholder="请输入行政区划" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="检查条数" prop="examCnt">
          <el-input v-model="form.examCnt" placeholder="请输入检查条数" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="日志描述" prop="logDscr">
          <el-input v-model="form.logDscr" type="textarea" placeholder="请输入内容" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="异常信息" prop="errorInfo">
          <el-input v-model="form.errorInfo" type="textarea" placeholder="请输入内容" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="备注" prop="memo">
          <el-input v-model="form.memo" type="textarea" placeholder="请输入内容" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="有效标志" prop="valiFlag">
          <el-input v-model="form.valiFlag" placeholder="请输入有效标志" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="数据唯一记录号" prop="rid">
          <el-input v-model="form.rid" placeholder="请输入数据唯一记录号" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="数据更新时间" prop="updtTime">
          <el-date-picker clearable v-model="form.updtTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择数据更新时间" :style="`width: ${inputWidth}px`">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="创建人id" prop="crterId">
          <el-input v-model="form.crterId" placeholder="请输入创建人id" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="创建人姓名" prop="crterName">
          <el-input v-model="form.crterName" placeholder="请输入创建人姓名" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="数据创建时间" prop="crteTime">
          <el-date-picker clearable v-model="form.crteTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择数据创建时间" :style="`width: ${inputWidth}px`">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="创建机构编号" prop="crteOptinsNo">
          <el-input v-model="form.crteOptinsNo" placeholder="请输入创建机构编号" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="经办人id" prop="opterId">
          <el-input v-model="form.opterId" placeholder="请输入经办人id" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="经办人姓名" prop="opterName">
          <el-input v-model="form.opterName" placeholder="请输入经办人姓名" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="经办时间" prop="optTime">
          <el-date-picker clearable v-model="form.optTime" type="date" value-format="yyyy-MM-dd" 
            placeholder="请选择经办时间" :style="`width: ${inputWidth}px`">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="经办机构编号" prop="optinsNo">
          <el-input v-model="form.optinsNo" placeholder="请输入经办机构编号" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="统筹区编号" prop="poolareaNo">
          <el-input v-model="form.poolareaNo" placeholder="请输入统筹区编号" :style="`width: ${inputWidth}px`" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listExamLog, getExamLog, delExamLog, addExamLog, updateExamLog } from "@/api/exam/examLog";
import CommonTable from "@/components/CommonTable";
import SearchForm from "@/components/SearchForm";
import card from '@/components/card'
import columns from "./columns";
import searchFields from "./searchFields";

export default {
  name: "ExamLog",
  components: {
    CommonTable,
    SearchForm,
    card
  },
  data() {
    return {
      // 表单输入框统一宽度
      inputWidth: 240,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 双随机日志记录表格数据
      examLogList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表格列配置
      columns: columns,
      // 筛选字段配置
      searchFields: searchFields,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        examInfoLogId: null,
        examType: null,
        examTaskId: null,
        admdvs: null,
        examCnt: null,
        logDscr: null,
        errorInfo: null,
        memo: null,
        valiFlag: null,
        rid: null,
        updtTime: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        poolareaNo: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        examInfoLogId: [
          { required: true, message: "检查日志id不能为空", trigger: "blur" }
        ],
        examType: [
          { required: true, message: "检查类型不能为空", trigger: "change" }
        ],
        valiFlag: [
          { required: true, message: "有效标志不能为空", trigger: "blur" }
        ],
        rid: [
          { required: true, message: "数据唯一记录号不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询双随机日志记录列表 */
    getList() {
      this.loading = true;
      listExamLog(this.queryParams).then(response => {
        this.examLogList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取数据出错:', error);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        examInfoLogId: null,
        examType: null,
        examTaskId: null,
        admdvs: null,
        examCnt: null,
        logDscr: null,
        errorInfo: null,
        memo: null,
        valiFlag: null,
        rid: null,
        updtTime: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        poolareaNo: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      if (params) {
        // 将搜索表单的数据合并到queryParams中
        Object.keys(params).forEach(key => {
          // 只合并有值的字段，忽略空值
          if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
            this.queryParams[key] = params[key];
          }
        });
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置查询参数（保留分页参数）
      const pageNum = this.queryParams.pageNum;
      const pageSize = this.queryParams.pageSize;

      // 初始化查询参数
      this.queryParams = {
        pageNum: pageNum,
        pageSize: pageSize
      };

      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.examInfoLogId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加双随机日志记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const examInfoLogId = row.examInfoLogId || this.ids
      getExamLog(examInfoLogId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改双随机日志记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.examInfoLogId != null) {
            updateExamLog(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addExamLog(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const examInfoLogIds = row.examInfoLogId || this.ids;
      this.$modal.confirm('是否确认删除双随机日志记录编号为"' + examInfoLogIds + '"的数据项？').then(function () {
        return delExamLog(examInfoLogIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('exam/examLog/export', {
        ...this.queryParams
      }, `examLog_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
