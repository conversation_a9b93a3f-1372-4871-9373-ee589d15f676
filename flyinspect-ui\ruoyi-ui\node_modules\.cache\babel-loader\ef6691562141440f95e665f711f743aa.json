{"remainingRequest": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\api\\basic\\volaData.js", "dependencies": [{"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\api\\basic\\volaData.js", "mtime": 1750383268000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\babel.config.js", "mtime": 1748414362378}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9Xb3JrU3BhY2Uvd29ya3NwYWNlLXJhbmRvbS9mbHlpbnNwZWN0L2ZseWluc3BlY3QtdWkvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmFkZFZvbGFEYXRhID0gYWRkVm9sYURhdGE7CmV4cG9ydHMuZGVsVm9sYURhdGEgPSBkZWxWb2xhRGF0YTsKZXhwb3J0cy5nZXRWb2xhRGF0YSA9IGdldFZvbGFEYXRhOwpleHBvcnRzLmxpc3RWb2xhRGF0YSA9IGxpc3RWb2xhRGF0YTsKZXhwb3J0cy51cGRhdGVWb2xhRGF0YSA9IHVwZGF0ZVZvbGFEYXRhOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i6L+d6KeE5L+h5oGv5YiX6KGoCmZ1bmN0aW9uIGxpc3RWb2xhRGF0YShxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2Jhc2ljL3ZvbGFEYXRhL2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i6L+d6KeE5L+h5oGv6K+m57uGCmZ1bmN0aW9uIGdldFZvbGFEYXRhKHNldGxGZWVJbmZvSWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9iYXNpYy92b2xhRGF0YS8nICsgc2V0bEZlZUluZm9JZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe6L+d6KeE5L+h5oGvCmZ1bmN0aW9uIGFkZFZvbGFEYXRhKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9iYXNpYy92b2xhRGF0YScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS56L+d6KeE5L+h5oGvCmZ1bmN0aW9uIHVwZGF0ZVZvbGFEYXRhKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9iYXNpYy92b2xhRGF0YScsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTov53op4Tkv6Hmga8KZnVuY3Rpb24gZGVsVm9sYURhdGEoc2V0bEZlZUluZm9JZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2Jhc2ljL3ZvbGFEYXRhLycgKyBzZXRsRmVlSW5mb0lkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listVolaData", "query", "request", "url", "method", "params", "getVolaData", "setlFeeInfoId", "addVolaData", "data", "updateVolaData", "delVolaData"], "sources": ["E:/WorkSpace/workspace-random/flyinspect/flyinspect-ui/ruoyi-ui/src/api/basic/volaData.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询违规信息列表\r\nexport function listVolaData(query) {\r\n  return request({\r\n    url: '/basic/volaData/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询违规信息详细\r\nexport function getVolaData(setlFeeInfoId) {\r\n  return request({\r\n    url: '/basic/volaData/' + setlFeeInfoId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增违规信息\r\nexport function addVolaData(data) {\r\n  return request({\r\n    url: '/basic/volaData',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改违规信息\r\nexport function updateVolaData(data) {\r\n  return request({\r\n    url: '/basic/volaData',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除违规信息\r\nexport function delVolaData(setlFeeInfoId) {\r\n  return request({\r\n    url: '/basic/volaData/' + setlFeeInfoId,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,WAAWA,CAACC,aAAa,EAAE;EACzC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,aAAa;IACvCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,cAAcA,CAACD,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,WAAWA,CAACJ,aAAa,EAAE;EACzC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,aAAa;IACvCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}