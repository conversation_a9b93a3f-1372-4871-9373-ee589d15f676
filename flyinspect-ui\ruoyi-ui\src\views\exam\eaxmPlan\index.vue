<template>
  <div class="app-container">
    <card>
        <search-form v-show="showSearch" :modelValue="queryParams" :fields="searchFields" :show-search="showSearch"
          @search="handleQuery" @reset="resetQuery" @update:model-value="val => queryParams = val" />
    </card>
    <card class="mt10">
      <el-row :gutter="10" class="mb8" style="display: flex;justify-content: flex-end;">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                     v-hasPermi="['exam:eaxmPlan:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                     v-hasPermi="['exam:eaxmPlan:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
                     v-hasPermi="['exam:eaxmPlan:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                     v-hasPermi="['exam:eaxmPlan:export']">导出</el-button>
        </el-col>
      </el-row>
      <common-table :loading="loading" :data="eaxmPlanList" :total="total" :columns="columns"
                    :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @selection-change="handleSelectionChange"
                    @pagination="getList" :dict="dict">
        <template #operation="{ row }">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(row)"
                     v-hasPermi="['exam:eaxmPlan:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(row)"
                     v-hasPermi="['exam:eaxmPlan:remove']">删除</el-button>
          <el-button size="mini" type="text"
                     @click="switchToExamTaskInfo(row)">任务创建</el-button>
        </template>
      </common-table>
    </card>
    <!-- 添加或修改检测计划对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="auto" inline>
        <el-form-item label="医保区划" prop="admdvs">
          <el-cascader
            v-model="form.admdvs"
            :style="`width: ${inputWidth}px`"
            :options="admdvsOptions"
            placeholder="请选择医保区划"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="检查计划编号" prop="examPlanCode">
          <el-input v-model="form.examPlanCode" placeholder="请输入检查计划编号" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="检查计划名称" prop="examPlanName">
          <el-input v-model="form.examPlanName" placeholder="请输入检查计划名称" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="计划开始日期" prop="planBegndate">
          <el-date-picker clearable v-model="form.planBegndate" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择计划开始日期" :style="`width: ${inputWidth}px`">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划结束日期" prop="planEnddate">
          <el-date-picker clearable v-model="form.planEnddate" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择计划结束日期" :style="`width: ${inputWidth}px`">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="所属部门名称" prop="locDeptName">
          <el-input v-model="form.locDeptName" placeholder="请输入所属部门名称" :style="`width: ${inputWidth}px`" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listEaxmPlan, getEaxmPlan, delEaxmPlan, addEaxmPlan, updateEaxmPlan } from "@/api/exam/eaxmPlan";
import CommonTable from "@/components/CommonTable";
import SearchForm from "@/components/SearchForm";
import card from '@/components/card';
import columns from "./columns";
import searchFields from "./searchFields";
import ExamTaskInfo from "@/views/exam/examTaskInfo/index";
import {admdvsList} from "@/api/system/admdvs";  // 导入检查任务信息组件

export default {
  name: "EaxmPlan",
  components: {
    CommonTable,
    SearchForm,
    card,
    ExamTaskInfo
  },
  data() {
    return {
      // 当前选中的检查计划信息
      currentExamPlanId: null,
      currentExamPlanName: null,
      // 表单输入框统一宽度
      inputWidth: 240,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检测计划表格数据
      eaxmPlanList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表格列配置
      columns: columns,
      // 筛选字段配置
      searchFields: searchFields,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        admdvs: null,
        examPlanCode: null,
        examPlanName: null,
        planBegndate: null,
        planEnddate: null,
        locDeptName: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        admdvs: [
          { required: true, message: "医保区划不能为空", trigger: "blur" }
        ],
        examPlanCode: [
          { required: true, message: "检查计划编号不能为空", trigger: "blur" }
        ],
        examPlanName: [
          { required: true, message: "检查计划名称不能为空", trigger: "blur" }
        ],
        planBegndate: [
          { required: true, message: "计划开始日期不能为空", trigger: "blur" }
        ],
        planEnddate: [
          { required: true, message: "计划结束日期不能为空", trigger: "blur" }
        ]
      },
      admdvsOptions: []
    };
  },
  created() {
    this.getList();
    this.getAdmdvs();
  },
  methods: {
    getAdmdvs(){
      admdvsList({}).then(response => {
        this.searchFields[0].options = response.data
        this.admdvsOptions = response.data
      }).catch(error => {
        console.error('获取数据出错:', error);
      });
    },
    /** 查询检测计划列表 */
    getList() {
      this.loading = true;
      listEaxmPlan(this.queryParams).then(response => {
        this.eaxmPlanList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取数据出错:', error);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        examPlanId: null,
        admdvs: null,
        examPlanCode: null,
        examPlanName: null,
        planBegndate: null,
        planEnddate: null,
        locDeptName: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      if (params) {
        // 将搜索表单的数据合并到queryParams中
        Object.keys(params).forEach(key => {
          // 只合并有值的字段，忽略空值
          if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
            this.queryParams[key] = params[key];
          }
        });
      }
      if(this.queryParams.admdvs){
        this.queryParams.admdvs = this.fixedAdmdvs(this.queryParams.admdvs)
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置查询参数（保留分页参数）
      const pageNum = this.queryParams.pageNum;
      const pageSize = this.queryParams.pageSize;

      // 初始化查询参数
      this.queryParams = {
        pageNum: pageNum,
        pageSize: pageSize
      };

      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.examPlanId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检测计划";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const examPlanId = row.examPlanId || this.ids
      getEaxmPlan(examPlanId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改检测计划";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.admdvs = this.fixedAdmdvs(this.form.admdvs)
          if (this.form.examPlanId != null) {
            updateEaxmPlan(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEaxmPlan(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const examPlanIds = row.examPlanId || this.ids;
      this.$modal.confirm('是否确认删除检测计划编号为"' + examPlanIds + '"的数据项？').then(function () {
        return delEaxmPlan(examPlanIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    switchToExamTaskInfo(row) {
      this.$router.push({
        path: "/exam/examTaskInfo",
        query: {
          examPlanId: row.examPlanId
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('exam/eaxmPlan/export', {
        ...this.queryParams
      }, `eaxmPlan_${new Date().getTime()}.xlsx`)
    },
    fixedAdmdvs(val) {
      if (typeof val == "string" && val.constructor == String) {
        return val;
      }
      if (val && val.length) {
        return val[val.length - 1];
      }
      return null;
    }
  }
};
</script>
