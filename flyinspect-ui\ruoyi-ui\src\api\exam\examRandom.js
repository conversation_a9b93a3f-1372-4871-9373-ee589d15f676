import request from '@/utils/request'

export function quickSearchTask(query) {
    return request({
        url: '/exam/examTask/random/quick',
        method: 'post',
        data: query
    })
}

export function queryExamineTaskList(query) {
    return request({
        url: '/exam/examTask/queryExamineTaskList',
        method: 'post',
        data: query
    })
}

export function listRandomSampInsp(query) {
    return request({
        url: '/exam/examTask/random/object',
        method: 'post',
        data: query
    })
}

export function examObjectTaskRandom(query) {
    return request({
        url: '/exam/examTask/examObjectTaskRandom',
        method: 'post',
        data: query
    })
}