import request from '@/utils/request'

// 查询国家药品目录违规药品信息列表
export function listVolaWm(query) {
  return request({
    url: '/basic/volaWm/list',
    method: 'get',
    params: query
  })
}

// 查询国家药品目录违规药品信息详细
export function getVolaWm(medListCodg) {
  return request({
    url: '/basic/volaWm/' + medListCodg,
    method: 'get'
  })
}

// 新增国家药品目录违规药品信息
export function addVolaWm(data) {
  return request({
    url: '/basic/volaWm',
    method: 'post',
    data: data
  })
}

// 修改国家药品目录违规药品信息
export function updateVolaWm(data) {
  return request({
    url: '/basic/volaWm',
    method: 'put',
    data: data
  })
}

// 删除国家药品目录违规药品信息
export function delVolaWm(medListCodg) {
  return request({
    url: '/basic/volaWm/' + medListCodg,
    method: 'delete'
  })
}
