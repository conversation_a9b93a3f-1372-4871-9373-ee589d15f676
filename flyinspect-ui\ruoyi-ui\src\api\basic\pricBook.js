import request from '@/utils/request'

// 查询物价书信息列表
export function listPricBook(query) {
  return request({
    url: '/basic/pricBook/list',
    method: 'get',
    params: query
  })
}

// 查询物价书信息详细
export function getPricBook(pricBookId) {
  return request({
    url: '/basic/pricBook/' + pricBookId,
    method: 'get'
  })
}

// 新增物价书信息
export function addPricBook(data) {
  return request({
    url: '/basic/pricBook',
    method: 'post',
    data: data
  })
}

// 修改物价书信息
export function updatePricBook(data) {
  return request({
    url: '/basic/pricBook',
    method: 'put',
    data: data
  })
}

// 删除物价书信息
export function delPricBook(pricBookId) {
  return request({
    url: '/basic/pricBook/' + pricBookId,
    method: 'delete'
  })
}
