// 定义筛选字段配置
export default [
  {
    label: '医保区划',
    prop: 'admdvs',
    component: 'el-cascader',
    multiple: false,
    checkStrictly: false,
    options: [],
    attrs: {
      placeholder: '请选择医保区划',
      clearable: true
    }
  },
  {
    label: '检查对象名称',
    prop: 'examObjName',
    component: 'el-input',
    attrs: {
      placeholder: '请输入检查对象名称',
      clearable: true
    }
  },
  {
    label: '检查主体名称',
    prop: 'examMainName',
    component: 'el-input',
    attrs: {
      placeholder: '请输入检查主体名称',
      clearable: true
    }
  }
];
