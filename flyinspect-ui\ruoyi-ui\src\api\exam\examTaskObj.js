import request from '@/utils/request'

// 查询检查任务对象列表
export function listExamTaskObj(query) {
  return request({
    url: '/exam/examTaskObj/list',
    method: 'get',
    params: query
  })
}

// 查询检查任务对象详细
export function getExamTaskObj(examTaskObjId) {
  return request({
    url: '/exam/examTaskObj/' + examTaskObjId,
    method: 'get'
  })
}

// 新增检查任务对象
export function addExamTaskObj(data) {
  return request({
    url: '/exam/examTaskObj',
    method: 'post',
    data: data
  })
}

// 修改检查任务对象
export function updateExamTaskObj(data) {
  return request({
    url: '/exam/examTaskObj',
    method: 'put',
    data: data
  })
}

// 删除检查任务对象
export function delExamTaskObj(examTaskObjId) {
  return request({
    url: '/exam/examTaskObj/' + examTaskObjId,
    method: 'delete'
  })
}
