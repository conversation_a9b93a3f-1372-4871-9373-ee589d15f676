import request from '@/utils/request'

// 查询检查任务信息列表
export function listExamTaskInfo(query) {
  return request({
    url: '/exam/examTaskInfo/list',
    method: 'get',
    params: query
  })
}

// 查询检查任务信息详细
export function getExamTaskInfo(examTaskId) {
  return request({
    url: '/exam/examTaskInfo/' + examTaskId,
    method: 'get'
  })
}

// 新增检查任务信息
export function addExamTaskInfo(data) {
  return request({
    url: '/exam/examTaskInfo',
    method: 'post',
    data: data
  })
}

// 修改检查任务信息
export function updateExamTaskInfo(data) {
  return request({
    url: '/exam/examTaskInfo',
    method: 'put',
    data: data
  })
}

// 删除检查任务信息
export function delExamTaskInfo(examTaskId) {
  return request({
    url: '/exam/examTaskInfo/' + examTaskId,
    method: 'delete'
  })
}

export function eaxmMattGetList(query) {
  return request({
    url: '/exam/eaxmMatt/getList',
    method: 'get',
    params: query
  })
}
