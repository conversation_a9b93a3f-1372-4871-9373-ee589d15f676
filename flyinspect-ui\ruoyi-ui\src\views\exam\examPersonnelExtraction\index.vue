<template>
    <div class="app-container" ref="appContainer" :class="{ 'fullscreen-mode': isFullscreen }">
        <div class="random-header">
            <!--            <img class="logo" src="@/assets/logo/logo.png" alt="logo" />-->
            <div class="title">
                {{ deptName }}
                "双随机、一公开"检查人员随机抽取平台
            </div>
        </div>

        <!-- 全屏按钮 -->
        <div class="fullscreen-btn" @click="toggleFullScreen">
            <i :class="isFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"></i>
        </div>

        <div class="main-content">
            <div class="content-wrapper">
                <div class="info-section">
                    <div class="task-info">
                        <div class="info-item">
                            <div class="label">抽查任务名称:</div>
                            <el-select v-model="form.option" placeholder="请选择抽查任务名称" @change="change"
                                class="task-select">
                                <el-option v-for="item in form.nameOptions" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                        <div class="info-item">
                            <div class="label">计划检查人数:</div>
                            <div class="value">{{ form.planExamPsncnt }}</div>
                        </div>
                    </div>

                    <div class="task-info">
                        <div class="info-item">
                            <div class="label">抽取检查人数:</div>
                            <div class="value">{{ form.selcExamPsncnt }}</div>
                        </div>
                        <div class="info-item">
                            <div class="label">检查分组人次:</div>
                            <div class="value">{{ form.groupExamCnt }}</div>
                        </div>
                    </div>

                    <div ref="resultTable" class="action-area">
                        <el-button v-if="!isScrolling && !isFinished" type="primary" class="start-btn"
                            @click="startRandom">
                            <i class="el-icon-video-play"></i> 开始抽取
                        </el-button>
                        <el-button v-else-if="isScrolling && !isFinished" type="danger" class="stop-btn"
                            @click="stopRandom">
                            <i class="el-icon-video-pause"></i> 停止抽取
                        </el-button>
                        <el-button v-else type="success" class="finish-btn" @click="finishRandom">
                            <i class="el-icon-circle-check"></i> 检查对象抽取完成!
                        </el-button>
                    </div>
                </div>

                <div class="result-table" :class="{ 'scrolling': isScrolling }">
                    <div class="table-header">
                        <span class="header-text">抽取结果</span>
                        <div class="header-line"></div>
                    </div>
                    <el-table :cell-style="{ padding: '0px' }" :row-style="{ height: '85px' }" stripe
                        :data="displayList" style="width: 100%;" border>
                        <el-table-column type="index" label="序号" align="center" width="100">
                            <template slot-scope="scope">
                                <span style="font-size: 25px">{{ scope.$index + 1 }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip prop="examPsnName" label="检查人员姓名"
                            align="center"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="psnNatuName" label="检查人员性质"
                            align="center"></el-table-column>
                        <template slot="empty">
                            <el-empty description="暂无数据"></el-empty>
                        </template>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { quickSearchTask, queryExamineTaskList } from "@/api/exam/examRandom";
import { personTaskRandom, queryPersonTaskList } from "@/api/exam/examPersonnelExtraction";
import { computed } from "vue";


export default {
    name: "ExamRandom",
    data() {
        return {
            form: {
                option: "",
                selcExamPsncnt: "",
                planExamPsncnt: "",
                groupExamCnt: "",
                nameOptions: []
            },
            totalObjects: 0,
            resultList: [],
            displayList: [],
            isScrolling: false,
            isFullscreen: false, // 是否全屏状态
            isFinished: false,
            allDataPool: [], // 用于存储所有可能被抽取的数据
            scrollTimer: null, // 滚动定时器
            scrollSpeed: 50, // 滚动速度毫秒
            taskRatio: 20 //默认抽查任务表格页码显示数量
        };
    },
    mounted() {
        this.getQuickSearch();
    },
    computed: {
        deptName() {
            return this.$store.state.user.deptName
        }
    },
    created() {
        // 监听退出全屏事件
        document.addEventListener('fullscreenchange', this.fullscreenChangeHandler);
        document.addEventListener('webkitfullscreenchange', this.fullscreenChangeHandler);
        document.addEventListener('mozfullscreenchange', this.fullscreenChangeHandler);
        document.addEventListener('MSFullscreenChange', this.fullscreenChangeHandler);
    },
    beforeDestroy() {
        // 组件销毁前移除事件监听
        document.removeEventListener('fullscreenchange', this.fullscreenChangeHandler);
        document.removeEventListener('webkitfullscreenchange', this.fullscreenChangeHandler);
        document.removeEventListener('mozfullscreenchange', this.fullscreenChangeHandler);
        document.removeEventListener('MSFullscreenChange', this.fullscreenChangeHandler);

        // 确保清除定时器
        this.stopScrollingEffect();

        // 确保退出全屏
        if (this.isFullscreen) {
            this.exitFullscreen();
        }
    },
    methods: {
        change(val) {
            this.getTaskList(val);
        },
        getQuickSearch() {
            this.loading = true;
            const params = {
                examTaskName: null
            };
            quickSearchTask(params).then((res) => {
                if (res.code === 200) {
                    this.form.nameOptions = res.data;
                }
                this.loading = false;
            })
        },
        async getTaskList(val) {
            this.loading = true;
            this.taskId = val;
            const params = {
                examTaskId: val
            };
            const result = await queryExamineTaskList(params);
            if (result.code === 200) {
                this.form.planExamPsncnt = result.rows[0].planExamPsncnt;
                this.form.selcExamPsncnt = result.rows[0].selcExamPsncnt;
                this.form.groupExamCnt = result.rows[0].groupExamCnt;
                this.getTableData(val);
            } else {
                Message.error({ message: result.message });
            }
            this.loading = false;
        },
        async getTableData(val) {
            this.loading = true;
            const params = {
                examTaskId: val
            };
            const result = await queryPersonTaskList(params);
            if (result.code === 200) {
                const size = result.data.length;
                this.displayList = result.data;
                this.allDataPool = result.data;
                this.tableResultData = result.data.slice(0, size);
                this.index = size + 1;
            } else {
                this.displayList = []
                this.$message.error(result.msg);
            }
            this.loading = false;
        },
        toggleFullScreen() {
            if (!this.isFullscreen) {
                this.enterFullscreen();
            } else {
                this.exitFullscreen();
            }
        },

        // 进入全屏模式，结合浏览器API和CSS增强效果
        enterFullscreen() {
            const container = this.$refs.appContainer;
            if (!container) return;

            // 先设置CSS增强样式
            this.isFullscreen = true;

            // 使用Document的原生全屏API
            try {
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen();
                } else if (document.documentElement.webkitRequestFullscreen) { // Chrome, Safari
                    document.documentElement.webkitRequestFullscreen();
                } else if (document.documentElement.mozRequestFullScreen) { // Firefox
                    document.documentElement.mozRequestFullScreen();
                } else if (document.documentElement.msRequestFullscreen) { // IE/Edge
                    document.documentElement.msRequestFullscreen();
                }
            } catch (e) {
                console.error('全屏请求失败:', e);
                // 尝试后备方法 - 对组件应用全屏样式
                if (container) {
                    // 阻止页面滚动
                    document.body.style.overflow = 'hidden';
                }
            }
        },

        // 退出全屏模式，处理两种全屏方式
        exitFullscreen() {
            // 先复位CSS状态
            this.isFullscreen = false;

            // 恢复页面滚动
            document.body.style.overflow = '';

            // 退出浏览器原生全屏
            try {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) { // Chrome, Safari
                    document.webkitExitFullscreen();
                } else if (document.mozCancelFullScreen) { // Firefox
                    document.mozCancelFullScreen();
                } else if (document.msExitFullscreen) { // IE/Edge
                    document.msExitFullscreen();
                }
            } catch (e) {
                console.error('退出全屏失败:', e);
                // 这里不需要处理，因为CSS状态已经恢复
            }
        },

        fullscreenChangeHandler() {
            // 检测浏览器全屏状态并同步到CSS全屏状态
            const fullscreenElement = document.fullscreenElement ||
                document.webkitFullscreenElement ||
                document.mozFullScreenElement ||
                document.msFullscreenElement;

            // 如果浏览器退出全屏，我们也需要确保CSS状态同步
            this.isFullscreen = !!fullscreenElement;
        },

        async startRandom() {
            if (this.isScrolling) return;
            if (!this.form.option) {
                this.$message.error('请选择任务名称');
                return;
            }
            // 设置状态为抽取中
            this.isScrolling = true;
            this.displayList = this.tableResultData;
            this.startScrollingEffect();

            // 滚动到结果区域
            this.$nextTick(() => {
                this.scrollToResults();
            });
        },

        // 开始滚动效果
        startScrollingEffect() {
            // 清除可能存在的定时器
            if (this.scrollTimer) {
                clearInterval(this.scrollTimer);
            }

            // 设置滚动定时器
            this.scrollTimer = setInterval(() => {
                if (!this.isScrolling) {
                    clearInterval(this.scrollTimer);
                    return;
                }

                // 从所有数据中随机选择要显示的数据
                if (this.allDataPool && this.allDataPool.length > 0) {
                    const displayCount = Math.min(this.taskRatio, this.allDataPool.length);
                    this.displayList = this.getRandomItems(this.allDataPool, displayCount);
                }
            }, this.scrollSpeed);
        },

        // 获取随机项目
        getRandomItems(array, count) {
            // 深拷贝数组避免修改原数组
            const shuffled = [...array].sort(() => 0.5 - Math.random());
            return shuffled.slice(0, count);
        },

        async stopRandom() {
            if (!this.isScrolling) return;

            // 先停止滚动效果
            this.stopScrollingEffect();

            try {
                // 直接调用抽取接口
                const params = {
                    examTaskId: this.taskId,
                    selcExamPsncnt: this.form.selcExamPsncnt,
                    groupExamCnt: this.form.groupExamCnt
                };

                console.log('停止抽取，参数:', params);

                // 调用接口获取抽取结果
                const result = await personTaskRandom(params);
                if (result.code === 200) {
                    console.log('抽取结果:', result);

                    // 处理结果数据并立即显示
                    if (result.data && result.data.length > 0) {
                        this.resultList = result.data;
                        this.displayList = result.data;
                    } else {
                        this.resultList = [];
                        this.displayList = [];
                    }

                    // 设置已停止但未完成状态
                    this.isFinished = true;

                    // 显示成功消息
                    this.$message({
                        message: '抽取成功',
                        type: 'success'
                    });
                } else {
                    this.$message.error(result.msg || '抽取失败');
                }
            } catch (error) {
                console.error('停止抽取过程出错:', error);
                this.$message.error('停止抽取过程出错');
            }
        },

        // 停止滚动效果
        stopScrollingEffect() {
            if (this.scrollTimer) {
                clearInterval(this.scrollTimer);
                this.scrollTimer = null;
            }
        },

        finishRandom() {
            // 停止滚动效果
            this.stopScrollingEffect();

            // 重置所有状态，准备下一次抽取
            this.isScrolling = false;
            this.isFinished = false;
        },

        scrollToResults() {
            // 使用setTimeout确保DOM已更新
            setTimeout(() => {
                if (this.$refs.resultTable) {
                    this.$refs.resultTable.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }, 300);
        }
    }
};
</script>

<style lang="scss" scoped>
.app-container {
    background: linear-gradient(135deg, #1a4a8d 0%, #3183c7 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    color: #fff;
    position: relative;
    overflow: hidden;
    transition: all 0.5s ease;

    &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        // background: url('@/assets/images/bg-pattern.png') repeat;
        opacity: 0.05;
        pointer-events: none;
    }

    /* 全屏模式的CSS增强 */
    &.fullscreen-mode {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 2000 !important;
        background: linear-gradient(135deg, #0c325e 0%, #1665b8 100%) !important;
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        overflow: scroll;
    }
}

.fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;

    &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
    }

    &:active {
        transform: scale(0.98);
    }

    i {
        color: #fff;
        font-size: 20px;
        text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    }
}

.random-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px 0;
    // background: rgba(0, 0, 0, 0.2);
    // box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    margin-bottom: 30px;
    position: relative;

    &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 10%;
        right: 10%;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    }

    .logo {
        width: 70px;
        height: 70px;
        margin-right: 20px;
        filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.3));
    }

    .title {
        font-size: 50px;
        color: #fff;
        font-weight: 600;
        padding: 15px 80px;
        letter-spacing: 5px;
        background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 216, 255, 0)), color-stop(rgba(0, 216, 255, .6)), to(rgba(0, 216, 255, 0)));
        background-image: linear-gradient(90deg, rgba(0, 216, 255, 0), rgba(0, 216, 255, .6), rgba(0, 216, 255, 0));
    }
}

.main-content {
    flex: 1;
    padding: 0 40px 30px;
}

.content-wrapper {
    background: rgba(26, 74, 141, 0.6);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.info-section {
    padding: 30px;
    background: rgba(19, 71, 129, 0.5);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.task-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;

    .info-item {
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.1);
        padding: 15px 20px;
        border-radius: 8px;
        flex: 1;
        margin: 0 10px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);

        &:first-child {
            margin-left: 0;
        }

        &:last-child {
            margin-right: 0;
        }

        .label {
            margin-right: 15px;
            font-size: 30px;
            color: rgba(255, 255, 255, 0.8);
        }

        .value {
            font-size: 22px;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            color: #fff;
        }

        .task-select {
            width: 350px;
        }
    }
}

.action-area {
    margin: 40px auto 20px;
    text-align: center;

    .start-btn,
    .stop-btn,
    .finish-btn {
        width: 100%;
        height: 130px;
        font-size: 56px;
        border-radius: 10px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        letter-spacing: 2px;

        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: all 0.6s ease;
        }

        &:hover {
            transform: translateY(-3px);
            filter: brightness(1.1);

            &:before {
                left: 100%;
            }
        }

        &:active {
            transform: translateY(1px);
        }

        i {
            margin-right: 10px;
            font-size: 56px;
        }
    }

    .start-btn {
        background: linear-gradient(135deg, #2b7eff, #4d5aed);
        border-color: #2460c9;
        position: relative;
        overflow: hidden;
        animation: buttonPulse 2s infinite;

        &:hover {
            background: linear-gradient(135deg, #3a8cff, #5d6afd);
            box-shadow: 0 15px 30px rgba(45, 86, 224, 0.5);
            animation: buttonPulse 1s infinite;
        }
        
        &::after {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(30deg);
            animation: shimmer 3s linear infinite;
            pointer-events: none;
        }
    }

    .stop-btn {
        background: linear-gradient(135deg, #c23531, #a12b28);
        border-color: #a12b28;

        &:hover {
            background: linear-gradient(135deg, #d13c38, #b12e2a);
            box-shadow: 0 15px 30px rgba(194, 53, 49, 0.5);
        }
    }

    .finish-btn {
        background: linear-gradient(135deg, #36a64f, #2a8c41);
        border-color: #2a8c41;

        &:hover {
            background: linear-gradient(135deg, #3eb159, #2e9846);
            box-shadow: 0 15px 30px rgba(54, 166, 79, 0.5);
        }
    }
}

.result-table {
    padding: 30px;
    transition: all 0.3s ease;
    scroll-margin-top: 20px;

    .table-header {
        margin-bottom: 20px;
        display: flex;
        align-items: center;

        .header-text {
            font-size: 22px;
            font-weight: bold;
            margin-right: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #fff;
            text-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);
        }

        .header-line {
            flex: 1;
            height: 1px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.5), transparent);
        }
    }

    &.scrolling {
        box-shadow: 0 0 50px rgba(255, 255, 255, 0.3) inset;

        &:before {
            opacity: 1;
        }
    }

    .empty-text {
        text-align: center;
        padding: 50px 0;
        color: rgba(255, 255, 255, 0.5);
        font-size: 16px;
        font-style: italic;
    }
}

.footer {
    padding: 20px 0;
    text-align: center;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.5);
    margin-top: 20px;
}

::v-deep .el-select .el-input__inner {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.2);
    color: #fff;
    height: 50px;
    font-size: 16px;
    border-radius: 8px;

    &::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }
}

::v-deep .el-select-dropdown {
    background-color: rgba(19, 71, 129, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);

    .el-select-dropdown__item {
        color: rgba(255, 255, 255, 0.8);

        &.selected,
        &.hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        &.selected {
            font-weight: bold;
        }
    }
}


@keyframes pulse {
    0% {
        background: linear-gradient(135deg, rgba(43, 148, 83, 0.5), rgba(26, 122, 62, 0.5)) !important;
    }

    50% {
        background: linear-gradient(135deg, rgba(43, 148, 83, 0.8), rgba(26, 122, 62, 0.8)) !important;
    }

    100% {
        background: linear-gradient(135deg, rgba(43, 148, 83, 0.5), rgba(26, 122, 62, 0.5)) !important;
    }
}

@keyframes shine {
    0% {
        background-position: -100% -100%;
    }

    100% {
        background-position: 100% 100%;
    }
}

@keyframes buttonPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(45, 86, 224, 0.6);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(45, 86, 224, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(45, 86, 224, 0);
    }
}

@keyframes shimmer {
    from {
        transform: rotate(30deg) translateX(-150%);
    }
    to {
        transform: rotate(30deg) translateX(150%);
    }
}

@media (max-width: 1200px) {
    .task-info {
        flex-direction: column;

        .info-item {
            margin: 0 0 15px 0;
        }
    }

    .fullscreen-btn {
        top: 10px;
        right: 10px;
        width: 50px;
        height: 50px;

        i {
            font-size: 24px;
        }
    }
}

/*****************表格样式覆盖 ********************/
::v-deep .el-table {
    border: 1px solid #397bd8;
    background: transparent !important;

    tr {
        background: transparent;
    }

    th.el-table__cell {
        background: #215eb4 !important;
        font-size: 20px !important;
        color: #fff !important;
        text-align: center;
        line-height: 60px;
    }

    th.el-table__cell.is-leaf,
    td.el-table__cell {
        // border-bottom: none;
        border: none;
    }

    .el-table__cell {
        border-bottom: none;
        font-size: 18px;
        color: #fff;
        line-height: 60px;
    }

    .cell {
        line-height: 60px !important;
    }
}

::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background: rgba(33, 94, 180, 0.8);
}

::v-deep .el-table__body tr:hover>td.el-table__cell {
    background: transparent;
}

::v-deep .el-table::before,
.el-table--group::after,
.el-table--border::after {
    background-color: transparent;
}

::v-deep .el-table__body-wrapper {
    overflow-x: hidden;
    overflow-y: auto;
    // height: 1000px;
}

/* 浏览器原生全屏状态下的额外样式 */
:fullscreen .app-container,
:-webkit-full-screen .app-container,
:-moz-full-screen .app-container,
:-ms-fullscreen .app-container {
    background: linear-gradient(135deg, #0c325e 0%, #1665b8 100%);
}

:fullscreen .fullscreen-btn,
:-webkit-full-screen .fullscreen-btn,
:-moz-full-screen .fullscreen-btn,
:-ms-fullscreen .fullscreen-btn {
    top: 30px;
    right: 30px;
}
</style>
