<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="就诊ID" prop="mdtrtId">
        <el-input
          v-model="queryParams.mdtrtId"
          placeholder="请输入就诊ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="结算ID" prop="setlId">
        <el-input
          v-model="queryParams.setlId"
          placeholder="请输入结算ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="定点医药机构编号" prop="fixmedinsCode">
        <el-input
          v-model="queryParams.fixmedinsCode"
          placeholder="请输入定点医药机构编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="定点医药机构名称" prop="fixmedinsName">
        <el-input
          v-model="queryParams.fixmedinsName"
          placeholder="请输入定点医药机构名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="人员编号" prop="psnNo">
        <el-input
          v-model="queryParams.psnNo"
          placeholder="请输入人员编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="人员姓名" prop="psnName">
        <el-input
          v-model="queryParams.psnName"
          placeholder="请输入人员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="性别" prop="GEND">
        <el-input
          v-model="queryParams.GEND"
          placeholder="请输入性别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年龄" prop="AGE">
        <el-input
          v-model="queryParams.AGE"
          placeholder="请输入年龄"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="基金支付总额" prop="fundPaySumamt">
        <el-input
          v-model="queryParams.fundPaySumamt"
          placeholder="请输入基金支付总额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="个人账户支出" prop="acctPay">
        <el-input
          v-model="queryParams.acctPay"
          placeholder="请输入个人账户支出"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="现金支付金额" prop="cashPayamt">
        <el-input
          v-model="queryParams.cashPayamt"
          placeholder="请输入现金支付金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="结算时间" prop="setlTime">
        <el-date-picker clearable
          v-model="queryParams.setlTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择结算时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="医药机构目录编码" prop="medinsListCodg">
        <el-input
          v-model="queryParams.medinsListCodg"
          placeholder="请输入医药机构目录编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医药机构目录名称" prop="medinsListName">
        <el-input
          v-model="queryParams.medinsListName"
          placeholder="请输入医药机构目录名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医保目录编码" prop="hilistCode">
        <el-input
          v-model="queryParams.hilistCode"
          placeholder="请输入医保目录编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医保目录名称" prop="hilistName">
        <el-input
          v-model="queryParams.hilistName"
          placeholder="请输入医保目录名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="药品厂家" prop="prodeptName">
        <el-input
          v-model="queryParams.prodeptName"
          placeholder="请输入药品厂家"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="规格" prop="SPEC">
        <el-input
          v-model="queryParams.SPEC"
          placeholder="请输入规格"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="剂型名称" prop="dosformName">
        <el-input
          v-model="queryParams.dosformName"
          placeholder="请输入剂型名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单位" prop="UNIT">
        <el-input
          v-model="queryParams.UNIT"
          placeholder="请输入单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数量" prop="CNT">
        <el-input
          v-model="queryParams.CNT"
          placeholder="请输入数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单价" prop="PRIC">
        <el-input
          v-model="queryParams.PRIC"
          placeholder="请输入单价"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="明细项目费用总额" prop="detItemFeeSumamt">
        <el-input
          v-model="queryParams.detItemFeeSumamt"
          placeholder="请输入明细项目费用总额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="全自费金额" prop="fulamtOwnpayAmt">
        <el-input
          v-model="queryParams.fulamtOwnpayAmt"
          placeholder="请输入全自费金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="先行自付金额" prop="preselfpayAmt">
        <el-input
          v-model="queryParams.preselfpayAmt"
          placeholder="请输入先行自付金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="符合范围金额" prop="inscpAmt">
        <el-input
          v-model="queryParams.inscpAmt"
          placeholder="请输入符合范围金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="自付比例" prop="selfpayProp">
        <el-input
          v-model="queryParams.selfpayProp"
          placeholder="请输入自付比例"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="超限价自费费用" prop="overlmtSelfpay">
        <el-input
          v-model="queryParams.overlmtSelfpay"
          placeholder="请输入超限价自费费用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="收费项目等级" prop="chrgitmLv">
        <el-input
          v-model="queryParams.chrgitmLv"
          placeholder="请输入收费项目等级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="科室名称" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入科室名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="病种编号" prop="diseNo">
        <el-input
          v-model="queryParams.diseNo"
          placeholder="请输入病种编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="有效标志" prop="valaFlag">
        <el-input
          v-model="queryParams.valaFlag"
          placeholder="请输入有效标志"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="疑似违规金额" prop="volaAmt">
        <el-input
          v-model="queryParams.volaAmt"
          placeholder="请输入疑似违规金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人姓名" prop="crterName">
        <el-input
          v-model="queryParams.crterName"
          placeholder="请输入创建人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数据创建时间" prop="crteTime">
        <el-date-picker clearable
          v-model="queryParams.crteTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择数据创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="更新人姓名" prop="updtName">
        <el-input
          v-model="queryParams.updtName"
          placeholder="请输入更新人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数据更新时间" prop="updtTime">
        <el-date-picker clearable
          v-model="queryParams.updtTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择数据更新时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['basic:volaData:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['basic:volaData:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['basic:volaData:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['basic:volaData:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="volaDataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="结算明细ID" align="center" prop="setlFeeInfoId" />
      <el-table-column label="就诊ID" align="center" prop="mdtrtId" />
      <el-table-column label="结算ID" align="center" prop="setlId" />
      <el-table-column label="定点医药机构编号" align="center" prop="fixmedinsCode" />
      <el-table-column label="定点医药机构名称" align="center" prop="fixmedinsName" />
      <el-table-column label="人员编号" align="center" prop="psnNo" />
      <el-table-column label="证件号码" align="center" prop="CERTNO" />
      <el-table-column label="人员姓名" align="center" prop="psnName" />
      <el-table-column label="性别" align="center" prop="GEND" />
      <el-table-column label="年龄" align="center" prop="AGE" />
      <el-table-column label="基金支付总额" align="center" prop="fundPaySumamt" />
      <el-table-column label="个人账户支出" align="center" prop="acctPay" />
      <el-table-column label="现金支付金额" align="center" prop="cashPayamt" />
      <el-table-column label="医疗类别" align="center" prop="medType" />
      <el-table-column label="结算时间" align="center" prop="setlTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.setlTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="医药机构目录编码" align="center" prop="medinsListCodg" />
      <el-table-column label="医药机构目录名称" align="center" prop="medinsListName" />
      <el-table-column label="医保目录编码" align="center" prop="hilistCode" />
      <el-table-column label="医保目录名称" align="center" prop="hilistName" />
      <el-table-column label="药品厂家" align="center" prop="prodeptName" />
      <el-table-column label="规格" align="center" prop="SPEC" />
      <el-table-column label="剂型名称" align="center" prop="dosformName" />
      <el-table-column label="单位" align="center" prop="UNIT" />
      <el-table-column label="数量" align="center" prop="CNT" />
      <el-table-column label="单价" align="center" prop="PRIC" />
      <el-table-column label="明细项目费用总额" align="center" prop="detItemFeeSumamt" />
      <el-table-column label="全自费金额" align="center" prop="fulamtOwnpayAmt" />
      <el-table-column label="先行自付金额" align="center" prop="preselfpayAmt" />
      <el-table-column label="符合范围金额" align="center" prop="inscpAmt" />
      <el-table-column label="自付比例" align="center" prop="selfpayProp" />
      <el-table-column label="超限价自费费用" align="center" prop="overlmtSelfpay" />
      <el-table-column label="收费项目等级" align="center" prop="chrgitmLv" />
      <el-table-column label="科室名称" align="center" prop="deptName" />
      <el-table-column label="病种编号" align="center" prop="diseNo" />
      <el-table-column label="病种名称" align="center" prop="diseName" />
      <el-table-column label="规则名称" align="center" prop="ruleName" />
      <el-table-column label="有效标志" align="center" prop="valaFlag" />
      <el-table-column label="违规类型" align="center" prop="volaType" />
      <el-table-column label="违规描述" align="center" prop="volaDesc" />
      <el-table-column label="违规依据" align="center" prop="volaAccor" />
      <el-table-column label="问题描述" align="center" prop="DESCRIBET" />
      <el-table-column label="疑似违规金额" align="center" prop="volaAmt" />
      <el-table-column label="创建人姓名" align="center" prop="crterName" />
      <el-table-column label="数据创建时间" align="center" prop="crteTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.crteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新人姓名" align="center" prop="updtName" />
      <el-table-column label="数据更新时间" align="center" prop="updtTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updtTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['basic:volaData:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['basic:volaData:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改违规信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="就诊ID" prop="mdtrtId">
          <el-input v-model="form.mdtrtId" placeholder="请输入就诊ID" />
        </el-form-item>
        <el-form-item label="结算ID" prop="setlId">
          <el-input v-model="form.setlId" placeholder="请输入结算ID" />
        </el-form-item>
        <el-form-item label="定点医药机构编号" prop="fixmedinsCode">
          <el-input v-model="form.fixmedinsCode" placeholder="请输入定点医药机构编号" />
        </el-form-item>
        <el-form-item label="定点医药机构名称" prop="fixmedinsName">
          <el-input v-model="form.fixmedinsName" placeholder="请输入定点医药机构名称" />
        </el-form-item>
        <el-form-item label="人员编号" prop="psnNo">
          <el-input v-model="form.psnNo" placeholder="请输入人员编号" />
        </el-form-item>
        <el-form-item label="证件号码" prop="CERTNO">
          <el-input v-model="form.CERTNO" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="人员姓名" prop="psnName">
          <el-input v-model="form.psnName" placeholder="请输入人员姓名" />
        </el-form-item>
        <el-form-item label="性别" prop="GEND">
          <el-input v-model="form.GEND" placeholder="请输入性别" />
        </el-form-item>
        <el-form-item label="年龄" prop="AGE">
          <el-input v-model="form.AGE" placeholder="请输入年龄" />
        </el-form-item>
        <el-form-item label="基金支付总额" prop="fundPaySumamt">
          <el-input v-model="form.fundPaySumamt" placeholder="请输入基金支付总额" />
        </el-form-item>
        <el-form-item label="个人账户支出" prop="acctPay">
          <el-input v-model="form.acctPay" placeholder="请输入个人账户支出" />
        </el-form-item>
        <el-form-item label="现金支付金额" prop="cashPayamt">
          <el-input v-model="form.cashPayamt" placeholder="请输入现金支付金额" />
        </el-form-item>
        <el-form-item label="结算时间" prop="setlTime">
          <el-date-picker clearable
            v-model="form.setlTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择结算时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="医药机构目录编码" prop="medinsListCodg">
          <el-input v-model="form.medinsListCodg" placeholder="请输入医药机构目录编码" />
        </el-form-item>
        <el-form-item label="医药机构目录名称" prop="medinsListName">
          <el-input v-model="form.medinsListName" placeholder="请输入医药机构目录名称" />
        </el-form-item>
        <el-form-item label="医保目录编码" prop="hilistCode">
          <el-input v-model="form.hilistCode" placeholder="请输入医保目录编码" />
        </el-form-item>
        <el-form-item label="医保目录名称" prop="hilistName">
          <el-input v-model="form.hilistName" placeholder="请输入医保目录名称" />
        </el-form-item>
        <el-form-item label="药品厂家" prop="prodeptName">
          <el-input v-model="form.prodeptName" placeholder="请输入药品厂家" />
        </el-form-item>
        <el-form-item label="规格" prop="SPEC">
          <el-input v-model="form.SPEC" placeholder="请输入规格" />
        </el-form-item>
        <el-form-item label="剂型名称" prop="dosformName">
          <el-input v-model="form.dosformName" placeholder="请输入剂型名称" />
        </el-form-item>
        <el-form-item label="单位" prop="UNIT">
          <el-input v-model="form.UNIT" placeholder="请输入单位" />
        </el-form-item>
        <el-form-item label="数量" prop="CNT">
          <el-input v-model="form.CNT" placeholder="请输入数量" />
        </el-form-item>
        <el-form-item label="单价" prop="PRIC">
          <el-input v-model="form.PRIC" placeholder="请输入单价" />
        </el-form-item>
        <el-form-item label="明细项目费用总额" prop="detItemFeeSumamt">
          <el-input v-model="form.detItemFeeSumamt" placeholder="请输入明细项目费用总额" />
        </el-form-item>
        <el-form-item label="全自费金额" prop="fulamtOwnpayAmt">
          <el-input v-model="form.fulamtOwnpayAmt" placeholder="请输入全自费金额" />
        </el-form-item>
        <el-form-item label="先行自付金额" prop="preselfpayAmt">
          <el-input v-model="form.preselfpayAmt" placeholder="请输入先行自付金额" />
        </el-form-item>
        <el-form-item label="符合范围金额" prop="inscpAmt">
          <el-input v-model="form.inscpAmt" placeholder="请输入符合范围金额" />
        </el-form-item>
        <el-form-item label="自付比例" prop="selfpayProp">
          <el-input v-model="form.selfpayProp" placeholder="请输入自付比例" />
        </el-form-item>
        <el-form-item label="超限价自费费用" prop="overlmtSelfpay">
          <el-input v-model="form.overlmtSelfpay" placeholder="请输入超限价自费费用" />
        </el-form-item>
        <el-form-item label="收费项目等级" prop="chrgitmLv">
          <el-input v-model="form.chrgitmLv" placeholder="请输入收费项目等级" />
        </el-form-item>
        <el-form-item label="科室名称" prop="deptName">
          <el-input v-model="form.deptName" placeholder="请输入科室名称" />
        </el-form-item>
        <el-form-item label="病种编号" prop="diseNo">
          <el-input v-model="form.diseNo" placeholder="请输入病种编号" />
        </el-form-item>
        <el-form-item label="病种名称" prop="diseName">
          <el-input v-model="form.diseName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="form.ruleName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="有效标志" prop="valaFlag">
          <el-input v-model="form.valaFlag" placeholder="请输入有效标志" />
        </el-form-item>
        <el-form-item label="违规描述" prop="volaDesc">
          <el-input v-model="form.volaDesc" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="违规依据" prop="volaAccor">
          <el-input v-model="form.volaAccor" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="问题描述" prop="DESCRIBET">
          <el-input v-model="form.DESCRIBET" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="疑似违规金额" prop="volaAmt">
          <el-input v-model="form.volaAmt" placeholder="请输入疑似违规金额" />
        </el-form-item>
        <el-form-item label="创建人姓名" prop="crterName">
          <el-input v-model="form.crterName" placeholder="请输入创建人姓名" />
        </el-form-item>
        <el-form-item label="数据创建时间" prop="crteTime">
          <el-date-picker clearable
            v-model="form.crteTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择数据创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="更新人姓名" prop="updtName">
          <el-input v-model="form.updtName" placeholder="请输入更新人姓名" />
        </el-form-item>
        <el-form-item label="数据更新时间" prop="updtTime">
          <el-date-picker clearable
            v-model="form.updtTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择数据更新时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listVolaData, getVolaData, delVolaData, addVolaData, updateVolaData } from "@/api/basic/volaData";

export default {
  name: "VolaData",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 违规信息表格数据
      volaDataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mdtrtId: null,
        setlId: null,
        fixmedinsCode: null,
        fixmedinsName: null,
        psnNo: null,
        CERTNO: null,
        psnName: null,
        GEND: null,
        AGE: null,
        fundPaySumamt: null,
        acctPay: null,
        cashPayamt: null,
        medType: null,
        setlTime: null,
        medinsListCodg: null,
        medinsListName: null,
        hilistCode: null,
        hilistName: null,
        prodeptName: null,
        SPEC: null,
        dosformName: null,
        UNIT: null,
        CNT: null,
        PRIC: null,
        detItemFeeSumamt: null,
        fulamtOwnpayAmt: null,
        preselfpayAmt: null,
        inscpAmt: null,
        selfpayProp: null,
        overlmtSelfpay: null,
        chrgitmLv: null,
        deptName: null,
        diseNo: null,
        diseName: null,
        ruleName: null,
        valaFlag: null,
        volaType: null,
        volaDesc: null,
        volaAccor: null,
        DESCRIBET: null,
        volaAmt: null,
        crterName: null,
        crteTime: null,
        updtName: null,
        updtTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mdtrtId: [
          { required: true, message: "就诊ID不能为空", trigger: "blur" }
        ],
        setlId: [
          { required: true, message: "结算ID不能为空", trigger: "blur" }
        ],
        fixmedinsCode: [
          { required: true, message: "定点医药机构编号不能为空", trigger: "blur" }
        ],
        fixmedinsName: [
          { required: true, message: "定点医药机构名称不能为空", trigger: "blur" }
        ],
        psnNo: [
          { required: true, message: "人员编号不能为空", trigger: "blur" }
        ],
        CERTNO: [
          { required: true, message: "证件号码不能为空", trigger: "blur" }
        ],
        psnName: [
          { required: true, message: "人员姓名不能为空", trigger: "blur" }
        ],
        fundPaySumamt: [
          { required: true, message: "基金支付总额不能为空", trigger: "blur" }
        ],
        acctPay: [
          { required: true, message: "个人账户支出不能为空", trigger: "blur" }
        ],
        cashPayamt: [
          { required: true, message: "现金支付金额不能为空", trigger: "blur" }
        ],
        medType: [
          { required: true, message: "医疗类别不能为空", trigger: "change" }
        ],
        setlTime: [
          { required: true, message: "结算时间不能为空", trigger: "blur" }
        ],
        hilistCode: [
          { required: true, message: "医保目录编码不能为空", trigger: "blur" }
        ],
        hilistName: [
          { required: true, message: "医保目录名称不能为空", trigger: "blur" }
        ],
        CNT: [
          { required: true, message: "数量不能为空", trigger: "blur" }
        ],
        PRIC: [
          { required: true, message: "单价不能为空", trigger: "blur" }
        ],
        detItemFeeSumamt: [
          { required: true, message: "明细项目费用总额不能为空", trigger: "blur" }
        ],
        valaFlag: [
          { required: true, message: "有效标志不能为空", trigger: "blur" }
        ],
        volaType: [
          { required: true, message: "违规类型不能为空", trigger: "change" }
        ],
        crteTime: [
          { required: true, message: "数据创建时间不能为空", trigger: "blur" }
        ],
        updtTime: [
          { required: true, message: "数据更新时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询违规信息列表 */
    getList() {
      this.loading = true;
      listVolaData(this.queryParams).then(response => {
        this.volaDataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        setlFeeInfoId: null,
        mdtrtId: null,
        setlId: null,
        fixmedinsCode: null,
        fixmedinsName: null,
        psnNo: null,
        CERTNO: null,
        psnName: null,
        GEND: null,
        AGE: null,
        fundPaySumamt: null,
        acctPay: null,
        cashPayamt: null,
        medType: null,
        setlTime: null,
        medinsListCodg: null,
        medinsListName: null,
        hilistCode: null,
        hilistName: null,
        prodeptName: null,
        SPEC: null,
        dosformName: null,
        UNIT: null,
        CNT: null,
        PRIC: null,
        detItemFeeSumamt: null,
        fulamtOwnpayAmt: null,
        preselfpayAmt: null,
        inscpAmt: null,
        selfpayProp: null,
        overlmtSelfpay: null,
        chrgitmLv: null,
        deptName: null,
        diseNo: null,
        diseName: null,
        ruleName: null,
        valaFlag: null,
        volaType: null,
        volaDesc: null,
        volaAccor: null,
        DESCRIBET: null,
        volaAmt: null,
        crterName: null,
        crteTime: null,
        updtName: null,
        updtTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.setlFeeInfoId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加违规信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const setlFeeInfoId = row.setlFeeInfoId || this.ids
      getVolaData(setlFeeInfoId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改违规信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.setlFeeInfoId != null) {
            updateVolaData(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addVolaData(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const setlFeeInfoIds = row.setlFeeInfoId || this.ids;
      this.$modal.confirm('是否确认删除违规信息编号为"' + setlFeeInfoIds + '"的数据项？').then(function() {
        return delVolaData(setlFeeInfoIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('basic/volaData/export', {
        ...this.queryParams
      }, `volaData_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
