/*
 * @Date: 2025-05-09 14:00:31
 * @LastEditors: <PERSON>
 * @LastEditTime: 2025-05-12 16:51:39
 * @FilePath: \ruoyi-ui\src\views\exam\examObject\examObjectA\columns.js
 */
const columns = [
  { prop: "examDpotName", label: "检查库名称", width: "150" },
  { prop: "uscc", label: "统一社会信用代码", width: "180" },
  { prop: "admdvs", label: "医保区划", width: "120" },
  { prop: "admdvsName", label: "医保区划名称", width: "120" },
  { prop: "legentCode", label: "检查对象编码", width: "150" },
  { prop: "legentName", label: "检查对象名称", width: "150" },
  { prop: "legentLv", label: "检查对象等级", width: "150", dictType: "legent_lv" },
  { prop: "econType", label: "经济类型", width: "200", dictType: "econ_type" },
  { prop: "aprvEstaDept", label: "批准成立部门", width: "150" },
  { prop: "legrepName", label: "法定代表人姓名", width: "150" },
  { prop: "legentAddr", label: "机构地址", width: "150" },
  { prop: "regRegCode", label: "注册登记代码", width: "150" },
  { prop: "aprvEstaDate", label: "成立日期", type: "date", width: "120" },
  { prop: "examObjType", label: "检查对象类型", width: "150", dictType: "exam_obj_type" },
  { prop: "bizScp", label: "经营范围", width: "120" },
  { prop: "year", label: "检查年月", width: "120" },
  { prop: "chronicFlag", label: "是否慢病", width: "120", dictType: "whether_flag" },
  { prop: "specialFlag", label: "是否特病", width: "120", dictType: "whether_flag" },
  { prop: "examCreditLv", label: "信用等级", width: "120", dictType: "exam_credit_lv" },
]
export default columns
