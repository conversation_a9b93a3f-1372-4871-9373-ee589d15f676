{"remainingRequest": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\dataAnalysis\\volaTask\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\dataAnalysis\\volaTask\\index.vue", "mtime": 1750390981849}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\babel.config.js", "mtime": 1748414362378}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_volaTask", "require", "_request", "_interopRequireDefault", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "volaTaskList", "title", "open", "currentStep", "transferData", "transferValue", "allRules", "key", "label", "medicalInstitutionList", "medListLoading", "queryParams", "pageNum", "pageSize", "volaTaskInfoName", "medinsInfo", "medinsLv", "medType", "setlTimeStart", "setlTimeEnd", "ruleIds", "ruleNames", "taskTime", "form", "rules", "required", "message", "trigger", "created", "getList", "methods", "_this", "listVolaTask", "then", "response", "rows", "cancel", "reset", "nextStep", "_this2", "$refs", "validate", "valid", "prevStep", "getRuleName", "ruleId", "rule", "find", "r", "getMedicalInstitutionList", "_this3", "length", "request", "url", "method", "code", "$modal", "msgError", "msg", "catch", "error", "console", "finally", "searchMedicalInstitutions", "query", "_this4", "params", "keyword", "volaTaskInfoId", "delFlag", "createBy", "createTime", "updateBy", "updateTime", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "handleAdd", "handleUpdate", "row", "_this5", "getVolaTask", "split", "submitForm", "_this6", "msgWarning", "submitData", "_objectSpread2", "default", "Array", "isArray", "join", "selectedRuleNames", "filter", "includes", "Date", "toISOString", "updateVolaTask", "msgSuccess", "addVolaTask", "handleDelete", "_this7", "volaTaskInfoIds", "confirm", "delVolaTask", "handleExport", "download", "concat", "getTime"], "sources": ["src/views/dataAnalysis/volaTask/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\r\n      <el-form-item label=\"调度任务名称\" prop=\"volaTaskInfoName\">\r\n        <el-input\r\n          v-model=\"queryParams.volaTaskInfoName\"\r\n          placeholder=\"请输入调度任务名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"医疗机构等级\" prop=\"medinsLv\">\r\n        <el-input\r\n          v-model=\"queryParams.medinsLv\"\r\n          placeholder=\"请输入医疗机构等级\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"结算开始时间\" prop=\"setlTimeStart\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.setlTimeStart\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择结算开始时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"结算结束时间\" prop=\"setlTimeEnd\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.setlTimeEnd\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择结算结束时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"规则ID集合\" prop=\"ruleIds\">\r\n        <el-input\r\n          v-model=\"queryParams.ruleIds\"\r\n          placeholder=\"请输入规则ID集合\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"建立任务时间\" prop=\"taskTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.taskTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择建立任务时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"volaTaskList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      fit\r\n      table-layout=\"auto\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column\r\n        label=\"调度任务名称\"\r\n        align=\"center\"\r\n        prop=\"volaTaskInfoName\"\r\n        min-width=\"150\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"筛查医疗机构集合\"\r\n        align=\"center\"\r\n        prop=\"medinsInfo\"\r\n        min-width=\"180\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"医疗机构等级\"\r\n        align=\"center\"\r\n        prop=\"medinsLv\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"医疗类别\"\r\n        align=\"center\"\r\n        prop=\"medType\"\r\n        width=\"100\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"结算开始时间\"\r\n        align=\"center\"\r\n        prop=\"setlTimeStart\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.setlTimeStart, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"结算结束时间\"\r\n        align=\"center\"\r\n        prop=\"setlTimeEnd\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.setlTimeEnd, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"规则ID集合\"\r\n        align=\"center\"\r\n        prop=\"ruleIds\"\r\n        min-width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"规则名称集合\"\r\n        align=\"center\"\r\n        prop=\"ruleNames\"\r\n        min-width=\"150\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"建立任务时间\"\r\n        align=\"center\"\r\n        prop=\"taskTime\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.taskTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"备注\"\r\n        align=\"center\"\r\n        prop=\"remark\"\r\n        min-width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        width=\"120\"\r\n        fixed=\"right\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['dataAnalysis:volaTask:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['dataAnalysis:volaTask:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改调度任务信息对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <!-- 步骤条 -->\r\n      <el-steps\r\n        :active=\"currentStep\"\r\n        finish-status=\"success\"\r\n        align-center\r\n        class=\"custom-steps\">\r\n        <el-step title=\"基本信息\" description=\"填写调度任务基本信息\">\r\n          <i slot=\"icon\" class=\"el-icon-edit-outline\"></i>\r\n        </el-step>\r\n        <el-step title=\"规则选择\" description=\"选择检查规则\">\r\n          <i slot=\"icon\" class=\"el-icon-setting\"></i>\r\n        </el-step>\r\n      </el-steps>\r\n\r\n      <!-- 第一步：基本信息 -->\r\n      <div v-show=\"currentStep === 0\" class=\"step-content\">\r\n        <div class=\"step-header\">\r\n          <h3><i class=\"el-icon-edit-outline\"></i> 基本信息</h3>\r\n          <p>请填写调度任务的基本信息</p>\r\n        </div>\r\n\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"140px\" class=\"step-form\">\r\n          <div class=\"form-section\">\r\n            <div class=\"section-title\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>任务信息</span>\r\n            </div>\r\n            <el-row :gutter=\"24\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"调度任务名称\" prop=\"volaTaskInfoName\">\r\n                  <el-input\r\n                    v-model=\"form.volaTaskInfoName\"\r\n                    placeholder=\"请输入调度任务名称\"\r\n                    prefix-icon=\"el-icon-edit\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"医疗机构等级\" prop=\"medinsLv\">\r\n                  <el-select\r\n                    v-model=\"form.medinsLv\"\r\n                    placeholder=\"请选择医疗机构等级\"\r\n                    clearable\r\n                    style=\"width: 100%\">\r\n                    <el-option\r\n                      v-for=\"dict in dict.type.legent_lv\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.label\"\r\n                      :value=\"dict.value\">\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n\r\n            <el-form-item label=\"筛查医疗机构集合\" prop=\"medinsInfo\">\r\n              <el-select\r\n                v-model=\"form.medinsInfo\"\r\n                multiple\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                placeholder=\"请选择筛查医疗机构\"\r\n                :remote-method=\"searchMedicalInstitutions\"\r\n                :loading=\"medListLoading\"\r\n                style=\"width: 100%\"\r\n                @focus=\"getMedicalInstitutionList\">\r\n                <el-option\r\n                  v-for=\"item in medicalInstitutionList\"\r\n                  :key=\"item.fixmedinsCode\"\r\n                  :label=\"item.fixmedinsName\"\r\n                  :value=\"item.fixmedinsCode\">\r\n                  <span style=\"float: left\">{{ item.fixmedinsName }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ item.fixmedinsCode }}</span>\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <div class=\"form-section\">\r\n            <div class=\"section-title\">\r\n              <i class=\"el-icon-date\"></i>\r\n              <span>结算时间</span>\r\n            </div>\r\n            <el-row :gutter=\"24\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"结算开始时间\" prop=\"setlTimeStart\">\r\n                  <el-date-picker\r\n                    clearable\r\n                    v-model=\"form.setlTimeStart\"\r\n                    type=\"date\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"请选择结算开始时间\"\r\n                    prefix-icon=\"el-icon-date\"\r\n                    style=\"width: 100%\">\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"结算结束时间\" prop=\"setlTimeEnd\">\r\n                  <el-date-picker\r\n                    clearable\r\n                    v-model=\"form.setlTimeEnd\"\r\n                    type=\"date\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"请选择结算结束时间\"\r\n                    prefix-icon=\"el-icon-date\"\r\n                    style=\"width: 100%\">\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 第二步：规则选择 -->\r\n      <div v-show=\"currentStep === 1\" class=\"step-content\">\r\n        <div class=\"step-header\">\r\n          <h3><i class=\"el-icon-setting\"></i> 规则选择</h3>\r\n          <p>请选择需要应用的检查规则</p>\r\n        </div>\r\n\r\n        <div class=\"transfer-container\">\r\n          <div class=\"transfer-tips\">\r\n            <el-alert\r\n              title=\"操作提示\"\r\n              type=\"info\"\r\n              :closable=\"false\"\r\n              show-icon>\r\n              <div slot=\"description\">\r\n                <p>• 从左侧选择需要的检查规则，点击右箭头添加到已选规则</p>\r\n                <p>• 支持搜索功能，可快速定位所需规则</p>\r\n                <p>• 至少需要选择一个规则才能完成创建</p>\r\n              </div>\r\n            </el-alert>\r\n          </div>\r\n\r\n          <div class=\"transfer-wrapper\">\r\n            <el-transfer\r\n              v-model=\"transferValue\"\r\n              :data=\"allRules\"\r\n              :titles=\"['可选规则', '已选规则']\"\r\n              :button-texts=\"['移除', '添加']\"\r\n              :format=\"{\r\n                noChecked: '共 ${total} 项',\r\n                hasChecked: '已选 ${checked}/${total} 项'\r\n              }\"\r\n              filterable\r\n              filter-placeholder=\"搜索规则名称\"\r\n              class=\"custom-transfer\">\r\n              <span slot-scope=\"{ option }\">\r\n                <i class=\"el-icon-document-checked\"></i>\r\n                {{ option.label }}\r\n              </span>\r\n            </el-transfer>\r\n          </div>\r\n\r\n          <div class=\"selected-summary\" v-if=\"transferValue.length > 0\">\r\n            <div class=\"summary-header\">\r\n              <i class=\"el-icon-success\"></i>\r\n              <span>已选择 {{ transferValue.length }} 个规则</span>\r\n            </div>\r\n            <div class=\"summary-tags\">\r\n              <el-tag\r\n                v-for=\"ruleId in transferValue\"\r\n                :key=\"ruleId\"\r\n                type=\"success\"\r\n                size=\"small\"\r\n                style=\"margin: 2px 4px 2px 0;\">\r\n                {{ getRuleName(ruleId) }}\r\n              </el-tag>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n        <el-button v-if=\"currentStep > 0\" @click=\"prevStep\">上一步</el-button>\r\n        <el-button v-if=\"currentStep < 1\" type=\"primary\" @click=\"nextStep\">下一步</el-button>\r\n        <el-button v-if=\"currentStep === 1\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listVolaTask, getVolaTask, delVolaTask, addVolaTask, updateVolaTask } from \"@/api/dataAnalysis/volaTask\";\r\nimport request from '@/utils/request';\r\n\r\nexport default {\r\n  name: \"VolaTask\",\r\n  dicts: ['legent_lv'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 调度任务信息表格数据\r\n      volaTaskList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 当前步骤\r\n      currentStep: 0,\r\n      // 穿梭框数据\r\n      transferData: [],\r\n      // 穿梭框选中的值\r\n      transferValue: [],\r\n      // 所有可选规则列表\r\n      allRules: [\r\n        { key: '1', label: '规则1：医保目录外用药检查' },\r\n        { key: '2', label: '规则2：超量用药检查' },\r\n        { key: '3', label: '规则3：重复用药检查' },\r\n        { key: '4', label: '规则4：配伍禁忌检查' },\r\n        { key: '5', label: '规则5：适应症检查' },\r\n        { key: '6', label: '规则6：诊疗项目合理性检查' },\r\n        { key: '7', label: '规则7：医疗服务价格检查' },\r\n        { key: '8', label: '规则8：住院天数合理性检查' }\r\n      ],\r\n      // 医疗机构列表\r\n      medicalInstitutionList: [],\r\n      // 医疗机构列表加载状态\r\n      medListLoading: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        volaTaskInfoName: null,\r\n        medinsInfo: null,\r\n        medinsLv: null,\r\n        medType: null,\r\n        setlTimeStart: null,\r\n        setlTimeEnd: null,\r\n        ruleIds: null,\r\n        ruleNames: null,\r\n        taskTime: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        volaTaskInfoName: [\r\n          { required: true, message: \"调度任务名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        medinsInfo: [\r\n          { required: true, message: \"筛查医疗机构集合不能为空\", trigger: \"blur\" }\r\n        ],\r\n        setlTimeStart: [\r\n          { required: true, message: \"结算开始时间不能为空\", trigger: \"blur\" }\r\n        ],\r\n        setlTimeEnd: [\r\n          { required: true, message: \"结算结束时间不能为空\", trigger: \"blur\" }\r\n        ],\r\n        // 注意：ruleIds、ruleNames、taskTime 由穿梭框和系统自动生成，不需要在第一步验证\r\n        // delFlag、createBy、createTime、updateBy、updateTime 由系统自动处理，不需要验证\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询调度任务信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listVolaTask(this.queryParams).then(response => {\r\n        this.volaTaskList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.currentStep = 0;\r\n      this.transferValue = [];\r\n      this.reset();\r\n    },\r\n    // 下一步\r\n    nextStep() {\r\n      if (this.currentStep === 0) {\r\n        // 验证第一步表单\r\n        this.$refs[\"form\"].validate(valid => {\r\n          if (valid) {\r\n            this.currentStep = 1;\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 上一步\r\n    prevStep() {\r\n      if (this.currentStep > 0) {\r\n        this.currentStep--;\r\n      }\r\n    },\r\n    // 获取规则名称\r\n    getRuleName(ruleId) {\r\n      const rule = this.allRules.find(r => r.key === ruleId);\r\n      return rule ? rule.label : '';\r\n    },\r\n    // 获取医疗机构列表\r\n    getMedicalInstitutionList() {\r\n      if (this.medicalInstitutionList.length > 0) {\r\n        return; // 如果已经有数据，不重复请求\r\n      }\r\n      this.medListLoading = true;\r\n      request({\r\n        url: '/dataAnalysis/volaTask/medList',\r\n        method: 'get'\r\n      }).then(response => {\r\n        if (response.code === 200) {\r\n          this.medicalInstitutionList = response.data || [];\r\n        } else {\r\n          this.$modal.msgError(response.msg || '获取医疗机构列表失败');\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取医疗机构列表失败:', error);\r\n        this.$modal.msgError('获取医疗机构列表失败');\r\n      }).finally(() => {\r\n        this.medListLoading = false;\r\n      });\r\n    },\r\n    // 搜索医疗机构\r\n    searchMedicalInstitutions(query) {\r\n      if (query !== '') {\r\n        this.medListLoading = true;\r\n        request({\r\n          url: '/dataAnalysis/volaTask/medList',\r\n          method: 'get',\r\n          params: { keyword: query }\r\n        }).then(response => {\r\n          if (response.code === 200) {\r\n            this.medicalInstitutionList = response.data || [];\r\n          }\r\n        }).catch(error => {\r\n          console.error('搜索医疗机构失败:', error);\r\n        }).finally(() => {\r\n          this.medListLoading = false;\r\n        });\r\n      } else {\r\n        this.getMedicalInstitutionList();\r\n      }\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.currentStep = 0;\r\n      this.transferValue = [];\r\n      this.form = {\r\n        volaTaskInfoId: null,\r\n        volaTaskInfoName: null,\r\n        medinsInfo: [], // 改为数组类型，支持多选\r\n        medinsLv: null,\r\n        medType: null,\r\n        setlTimeStart: null,\r\n        setlTimeEnd: null,\r\n        ruleIds: null,\r\n        ruleNames: null,\r\n        taskTime: null,\r\n        delFlag: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.volaTaskInfoId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加调度任务信息\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const volaTaskInfoId = row.volaTaskInfoId || this.ids\r\n      getVolaTask(volaTaskInfoId).then(response => {\r\n        this.form = response.data;\r\n        // 回显穿梭框数据\r\n        if (this.form.ruleIds) {\r\n          this.transferValue = this.form.ruleIds.split(',');\r\n        }\r\n        // 回显医疗机构数据\r\n        if (this.form.medinsInfo) {\r\n          // 如果是字符串，转换为数组\r\n          if (typeof this.form.medinsInfo === 'string') {\r\n            this.form.medinsInfo = this.form.medinsInfo.split(',');\r\n          }\r\n        } else {\r\n          this.form.medinsInfo = [];\r\n        }\r\n        this.open = true;\r\n        this.title = \"修改调度任务信息\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      // 处理穿梭框选中的规则\r\n      if (this.transferValue.length === 0) {\r\n        this.$modal.msgWarning(\"请至少选择一个检查规则\");\r\n        return;\r\n      }\r\n\r\n      // 处理医疗机构数据\r\n      if (!this.form.medinsInfo || this.form.medinsInfo.length === 0) {\r\n        this.$modal.msgWarning(\"请至少选择一个医疗机构\");\r\n        return;\r\n      }\r\n\r\n      // 创建提交数据的副本\r\n      const submitData = { ...this.form };\r\n\r\n      // 将医疗机构数组转换为逗号分隔的字符串\r\n      if (Array.isArray(submitData.medinsInfo)) {\r\n        submitData.medinsInfo = submitData.medinsInfo.join(',');\r\n      }\r\n\r\n      // 将选中的规则ID和名称设置到表单中\r\n      submitData.ruleIds = this.transferValue.join(',');\r\n      const selectedRuleNames = this.allRules\r\n        .filter(rule => this.transferValue.includes(rule.key))\r\n        .map(rule => rule.label)\r\n        .join(',');\r\n      submitData.ruleNames = selectedRuleNames;\r\n\r\n      // 设置当前时间为建立任务时间\r\n      submitData.taskTime = new Date().toISOString().split('T')[0];\r\n\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (submitData.volaTaskInfoId != null) {\r\n            updateVolaTask(submitData).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.currentStep = 0;\r\n              this.transferValue = [];\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addVolaTask(submitData).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.currentStep = 0;\r\n              this.transferValue = [];\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const volaTaskInfoIds = row.volaTaskInfoId || this.ids;\r\n      this.$modal.confirm('是否确认删除调度任务信息编号为\"' + volaTaskInfoIds + '\"的数据项？').then(function() {\r\n        return delVolaTask(volaTaskInfoIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('dataAnalysis/volaTask/export', {\r\n        ...this.queryParams\r\n      }, `volaTask_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 表格自适应样式 */\r\n.el-table {\r\n  width: 100%;\r\n}\r\n\r\n/* 确保表格在小屏幕上的显示 */\r\n@media (max-width: 768px) {\r\n  .el-table {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .el-table .cell {\r\n    padding-left: 5px;\r\n    padding-right: 5px;\r\n  }\r\n}\r\n\r\n/* 表格行高优化 */\r\n.el-table .el-table__row {\r\n  height: auto;\r\n}\r\n\r\n/* 操作按钮样式优化 */\r\n.el-table .el-button--mini {\r\n  margin: 0 2px;\r\n}\r\n\r\n/* 步骤条样式优化 */\r\n.custom-steps {\r\n  margin-bottom: 40px;\r\n  padding: 0 20px;\r\n}\r\n\r\n.custom-steps .el-step__title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.custom-steps .el-step__description {\r\n  font-size: 13px;\r\n  color: #909399;\r\n}\r\n\r\n/* 步骤内容区域 */\r\n.step-content {\r\n  min-height: 400px;\r\n  padding: 0 20px;\r\n}\r\n\r\n.step-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  padding-bottom: 20px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.step-header h3 {\r\n  font-size: 20px;\r\n  color: #303133;\r\n  margin: 0 0 8px 0;\r\n  font-weight: 600;\r\n}\r\n\r\n.step-header h3 i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n.step-header p {\r\n  color: #606266;\r\n  margin: 0;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.step-form {\r\n  max-width: 100%;\r\n}\r\n\r\n.form-section {\r\n  margin-bottom: 30px;\r\n  background: #fafafa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.section-title i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.step-form .el-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.step-form .el-form-item__label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n/* 穿梭框容器样式 */\r\n.transfer-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.transfer-tips {\r\n  width: 100%;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.transfer-wrapper {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.custom-transfer {\r\n  text-align: center;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel {\r\n  width: 280px;\r\n  height: 350px;\r\n  border-radius: 8px;\r\n  border: 1px solid #dcdfe6;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  border-radius: 8px 8px 0 0;\r\n  font-weight: 600;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__header .el-checkbox {\r\n  color: white;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__filter {\r\n  padding: 12px;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__list {\r\n  height: 240px;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__item {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__item:hover {\r\n  background: #f5f7fa;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__item i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n/* 已选规则摘要 */\r\n.selected-summary {\r\n  width: 100%;\r\n  max-width: 600px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n}\r\n\r\n.summary-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.summary-header i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.summary-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 4px;\r\n}\r\n\r\n/* 对话框底部按钮样式 */\r\n.dialog-footer {\r\n  text-align: right;\r\n  padding: 20px 24px;\r\n  background: #fafafa;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n  margin-left: 12px;\r\n  min-width: 80px;\r\n}\r\n\r\n/* 响应式优化 */\r\n@media (max-width: 768px) {\r\n  .step-content {\r\n    padding: 0 10px;\r\n  }\r\n\r\n  .custom-transfer .el-transfer-panel {\r\n    width: 240px;\r\n    height: 300px;\r\n  }\r\n\r\n  .form-section {\r\n    padding: 15px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAiaA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,aAAA;MACA;MACAC,QAAA,GACA;QAAAC,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,EACA;MACA;MACAC,sBAAA;MACA;MACAC,cAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,UAAA;QACAC,QAAA;QACAC,OAAA;QACAC,aAAA;QACAC,WAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAV,gBAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,UAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,aAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,WAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QAEA;QACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,iBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAArC,OAAA;MACA,IAAAsC,sBAAA,OAAArB,WAAA,EAAAsB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA/B,YAAA,GAAAkC,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAhC,KAAA,GAAAmC,QAAA,CAAAnC,KAAA;QACAgC,KAAA,CAAArC,OAAA;MACA;IACA;IACA;IACA0C,MAAA,WAAAA,OAAA;MACA,KAAAlC,IAAA;MACA,KAAAC,WAAA;MACA,KAAAE,aAAA;MACA,KAAAgC,KAAA;IACA;IACA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,SAAApC,WAAA;QACA;QACA,KAAAqC,KAAA,SAAAC,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACAH,MAAA,CAAApC,WAAA;UACA;QACA;MACA;IACA;IACA;IACAwC,QAAA,WAAAA,SAAA;MACA,SAAAxC,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IACA;IACAyC,WAAA,WAAAA,YAAAC,MAAA;MACA,IAAAC,IAAA,QAAAxC,QAAA,CAAAyC,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAzC,GAAA,KAAAsC,MAAA;MAAA;MACA,OAAAC,IAAA,GAAAA,IAAA,CAAAtC,KAAA;IACA;IACA;IACAyC,yBAAA,WAAAA,0BAAA;MAAA,IAAAC,MAAA;MACA,SAAAzC,sBAAA,CAAA0C,MAAA;QACA;MACA;MACA,KAAAzC,cAAA;MACA,IAAA0C,gBAAA;QACAC,GAAA;QACAC,MAAA;MACA,GAAArB,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAqB,IAAA;UACAL,MAAA,CAAAzC,sBAAA,GAAAyB,QAAA,CAAAzC,IAAA;QACA;UACAyD,MAAA,CAAAM,MAAA,CAAAC,QAAA,CAAAvB,QAAA,CAAAwB,GAAA;QACA;MACA,GAAAC,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,gBAAAA,KAAA;QACAV,MAAA,CAAAM,MAAA,CAAAC,QAAA;MACA,GAAAK,OAAA;QACAZ,MAAA,CAAAxC,cAAA;MACA;IACA;IACA;IACAqD,yBAAA,WAAAA,0BAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,KAAA;QACA,KAAAtD,cAAA;QACA,IAAA0C,gBAAA;UACAC,GAAA;UACAC,MAAA;UACAY,MAAA;YAAAC,OAAA,EAAAH;UAAA;QACA,GAAA/B,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAqB,IAAA;YACAU,MAAA,CAAAxD,sBAAA,GAAAyB,QAAA,CAAAzC,IAAA;UACA;QACA,GAAAkE,KAAA,WAAAC,KAAA;UACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA,GAAAE,OAAA;UACAG,MAAA,CAAAvD,cAAA;QACA;MACA;QACA,KAAAuC,yBAAA;MACA;IACA;IACA;IACAZ,KAAA,WAAAA,MAAA;MACA,KAAAlC,WAAA;MACA,KAAAE,aAAA;MACA,KAAAkB,IAAA;QACA6C,cAAA;QACAtD,gBAAA;QACAC,UAAA;QAAA;QACAC,QAAA;QACAC,OAAA;QACAC,aAAA;QACAC,WAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACA+C,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAjE,WAAA,CAAAC,OAAA;MACA,KAAAiB,OAAA;IACA;IACA,aACAgD,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApF,GAAA,GAAAoF,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAb,cAAA;MAAA;MACA,KAAAxE,MAAA,GAAAmF,SAAA,CAAA5B,MAAA;MACA,KAAAtD,QAAA,IAAAkF,SAAA,CAAA5B,MAAA;IACA;IACA,aACA+B,SAAA,WAAAA,UAAA;MACA,KAAA7C,KAAA;MACA,KAAAnC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAkF,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAhD,KAAA;MACA,IAAA+B,cAAA,GAAAgB,GAAA,CAAAhB,cAAA,SAAAzE,GAAA;MACA,IAAA2F,qBAAA,EAAAlB,cAAA,EAAAnC,IAAA,WAAAC,QAAA;QACAmD,MAAA,CAAA9D,IAAA,GAAAW,QAAA,CAAAzC,IAAA;QACA;QACA,IAAA4F,MAAA,CAAA9D,IAAA,CAAAH,OAAA;UACAiE,MAAA,CAAAhF,aAAA,GAAAgF,MAAA,CAAA9D,IAAA,CAAAH,OAAA,CAAAmE,KAAA;QACA;QACA;QACA,IAAAF,MAAA,CAAA9D,IAAA,CAAAR,UAAA;UACA;UACA,WAAAsE,MAAA,CAAA9D,IAAA,CAAAR,UAAA;YACAsE,MAAA,CAAA9D,IAAA,CAAAR,UAAA,GAAAsE,MAAA,CAAA9D,IAAA,CAAAR,UAAA,CAAAwE,KAAA;UACA;QACA;UACAF,MAAA,CAAA9D,IAAA,CAAAR,UAAA;QACA;QACAsE,MAAA,CAAAnF,IAAA;QACAmF,MAAA,CAAApF,KAAA;MACA;IACA;IACA,WACAuF,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAApF,aAAA,CAAA8C,MAAA;QACA,KAAAK,MAAA,CAAAkC,UAAA;QACA;MACA;;MAEA;MACA,UAAAnE,IAAA,CAAAR,UAAA,SAAAQ,IAAA,CAAAR,UAAA,CAAAoC,MAAA;QACA,KAAAK,MAAA,CAAAkC,UAAA;QACA;MACA;;MAEA;MACA,IAAAC,UAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAtE,IAAA;;MAEA;MACA,IAAAuE,KAAA,CAAAC,OAAA,CAAAJ,UAAA,CAAA5E,UAAA;QACA4E,UAAA,CAAA5E,UAAA,GAAA4E,UAAA,CAAA5E,UAAA,CAAAiF,IAAA;MACA;;MAEA;MACAL,UAAA,CAAAvE,OAAA,QAAAf,aAAA,CAAA2F,IAAA;MACA,IAAAC,iBAAA,QAAA3F,QAAA,CACA4F,MAAA,WAAApD,IAAA;QAAA,OAAA2C,MAAA,CAAApF,aAAA,CAAA8F,QAAA,CAAArD,IAAA,CAAAvC,GAAA;MAAA,GACAyE,GAAA,WAAAlC,IAAA;QAAA,OAAAA,IAAA,CAAAtC,KAAA;MAAA,GACAwF,IAAA;MACAL,UAAA,CAAAtE,SAAA,GAAA4E,iBAAA;;MAEA;MACAN,UAAA,CAAArE,QAAA,OAAA8E,IAAA,GAAAC,WAAA,GAAAd,KAAA;MAEA,KAAA/C,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAiD,UAAA,CAAAvB,cAAA;YACA,IAAAkC,wBAAA,EAAAX,UAAA,EAAA1D,IAAA,WAAAC,QAAA;cACAuD,MAAA,CAAAjC,MAAA,CAAA+C,UAAA;cACAd,MAAA,CAAAvF,IAAA;cACAuF,MAAA,CAAAtF,WAAA;cACAsF,MAAA,CAAApF,aAAA;cACAoF,MAAA,CAAA5D,OAAA;YACA;UACA;YACA,IAAA2E,qBAAA,EAAAb,UAAA,EAAA1D,IAAA,WAAAC,QAAA;cACAuD,MAAA,CAAAjC,MAAA,CAAA+C,UAAA;cACAd,MAAA,CAAAvF,IAAA;cACAuF,MAAA,CAAAtF,WAAA;cACAsF,MAAA,CAAApF,aAAA;cACAoF,MAAA,CAAA5D,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA4E,YAAA,WAAAA,aAAArB,GAAA;MAAA,IAAAsB,MAAA;MACA,IAAAC,eAAA,GAAAvB,GAAA,CAAAhB,cAAA,SAAAzE,GAAA;MACA,KAAA6D,MAAA,CAAAoD,OAAA,sBAAAD,eAAA,aAAA1E,IAAA;QACA,WAAA4E,qBAAA,EAAAF,eAAA;MACA,GAAA1E,IAAA;QACAyE,MAAA,CAAA7E,OAAA;QACA6E,MAAA,CAAAlD,MAAA,CAAA+C,UAAA;MACA,GAAA5C,KAAA;IACA;IACA,aACAmD,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,qCAAAnB,cAAA,CAAAC,OAAA,MACA,KAAAlF,WAAA,eAAAqG,MAAA,CACA,IAAAZ,IAAA,GAAAa,OAAA;IACA;EACA;AACA", "ignoreList": []}]}