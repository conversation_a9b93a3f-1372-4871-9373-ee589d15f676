{"remainingRequest": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\dataAnalysis\\volaTask\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\dataAnalysis\\volaTask\\index.vue", "mtime": 1750391709735}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\babel.config.js", "mtime": 1748414362378}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_volaTask", "require", "_request", "_interopRequireDefault", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "volaTaskList", "title", "open", "currentStep", "transferData", "transferValue", "allRules", "key", "label", "medicalInstitutionList", "medListLoading", "queryParams", "pageNum", "pageSize", "volaTaskInfoName", "medinsInfo", "medinsLv", "medType", "setlTimeStart", "setlTimeEnd", "ruleIds", "ruleNames", "taskTime", "form", "rules", "required", "message", "trigger", "settlementTimeRange", "created", "getList", "methods", "_this", "listVolaTask", "then", "response", "rows", "cancel", "reset", "nextStep", "_this2", "$refs", "validate", "valid", "prevStep", "getRuleName", "ruleId", "rule", "find", "r", "getMedicalInstitutionList", "_this3", "length", "request", "url", "method", "code", "$modal", "msgError", "msg", "catch", "error", "console", "finally", "searchMedicalInstitutions", "query", "_this4", "params", "keyword", "volaTaskInfoId", "delFlag", "createBy", "createTime", "updateBy", "updateTime", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "handleAdd", "handleUpdate", "row", "_this5", "getVolaTask", "split", "submitForm", "_this6", "msgWarning", "submitData", "_objectSpread2", "default", "Array", "isArray", "join", "selectedRuleNames", "filter", "includes", "Date", "toISOString", "updateVolaTask", "msgSuccess", "addVolaTask", "handleDelete", "_this7", "volaTaskInfoIds", "confirm", "delVolaTask", "handleExport", "download", "concat", "getTime"], "sources": ["src/views/dataAnalysis/volaTask/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\r\n      <el-form-item label=\"调度任务名称\" prop=\"volaTaskInfoName\">\r\n        <el-input\r\n          v-model=\"queryParams.volaTaskInfoName\"\r\n          placeholder=\"请输入调度任务名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"医疗机构等级\" prop=\"medinsLv\">\r\n        <el-input\r\n          v-model=\"queryParams.medinsLv\"\r\n          placeholder=\"请输入医疗机构等级\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"结算开始时间\" prop=\"setlTimeStart\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.setlTimeStart\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择结算开始时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"结算结束时间\" prop=\"setlTimeEnd\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.setlTimeEnd\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择结算结束时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"规则ID集合\" prop=\"ruleIds\">\r\n        <el-input\r\n          v-model=\"queryParams.ruleIds\"\r\n          placeholder=\"请输入规则ID集合\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"建立任务时间\" prop=\"taskTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.taskTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择建立任务时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"volaTaskList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      fit\r\n      table-layout=\"auto\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column\r\n        label=\"调度任务名称\"\r\n        align=\"center\"\r\n        prop=\"volaTaskInfoName\"\r\n        min-width=\"150\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"筛查医疗机构集合\"\r\n        align=\"center\"\r\n        prop=\"medinsInfo\"\r\n        min-width=\"180\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"医疗机构等级\"\r\n        align=\"center\"\r\n        prop=\"medinsLv\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"医疗类别\"\r\n        align=\"center\"\r\n        prop=\"medType\"\r\n        width=\"100\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"结算开始时间\"\r\n        align=\"center\"\r\n        prop=\"setlTimeStart\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.setlTimeStart, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"结算结束时间\"\r\n        align=\"center\"\r\n        prop=\"setlTimeEnd\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.setlTimeEnd, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"规则ID集合\"\r\n        align=\"center\"\r\n        prop=\"ruleIds\"\r\n        min-width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"规则名称集合\"\r\n        align=\"center\"\r\n        prop=\"ruleNames\"\r\n        min-width=\"150\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"建立任务时间\"\r\n        align=\"center\"\r\n        prop=\"taskTime\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.taskTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"备注\"\r\n        align=\"center\"\r\n        prop=\"remark\"\r\n        min-width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        width=\"120\"\r\n        fixed=\"right\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['dataAnalysis:volaTask:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['dataAnalysis:volaTask:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改调度任务信息对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"1200px\" append-to-body>\r\n      <!-- 步骤条 -->\r\n      <el-steps\r\n        :active=\"currentStep\"\r\n        finish-status=\"success\"\r\n        align-center\r\n        class=\"custom-steps\">\r\n        <el-step title=\"基本信息\" description=\"填写调度任务基本信息\">\r\n          <i slot=\"icon\" class=\"el-icon-edit-outline\"></i>\r\n        </el-step>\r\n        <el-step title=\"规则选择\" description=\"选择检查规则\">\r\n          <i slot=\"icon\" class=\"el-icon-setting\"></i>\r\n        </el-step>\r\n      </el-steps>\r\n\r\n      <!-- 第一步：基本信息 -->\r\n      <div v-show=\"currentStep === 0\" class=\"step-content\">\r\n        <div class=\"step-header\">\r\n          <h3><i class=\"el-icon-edit-outline\"></i> 基本信息</h3>\r\n          <p>请填写调度任务的基本信息</p>\r\n        </div>\r\n\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"140px\" class=\"step-form\">\r\n          <div class=\"form-section\">\r\n            <div class=\"section-title\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>任务信息</span>\r\n            </div>\r\n            <el-row :gutter=\"24\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"调度任务名称\" prop=\"volaTaskInfoName\">\r\n                  <el-input\r\n                    v-model=\"form.volaTaskInfoName\"\r\n                    placeholder=\"请输入调度任务名称\"\r\n                    prefix-icon=\"el-icon-edit\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"医疗机构等级\" prop=\"medinsLv\">\r\n                  <el-select\r\n                    v-model=\"form.medinsLv\"\r\n                    placeholder=\"请选择医疗机构等级\"\r\n                    clearable\r\n                    style=\"width: 100%\">\r\n                    <el-option\r\n                      v-for=\"dict in dict.type.legent_lv\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.label\"\r\n                      :value=\"dict.value\">\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n\r\n            <el-form-item label=\"筛查医疗机构集合\" prop=\"medinsInfo\">\r\n              <el-select\r\n                v-model=\"form.medinsInfo\"\r\n                multiple\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                placeholder=\"请选择筛查医疗机构\"\r\n                :remote-method=\"searchMedicalInstitutions\"\r\n                :loading=\"medListLoading\"\r\n                style=\"width: 100%\"\r\n                @focus=\"getMedicalInstitutionList\">\r\n                <el-option\r\n                  v-for=\"item in medicalInstitutionList\"\r\n                  :key=\"item.fixmedinsCode\"\r\n                  :label=\"item.fixmedinsName\"\r\n                  :value=\"item.fixmedinsCode\">\r\n                  <span style=\"float: left\">{{ item.fixmedinsName }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ item.fixmedinsCode }}</span>\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <div class=\"form-section\">\r\n            <div class=\"section-title\">\r\n              <i class=\"el-icon-date\"></i>\r\n              <span>结算时间</span>\r\n            </div>\r\n            <el-form-item label=\"结算时间范围\" prop=\"settlementTimeRange\">\r\n              <el-date-picker\r\n                v-model=\"form.settlementTimeRange\"\r\n                type=\"datetimerange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期时间\"\r\n                end-placeholder=\"结束日期时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                format=\"yyyy-MM-dd HH:mm:ss\"\r\n                :default-time=\"['00:00:00', '23:59:59']\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 第二步：规则选择 -->\r\n      <div v-show=\"currentStep === 1\" class=\"step-content\">\r\n        <div class=\"step-header\">\r\n          <h3><i class=\"el-icon-setting\"></i> 规则选择</h3>\r\n          <p>请选择需要应用的检查规则</p>\r\n        </div>\r\n\r\n        <div class=\"transfer-container\">\r\n          <div class=\"transfer-tips\">\r\n            <el-alert\r\n              title=\"操作提示\"\r\n              type=\"info\"\r\n              :closable=\"false\"\r\n              show-icon>\r\n              <div slot=\"description\">\r\n                <p>• 从左侧选择需要的检查规则，点击右箭头添加到已选规则</p>\r\n                <p>• 支持搜索功能，可快速定位所需规则</p>\r\n                <p>• 至少需要选择一个规则才能完成创建</p>\r\n              </div>\r\n            </el-alert>\r\n          </div>\r\n\r\n          <div class=\"transfer-wrapper\">\r\n            <el-transfer\r\n              v-model=\"transferValue\"\r\n              :data=\"allRules\"\r\n              :titles=\"['可选规则', '已选规则']\"\r\n              :button-texts=\"['移除', '添加']\"\r\n              :format=\"{\r\n                noChecked: '共 ${total} 项',\r\n                hasChecked: '已选 ${checked}/${total} 项'\r\n              }\"\r\n              filterable\r\n              filter-placeholder=\"搜索规则名称\"\r\n              :props=\"{\r\n                key: 'key',\r\n                label: 'label'\r\n              }\"\r\n              class=\"enhanced-transfer\">\r\n              <template slot-scope=\"{ option }\">\r\n                <div class=\"transfer-item\">\r\n                  <div class=\"item-icon\">\r\n                    <i class=\"el-icon-document-checked\"></i>\r\n                  </div>\r\n                  <div class=\"item-content\">\r\n                    <div class=\"item-title\">{{ option.label.split('：')[0] }}</div>\r\n                    <div class=\"item-description\">{{ option.label.split('：')[1] || '' }}</div>\r\n                  </div>\r\n                </div>\r\n              </template>\r\n            </el-transfer>\r\n          </div>\r\n\r\n          <div class=\"selected-summary\" v-if=\"transferValue.length > 0\">\r\n            <div class=\"summary-header\">\r\n              <i class=\"el-icon-success\"></i>\r\n              <span>已选择 {{ transferValue.length }} 个规则</span>\r\n            </div>\r\n            <div class=\"summary-tags\">\r\n              <el-tag\r\n                v-for=\"ruleId in transferValue\"\r\n                :key=\"ruleId\"\r\n                type=\"success\"\r\n                size=\"small\"\r\n                style=\"margin: 2px 4px 2px 0;\">\r\n                {{ getRuleName(ruleId) }}\r\n              </el-tag>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n        <el-button v-if=\"currentStep > 0\" @click=\"prevStep\">上一步</el-button>\r\n        <el-button v-if=\"currentStep < 1\" type=\"primary\" @click=\"nextStep\">下一步</el-button>\r\n        <el-button v-if=\"currentStep === 1\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listVolaTask, getVolaTask, delVolaTask, addVolaTask, updateVolaTask } from \"@/api/dataAnalysis/volaTask\";\r\nimport request from '@/utils/request';\r\n\r\nexport default {\r\n  name: \"VolaTask\",\r\n  dicts: ['legent_lv'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 调度任务信息表格数据\r\n      volaTaskList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 当前步骤\r\n      currentStep: 0,\r\n      // 穿梭框数据\r\n      transferData: [],\r\n      // 穿梭框选中的值\r\n      transferValue: [],\r\n      // 所有可选规则列表\r\n      allRules: [\r\n        { key: '1', label: '规则1：医保目录外用药检查' },\r\n        { key: '2', label: '规则2：超量用药检查' },\r\n        { key: '3', label: '规则3：重复用药检查' },\r\n        { key: '4', label: '规则4：配伍禁忌检查' },\r\n        { key: '5', label: '规则5：适应症检查' },\r\n        { key: '6', label: '规则6：诊疗项目合理性检查' },\r\n        { key: '7', label: '规则7：医疗服务价格检查' },\r\n        { key: '8', label: '规则8：住院天数合理性检查' }\r\n      ],\r\n      // 医疗机构列表\r\n      medicalInstitutionList: [],\r\n      // 医疗机构列表加载状态\r\n      medListLoading: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        volaTaskInfoName: null,\r\n        medinsInfo: null,\r\n        medinsLv: null,\r\n        medType: null,\r\n        setlTimeStart: null,\r\n        setlTimeEnd: null,\r\n        ruleIds: null,\r\n        ruleNames: null,\r\n        taskTime: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        volaTaskInfoName: [\r\n          { required: true, message: \"调度任务名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        medinsInfo: [\r\n          { required: true, message: \"筛查医疗机构集合不能为空\", trigger: \"blur\" }\r\n        ],\r\n        settlementTimeRange: [\r\n          { required: true, message: \"结算时间范围不能为空\", trigger: \"change\" }\r\n        ],\r\n        // 注意：ruleIds、ruleNames、taskTime 由穿梭框和系统自动生成，不需要在第一步验证\r\n        // delFlag、createBy、createTime、updateBy、updateTime 由系统自动处理，不需要验证\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询调度任务信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listVolaTask(this.queryParams).then(response => {\r\n        this.volaTaskList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.currentStep = 0;\r\n      this.transferValue = [];\r\n      this.reset();\r\n    },\r\n    // 下一步\r\n    nextStep() {\r\n      if (this.currentStep === 0) {\r\n        // 验证第一步表单\r\n        this.$refs[\"form\"].validate(valid => {\r\n          if (valid) {\r\n            this.currentStep = 1;\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 上一步\r\n    prevStep() {\r\n      if (this.currentStep > 0) {\r\n        this.currentStep--;\r\n      }\r\n    },\r\n    // 获取规则名称\r\n    getRuleName(ruleId) {\r\n      const rule = this.allRules.find(r => r.key === ruleId);\r\n      return rule ? rule.label : '';\r\n    },\r\n    // 获取医疗机构列表\r\n    getMedicalInstitutionList() {\r\n      if (this.medicalInstitutionList.length > 0) {\r\n        return; // 如果已经有数据，不重复请求\r\n      }\r\n      this.medListLoading = true;\r\n      request({\r\n        url: '/dataAnalysis/volaTask/medList',\r\n        method: 'get'\r\n      }).then(response => {\r\n        if (response.code === 200) {\r\n          this.medicalInstitutionList = response.data || [];\r\n        } else {\r\n          this.$modal.msgError(response.msg || '获取医疗机构列表失败');\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取医疗机构列表失败:', error);\r\n        this.$modal.msgError('获取医疗机构列表失败');\r\n      }).finally(() => {\r\n        this.medListLoading = false;\r\n      });\r\n    },\r\n    // 搜索医疗机构\r\n    searchMedicalInstitutions(query) {\r\n      if (query !== '') {\r\n        this.medListLoading = true;\r\n        request({\r\n          url: '/dataAnalysis/volaTask/medList',\r\n          method: 'get',\r\n          params: { keyword: query }\r\n        }).then(response => {\r\n          if (response.code === 200) {\r\n            this.medicalInstitutionList = response.data || [];\r\n          }\r\n        }).catch(error => {\r\n          console.error('搜索医疗机构失败:', error);\r\n        }).finally(() => {\r\n          this.medListLoading = false;\r\n        });\r\n      } else {\r\n        this.getMedicalInstitutionList();\r\n      }\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.currentStep = 0;\r\n      this.transferValue = [];\r\n      this.form = {\r\n        volaTaskInfoId: null,\r\n        volaTaskInfoName: null,\r\n        medinsInfo: [], // 改为数组类型，支持多选\r\n        medinsLv: null,\r\n        medType: null,\r\n        setlTimeStart: null,\r\n        setlTimeEnd: null,\r\n        settlementTimeRange: [], // 新增时间范围字段\r\n        ruleIds: null,\r\n        ruleNames: null,\r\n        taskTime: null,\r\n        delFlag: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.volaTaskInfoId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加调度任务信息\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const volaTaskInfoId = row.volaTaskInfoId || this.ids\r\n      getVolaTask(volaTaskInfoId).then(response => {\r\n        this.form = response.data;\r\n        // 回显穿梭框数据\r\n        if (this.form.ruleIds) {\r\n          this.transferValue = this.form.ruleIds.split(',');\r\n        }\r\n        // 回显医疗机构数据\r\n        if (this.form.medinsInfo) {\r\n          // 如果是字符串，转换为数组\r\n          if (typeof this.form.medinsInfo === 'string') {\r\n            this.form.medinsInfo = this.form.medinsInfo.split(',');\r\n          }\r\n        } else {\r\n          this.form.medinsInfo = [];\r\n        }\r\n        // 回显时间范围数据\r\n        if (this.form.setlTimeStart && this.form.setlTimeEnd) {\r\n          this.form.settlementTimeRange = [this.form.setlTimeStart, this.form.setlTimeEnd];\r\n        } else {\r\n          this.form.settlementTimeRange = [];\r\n        }\r\n        this.open = true;\r\n        this.title = \"修改调度任务信息\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      // 处理穿梭框选中的规则\r\n      if (this.transferValue.length === 0) {\r\n        this.$modal.msgWarning(\"请至少选择一个检查规则\");\r\n        return;\r\n      }\r\n\r\n      // 处理医疗机构数据\r\n      if (!this.form.medinsInfo || this.form.medinsInfo.length === 0) {\r\n        this.$modal.msgWarning(\"请至少选择一个医疗机构\");\r\n        return;\r\n      }\r\n\r\n      // 处理时间范围数据\r\n      if (!this.form.settlementTimeRange || this.form.settlementTimeRange.length !== 2) {\r\n        this.$modal.msgWarning(\"请选择结算时间范围\");\r\n        return;\r\n      }\r\n\r\n      // 创建提交数据的副本\r\n      const submitData = { ...this.form };\r\n\r\n      // 将医疗机构数组转换为逗号分隔的字符串\r\n      if (Array.isArray(submitData.medinsInfo)) {\r\n        submitData.medinsInfo = submitData.medinsInfo.join(',');\r\n      }\r\n\r\n      // 处理时间范围数据\r\n      if (submitData.settlementTimeRange && submitData.settlementTimeRange.length === 2) {\r\n        submitData.setlTimeStart = submitData.settlementTimeRange[0];\r\n        submitData.setlTimeEnd = submitData.settlementTimeRange[1];\r\n      }\r\n      // 移除时间范围字段，因为后端不需要这个字段\r\n      delete submitData.settlementTimeRange;\r\n\r\n      // 将选中的规则ID和名称设置到表单中\r\n      submitData.ruleIds = this.transferValue.join(',');\r\n      const selectedRuleNames = this.allRules\r\n        .filter(rule => this.transferValue.includes(rule.key))\r\n        .map(rule => rule.label)\r\n        .join(',');\r\n      submitData.ruleNames = selectedRuleNames;\r\n\r\n      // 设置当前时间为建立任务时间\r\n      submitData.taskTime = new Date().toISOString().split('T')[0];\r\n\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (submitData.volaTaskInfoId != null) {\r\n            updateVolaTask(submitData).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.currentStep = 0;\r\n              this.transferValue = [];\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addVolaTask(submitData).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.currentStep = 0;\r\n              this.transferValue = [];\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const volaTaskInfoIds = row.volaTaskInfoId || this.ids;\r\n      this.$modal.confirm('是否确认删除调度任务信息编号为\"' + volaTaskInfoIds + '\"的数据项？').then(function() {\r\n        return delVolaTask(volaTaskInfoIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('dataAnalysis/volaTask/export', {\r\n        ...this.queryParams\r\n      }, `volaTask_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 表格自适应样式 */\r\n.el-table {\r\n  width: 100%;\r\n}\r\n\r\n/* 确保表格在小屏幕上的显示 */\r\n@media (max-width: 768px) {\r\n  .el-table {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .el-table .cell {\r\n    padding-left: 5px;\r\n    padding-right: 5px;\r\n  }\r\n}\r\n\r\n/* 表格行高优化 */\r\n.el-table .el-table__row {\r\n  height: auto;\r\n}\r\n\r\n/* 操作按钮样式优化 */\r\n.el-table .el-button--mini {\r\n  margin: 0 2px;\r\n}\r\n\r\n/* 步骤条样式优化 */\r\n.custom-steps {\r\n  margin-bottom: 40px;\r\n  padding: 0 20px;\r\n}\r\n\r\n.custom-steps .el-step__title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.custom-steps .el-step__description {\r\n  font-size: 13px;\r\n  color: #909399;\r\n}\r\n\r\n/* 步骤内容区域 */\r\n.step-content {\r\n  min-height: 400px;\r\n  padding: 0 20px;\r\n}\r\n\r\n.step-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  padding-bottom: 20px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.step-header h3 {\r\n  font-size: 20px;\r\n  color: #303133;\r\n  margin: 0 0 8px 0;\r\n  font-weight: 600;\r\n}\r\n\r\n.step-header h3 i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n.step-header p {\r\n  color: #606266;\r\n  margin: 0;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.step-form {\r\n  max-width: 100%;\r\n}\r\n\r\n.form-section {\r\n  margin-bottom: 30px;\r\n  background: #fafafa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.section-title i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.step-form .el-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.step-form .el-form-item__label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n/* 穿梭框容器样式 */\r\n.transfer-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.transfer-tips {\r\n  width: 100%;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.transfer-wrapper {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.custom-transfer {\r\n  text-align: center;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel {\r\n  width: 280px;\r\n  height: 350px;\r\n  border-radius: 8px;\r\n  border: 1px solid #dcdfe6;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  border-radius: 8px 8px 0 0;\r\n  font-weight: 600;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__header .el-checkbox {\r\n  color: white;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__filter {\r\n  padding: 12px;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__list {\r\n  height: 240px;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__item {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__item:hover {\r\n  background: #f5f7fa;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__item i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n/* 已选规则摘要 */\r\n.selected-summary {\r\n  width: 100%;\r\n  max-width: 600px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n}\r\n\r\n.summary-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.summary-header i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.summary-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 4px;\r\n}\r\n\r\n/* 对话框底部按钮样式 */\r\n.dialog-footer {\r\n  text-align: right;\r\n  padding: 20px 24px;\r\n  background: #fafafa;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n  margin-left: 12px;\r\n  min-width: 80px;\r\n}\r\n\r\n/* 响应式优化 */\r\n@media (max-width: 768px) {\r\n  .step-content {\r\n    padding: 0 10px;\r\n  }\r\n\r\n  .custom-transfer .el-transfer-panel {\r\n    width: 240px;\r\n    height: 300px;\r\n  }\r\n\r\n  .form-section {\r\n    padding: 15px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AA6ZA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,aAAA;MACA;MACAC,QAAA,GACA;QAAAC,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,EACA;MACA;MACAC,sBAAA;MACA;MACAC,cAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,UAAA;QACAC,QAAA;QACAC,OAAA;QACAC,aAAA;QACAC,WAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAV,gBAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,UAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,mBAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QAEA;QACA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,iBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAtC,OAAA;MACA,IAAAuC,sBAAA,OAAAtB,WAAA,EAAAuB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAhC,YAAA,GAAAmC,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAjC,KAAA,GAAAoC,QAAA,CAAApC,KAAA;QACAiC,KAAA,CAAAtC,OAAA;MACA;IACA;IACA;IACA2C,MAAA,WAAAA,OAAA;MACA,KAAAnC,IAAA;MACA,KAAAC,WAAA;MACA,KAAAE,aAAA;MACA,KAAAiC,KAAA;IACA;IACA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,SAAArC,WAAA;QACA;QACA,KAAAsC,KAAA,SAAAC,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACAH,MAAA,CAAArC,WAAA;UACA;QACA;MACA;IACA;IACA;IACAyC,QAAA,WAAAA,SAAA;MACA,SAAAzC,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IACA;IACA0C,WAAA,WAAAA,YAAAC,MAAA;MACA,IAAAC,IAAA,QAAAzC,QAAA,CAAA0C,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA1C,GAAA,KAAAuC,MAAA;MAAA;MACA,OAAAC,IAAA,GAAAA,IAAA,CAAAvC,KAAA;IACA;IACA;IACA0C,yBAAA,WAAAA,0BAAA;MAAA,IAAAC,MAAA;MACA,SAAA1C,sBAAA,CAAA2C,MAAA;QACA;MACA;MACA,KAAA1C,cAAA;MACA,IAAA2C,gBAAA;QACAC,GAAA;QACAC,MAAA;MACA,GAAArB,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAqB,IAAA;UACAL,MAAA,CAAA1C,sBAAA,GAAA0B,QAAA,CAAA1C,IAAA;QACA;UACA0D,MAAA,CAAAM,MAAA,CAAAC,QAAA,CAAAvB,QAAA,CAAAwB,GAAA;QACA;MACA,GAAAC,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,gBAAAA,KAAA;QACAV,MAAA,CAAAM,MAAA,CAAAC,QAAA;MACA,GAAAK,OAAA;QACAZ,MAAA,CAAAzC,cAAA;MACA;IACA;IACA;IACAsD,yBAAA,WAAAA,0BAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,KAAA;QACA,KAAAvD,cAAA;QACA,IAAA2C,gBAAA;UACAC,GAAA;UACAC,MAAA;UACAY,MAAA;YAAAC,OAAA,EAAAH;UAAA;QACA,GAAA/B,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAqB,IAAA;YACAU,MAAA,CAAAzD,sBAAA,GAAA0B,QAAA,CAAA1C,IAAA;UACA;QACA,GAAAmE,KAAA,WAAAC,KAAA;UACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA,GAAAE,OAAA;UACAG,MAAA,CAAAxD,cAAA;QACA;MACA;QACA,KAAAwC,yBAAA;MACA;IACA;IACA;IACAZ,KAAA,WAAAA,MAAA;MACA,KAAAnC,WAAA;MACA,KAAAE,aAAA;MACA,KAAAkB,IAAA;QACA8C,cAAA;QACAvD,gBAAA;QACAC,UAAA;QAAA;QACAC,QAAA;QACAC,OAAA;QACAC,aAAA;QACAC,WAAA;QACAS,mBAAA;QAAA;QACAR,OAAA;QACAC,SAAA;QACAC,QAAA;QACAgD,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAlE,WAAA,CAAAC,OAAA;MACA,KAAAkB,OAAA;IACA;IACA,aACAgD,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAArF,GAAA,GAAAqF,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAb,cAAA;MAAA;MACA,KAAAzE,MAAA,GAAAoF,SAAA,CAAA5B,MAAA;MACA,KAAAvD,QAAA,IAAAmF,SAAA,CAAA5B,MAAA;IACA;IACA,aACA+B,SAAA,WAAAA,UAAA;MACA,KAAA7C,KAAA;MACA,KAAApC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAmF,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAhD,KAAA;MACA,IAAA+B,cAAA,GAAAgB,GAAA,CAAAhB,cAAA,SAAA1E,GAAA;MACA,IAAA4F,qBAAA,EAAAlB,cAAA,EAAAnC,IAAA,WAAAC,QAAA;QACAmD,MAAA,CAAA/D,IAAA,GAAAY,QAAA,CAAA1C,IAAA;QACA;QACA,IAAA6F,MAAA,CAAA/D,IAAA,CAAAH,OAAA;UACAkE,MAAA,CAAAjF,aAAA,GAAAiF,MAAA,CAAA/D,IAAA,CAAAH,OAAA,CAAAoE,KAAA;QACA;QACA;QACA,IAAAF,MAAA,CAAA/D,IAAA,CAAAR,UAAA;UACA;UACA,WAAAuE,MAAA,CAAA/D,IAAA,CAAAR,UAAA;YACAuE,MAAA,CAAA/D,IAAA,CAAAR,UAAA,GAAAuE,MAAA,CAAA/D,IAAA,CAAAR,UAAA,CAAAyE,KAAA;UACA;QACA;UACAF,MAAA,CAAA/D,IAAA,CAAAR,UAAA;QACA;QACA;QACA,IAAAuE,MAAA,CAAA/D,IAAA,CAAAL,aAAA,IAAAoE,MAAA,CAAA/D,IAAA,CAAAJ,WAAA;UACAmE,MAAA,CAAA/D,IAAA,CAAAK,mBAAA,IAAA0D,MAAA,CAAA/D,IAAA,CAAAL,aAAA,EAAAoE,MAAA,CAAA/D,IAAA,CAAAJ,WAAA;QACA;UACAmE,MAAA,CAAA/D,IAAA,CAAAK,mBAAA;QACA;QACA0D,MAAA,CAAApF,IAAA;QACAoF,MAAA,CAAArF,KAAA;MACA;IACA;IACA,WACAwF,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAArF,aAAA,CAAA+C,MAAA;QACA,KAAAK,MAAA,CAAAkC,UAAA;QACA;MACA;;MAEA;MACA,UAAApE,IAAA,CAAAR,UAAA,SAAAQ,IAAA,CAAAR,UAAA,CAAAqC,MAAA;QACA,KAAAK,MAAA,CAAAkC,UAAA;QACA;MACA;;MAEA;MACA,UAAApE,IAAA,CAAAK,mBAAA,SAAAL,IAAA,CAAAK,mBAAA,CAAAwB,MAAA;QACA,KAAAK,MAAA,CAAAkC,UAAA;QACA;MACA;;MAEA;MACA,IAAAC,UAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAvE,IAAA;;MAEA;MACA,IAAAwE,KAAA,CAAAC,OAAA,CAAAJ,UAAA,CAAA7E,UAAA;QACA6E,UAAA,CAAA7E,UAAA,GAAA6E,UAAA,CAAA7E,UAAA,CAAAkF,IAAA;MACA;;MAEA;MACA,IAAAL,UAAA,CAAAhE,mBAAA,IAAAgE,UAAA,CAAAhE,mBAAA,CAAAwB,MAAA;QACAwC,UAAA,CAAA1E,aAAA,GAAA0E,UAAA,CAAAhE,mBAAA;QACAgE,UAAA,CAAAzE,WAAA,GAAAyE,UAAA,CAAAhE,mBAAA;MACA;MACA;MACA,OAAAgE,UAAA,CAAAhE,mBAAA;;MAEA;MACAgE,UAAA,CAAAxE,OAAA,QAAAf,aAAA,CAAA4F,IAAA;MACA,IAAAC,iBAAA,QAAA5F,QAAA,CACA6F,MAAA,WAAApD,IAAA;QAAA,OAAA2C,MAAA,CAAArF,aAAA,CAAA+F,QAAA,CAAArD,IAAA,CAAAxC,GAAA;MAAA,GACA0E,GAAA,WAAAlC,IAAA;QAAA,OAAAA,IAAA,CAAAvC,KAAA;MAAA,GACAyF,IAAA;MACAL,UAAA,CAAAvE,SAAA,GAAA6E,iBAAA;;MAEA;MACAN,UAAA,CAAAtE,QAAA,OAAA+E,IAAA,GAAAC,WAAA,GAAAd,KAAA;MAEA,KAAA/C,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAiD,UAAA,CAAAvB,cAAA;YACA,IAAAkC,wBAAA,EAAAX,UAAA,EAAA1D,IAAA,WAAAC,QAAA;cACAuD,MAAA,CAAAjC,MAAA,CAAA+C,UAAA;cACAd,MAAA,CAAAxF,IAAA;cACAwF,MAAA,CAAAvF,WAAA;cACAuF,MAAA,CAAArF,aAAA;cACAqF,MAAA,CAAA5D,OAAA;YACA;UACA;YACA,IAAA2E,qBAAA,EAAAb,UAAA,EAAA1D,IAAA,WAAAC,QAAA;cACAuD,MAAA,CAAAjC,MAAA,CAAA+C,UAAA;cACAd,MAAA,CAAAxF,IAAA;cACAwF,MAAA,CAAAvF,WAAA;cACAuF,MAAA,CAAArF,aAAA;cACAqF,MAAA,CAAA5D,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA4E,YAAA,WAAAA,aAAArB,GAAA;MAAA,IAAAsB,MAAA;MACA,IAAAC,eAAA,GAAAvB,GAAA,CAAAhB,cAAA,SAAA1E,GAAA;MACA,KAAA8D,MAAA,CAAAoD,OAAA,sBAAAD,eAAA,aAAA1E,IAAA;QACA,WAAA4E,qBAAA,EAAAF,eAAA;MACA,GAAA1E,IAAA;QACAyE,MAAA,CAAA7E,OAAA;QACA6E,MAAA,CAAAlD,MAAA,CAAA+C,UAAA;MACA,GAAA5C,KAAA;IACA;IACA,aACAmD,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,qCAAAnB,cAAA,CAAAC,OAAA,MACA,KAAAnF,WAAA,eAAAsG,MAAA,CACA,IAAAZ,IAAA,GAAAa,OAAA;IACA;EACA;AACA", "ignoreList": []}]}