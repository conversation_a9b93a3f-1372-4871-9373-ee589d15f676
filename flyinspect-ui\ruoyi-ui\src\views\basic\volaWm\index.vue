<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="药品目录名称" prop="regName">
        <el-input
          v-model="queryParams.regName"
          placeholder="请输入药品目录名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最大年龄" prop="maxAge">
        <el-input
          v-model="queryParams.maxAge"
          placeholder="请输入最大年龄"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最小年龄" prop="minAge">
        <el-input
          v-model="queryParams.minAge"
          placeholder="请输入最小年龄"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医疗机构等级" prop="medinsLv">
        <el-input
          v-model="queryParams.medinsLv"
          placeholder="请输入医疗机构等级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="有效标志" prop="valiFlag">
        <el-input
          v-model="queryParams.valiFlag"
          placeholder="请输入有效标志"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['basic:volaWm:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['basic:volaWm:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['basic:volaWm:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['basic:volaWm:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="volaWmList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="药品目录编码" align="center" prop="medListCodg" />
      <el-table-column label="药品目录名称" align="center" prop="regName" />
      <el-table-column label="最大年龄" align="center" prop="maxAge" />
      <el-table-column label="最小年龄" align="center" prop="minAge" />
      <el-table-column label="医疗类别" align="center" prop="medType" />
      <el-table-column label="医疗机构等级" align="center" prop="medinsLv" />
      <el-table-column label="诊断" align="center" prop="diag" />
      <el-table-column label="违规说明" align="center" prop="natHiDruglistMemo" />
      <el-table-column label="有效标志" align="center" prop="valiFlag" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['basic:volaWm:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['basic:volaWm:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改国家药品目录违规药品信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="药品目录名称" prop="regName">
          <el-input v-model="form.regName" placeholder="请输入药品目录名称" />
        </el-form-item>
        <el-form-item label="最大年龄" prop="maxAge">
          <el-input v-model="form.maxAge" placeholder="请输入最大年龄" />
        </el-form-item>
        <el-form-item label="最小年龄" prop="minAge">
          <el-input v-model="form.minAge" placeholder="请输入最小年龄" />
        </el-form-item>
        <el-form-item label="医疗机构等级" prop="medinsLv">
          <el-input v-model="form.medinsLv" placeholder="请输入医疗机构等级" />
        </el-form-item>
        <el-form-item label="诊断" prop="diag">
          <el-input v-model="form.diag" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="违规说明" prop="natHiDruglistMemo">
          <el-input v-model="form.natHiDruglistMemo" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="有效标志" prop="valiFlag">
          <el-input v-model="form.valiFlag" placeholder="请输入有效标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listVolaWm, getVolaWm, delVolaWm, addVolaWm, updateVolaWm } from "@/api/basic/volaWm";

export default {
  name: "VolaWm",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 国家药品目录违规药品信息表格数据
      volaWmList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        regName: null,
        maxAge: null,
        minAge: null,
        medType: null,
        medinsLv: null,
        diag: null,
        natHiDruglistMemo: null,
        valiFlag: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询国家药品目录违规药品信息列表 */
    getList() {
      this.loading = true;
      listVolaWm(this.queryParams).then(response => {
        this.volaWmList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        medListCodg: null,
        regName: null,
        maxAge: null,
        minAge: null,
        medType: null,
        medinsLv: null,
        diag: null,
        natHiDruglistMemo: null,
        valiFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.medListCodg)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加国家药品目录违规药品信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const medListCodg = row.medListCodg || this.ids
      getVolaWm(medListCodg).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改国家药品目录违规药品信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.medListCodg != null) {
            updateVolaWm(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addVolaWm(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const medListCodgs = row.medListCodg || this.ids;
      this.$modal.confirm('是否确认删除国家药品目录违规药品信息编号为"' + medListCodgs + '"的数据项？').then(function() {
        return delVolaWm(medListCodgs);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('basic/volaWm/export', {
        ...this.queryParams
      }, `volaWm_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
