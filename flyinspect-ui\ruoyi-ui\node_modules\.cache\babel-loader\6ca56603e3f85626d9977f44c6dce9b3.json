{"remainingRequest": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\basic\\volaData\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\basic\\volaData\\index.vue", "mtime": 1750383268000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\babel.config.js", "mtime": 1748414362378}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_volaData", "require", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "volaDataList", "title", "open", "queryParams", "pageNum", "pageSize", "mdtrtId", "setlId", "fixmedinsCode", "fixmedinsName", "psnNo", "CERTNO", "psnName", "GEND", "AGE", "fundPaySumamt", "acctPay", "cashPayamt", "medType", "setlTime", "medinsListCodg", "medinsListName", "hilistCode", "hilistName", "prodeptName", "SPEC", "dosformName", "UNIT", "CNT", "PRIC", "detItemFeeSumamt", "fulamtOwnpayAmt", "preselfpayAmt", "inscpAmt", "selfpayProp", "overlmtSelfpay", "chrgitmLv", "deptName", "diseNo", "diseName", "ruleName", "valaFlag", "volaType", "volaDesc", "volaAccor", "DESCRIBET", "volaAmt", "crterName", "crteTime", "updtName", "updtTime", "form", "rules", "required", "message", "trigger", "created", "getList", "methods", "_this", "listVolaData", "then", "response", "rows", "cancel", "reset", "setlFeeInfoId", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getVolaData", "submitForm", "_this3", "$refs", "validate", "valid", "updateVolaData", "$modal", "msgSuccess", "addVolaData", "handleDelete", "_this4", "setlFeeInfoIds", "confirm", "delVolaData", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/basic/volaData/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"就诊ID\" prop=\"mdtrtId\">\r\n        <el-input\r\n          v-model=\"queryParams.mdtrtId\"\r\n          placeholder=\"请输入就诊ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"结算ID\" prop=\"setlId\">\r\n        <el-input\r\n          v-model=\"queryParams.setlId\"\r\n          placeholder=\"请输入结算ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"定点医药机构编号\" prop=\"fixmedinsCode\">\r\n        <el-input\r\n          v-model=\"queryParams.fixmedinsCode\"\r\n          placeholder=\"请输入定点医药机构编号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"定点医药机构名称\" prop=\"fixmedinsName\">\r\n        <el-input\r\n          v-model=\"queryParams.fixmedinsName\"\r\n          placeholder=\"请输入定点医药机构名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"人员编号\" prop=\"psnNo\">\r\n        <el-input\r\n          v-model=\"queryParams.psnNo\"\r\n          placeholder=\"请输入人员编号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"人员姓名\" prop=\"psnName\">\r\n        <el-input\r\n          v-model=\"queryParams.psnName\"\r\n          placeholder=\"请输入人员姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"性别\" prop=\"GEND\">\r\n        <el-input\r\n          v-model=\"queryParams.GEND\"\r\n          placeholder=\"请输入性别\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"年龄\" prop=\"AGE\">\r\n        <el-input\r\n          v-model=\"queryParams.AGE\"\r\n          placeholder=\"请输入年龄\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"基金支付总额\" prop=\"fundPaySumamt\">\r\n        <el-input\r\n          v-model=\"queryParams.fundPaySumamt\"\r\n          placeholder=\"请输入基金支付总额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"个人账户支出\" prop=\"acctPay\">\r\n        <el-input\r\n          v-model=\"queryParams.acctPay\"\r\n          placeholder=\"请输入个人账户支出\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"现金支付金额\" prop=\"cashPayamt\">\r\n        <el-input\r\n          v-model=\"queryParams.cashPayamt\"\r\n          placeholder=\"请输入现金支付金额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"结算时间\" prop=\"setlTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.setlTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择结算时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"医药机构目录编码\" prop=\"medinsListCodg\">\r\n        <el-input\r\n          v-model=\"queryParams.medinsListCodg\"\r\n          placeholder=\"请输入医药机构目录编码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"医药机构目录名称\" prop=\"medinsListName\">\r\n        <el-input\r\n          v-model=\"queryParams.medinsListName\"\r\n          placeholder=\"请输入医药机构目录名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"医保目录编码\" prop=\"hilistCode\">\r\n        <el-input\r\n          v-model=\"queryParams.hilistCode\"\r\n          placeholder=\"请输入医保目录编码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"医保目录名称\" prop=\"hilistName\">\r\n        <el-input\r\n          v-model=\"queryParams.hilistName\"\r\n          placeholder=\"请输入医保目录名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"药品厂家\" prop=\"prodeptName\">\r\n        <el-input\r\n          v-model=\"queryParams.prodeptName\"\r\n          placeholder=\"请输入药品厂家\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"规格\" prop=\"SPEC\">\r\n        <el-input\r\n          v-model=\"queryParams.SPEC\"\r\n          placeholder=\"请输入规格\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"剂型名称\" prop=\"dosformName\">\r\n        <el-input\r\n          v-model=\"queryParams.dosformName\"\r\n          placeholder=\"请输入剂型名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"单位\" prop=\"UNIT\">\r\n        <el-input\r\n          v-model=\"queryParams.UNIT\"\r\n          placeholder=\"请输入单位\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"数量\" prop=\"CNT\">\r\n        <el-input\r\n          v-model=\"queryParams.CNT\"\r\n          placeholder=\"请输入数量\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"单价\" prop=\"PRIC\">\r\n        <el-input\r\n          v-model=\"queryParams.PRIC\"\r\n          placeholder=\"请输入单价\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"明细项目费用总额\" prop=\"detItemFeeSumamt\">\r\n        <el-input\r\n          v-model=\"queryParams.detItemFeeSumamt\"\r\n          placeholder=\"请输入明细项目费用总额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"全自费金额\" prop=\"fulamtOwnpayAmt\">\r\n        <el-input\r\n          v-model=\"queryParams.fulamtOwnpayAmt\"\r\n          placeholder=\"请输入全自费金额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"先行自付金额\" prop=\"preselfpayAmt\">\r\n        <el-input\r\n          v-model=\"queryParams.preselfpayAmt\"\r\n          placeholder=\"请输入先行自付金额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"符合范围金额\" prop=\"inscpAmt\">\r\n        <el-input\r\n          v-model=\"queryParams.inscpAmt\"\r\n          placeholder=\"请输入符合范围金额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"自付比例\" prop=\"selfpayProp\">\r\n        <el-input\r\n          v-model=\"queryParams.selfpayProp\"\r\n          placeholder=\"请输入自付比例\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"超限价自费费用\" prop=\"overlmtSelfpay\">\r\n        <el-input\r\n          v-model=\"queryParams.overlmtSelfpay\"\r\n          placeholder=\"请输入超限价自费费用\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"收费项目等级\" prop=\"chrgitmLv\">\r\n        <el-input\r\n          v-model=\"queryParams.chrgitmLv\"\r\n          placeholder=\"请输入收费项目等级\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"科室名称\" prop=\"deptName\">\r\n        <el-input\r\n          v-model=\"queryParams.deptName\"\r\n          placeholder=\"请输入科室名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"病种编号\" prop=\"diseNo\">\r\n        <el-input\r\n          v-model=\"queryParams.diseNo\"\r\n          placeholder=\"请输入病种编号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"有效标志\" prop=\"valaFlag\">\r\n        <el-input\r\n          v-model=\"queryParams.valaFlag\"\r\n          placeholder=\"请输入有效标志\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"疑似违规金额\" prop=\"volaAmt\">\r\n        <el-input\r\n          v-model=\"queryParams.volaAmt\"\r\n          placeholder=\"请输入疑似违规金额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"创建人姓名\" prop=\"crterName\">\r\n        <el-input\r\n          v-model=\"queryParams.crterName\"\r\n          placeholder=\"请输入创建人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"数据创建时间\" prop=\"crteTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.crteTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择数据创建时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"更新人姓名\" prop=\"updtName\">\r\n        <el-input\r\n          v-model=\"queryParams.updtName\"\r\n          placeholder=\"请输入更新人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"数据更新时间\" prop=\"updtTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.updtTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择数据更新时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['basic:volaData:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['basic:volaData:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['basic:volaData:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['basic:volaData:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"volaDataList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"结算明细ID\" align=\"center\" prop=\"setlFeeInfoId\" />\r\n      <el-table-column label=\"就诊ID\" align=\"center\" prop=\"mdtrtId\" />\r\n      <el-table-column label=\"结算ID\" align=\"center\" prop=\"setlId\" />\r\n      <el-table-column label=\"定点医药机构编号\" align=\"center\" prop=\"fixmedinsCode\" />\r\n      <el-table-column label=\"定点医药机构名称\" align=\"center\" prop=\"fixmedinsName\" />\r\n      <el-table-column label=\"人员编号\" align=\"center\" prop=\"psnNo\" />\r\n      <el-table-column label=\"证件号码\" align=\"center\" prop=\"CERTNO\" />\r\n      <el-table-column label=\"人员姓名\" align=\"center\" prop=\"psnName\" />\r\n      <el-table-column label=\"性别\" align=\"center\" prop=\"GEND\" />\r\n      <el-table-column label=\"年龄\" align=\"center\" prop=\"AGE\" />\r\n      <el-table-column label=\"基金支付总额\" align=\"center\" prop=\"fundPaySumamt\" />\r\n      <el-table-column label=\"个人账户支出\" align=\"center\" prop=\"acctPay\" />\r\n      <el-table-column label=\"现金支付金额\" align=\"center\" prop=\"cashPayamt\" />\r\n      <el-table-column label=\"医疗类别\" align=\"center\" prop=\"medType\" />\r\n      <el-table-column label=\"结算时间\" align=\"center\" prop=\"setlTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.setlTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"医药机构目录编码\" align=\"center\" prop=\"medinsListCodg\" />\r\n      <el-table-column label=\"医药机构目录名称\" align=\"center\" prop=\"medinsListName\" />\r\n      <el-table-column label=\"医保目录编码\" align=\"center\" prop=\"hilistCode\" />\r\n      <el-table-column label=\"医保目录名称\" align=\"center\" prop=\"hilistName\" />\r\n      <el-table-column label=\"药品厂家\" align=\"center\" prop=\"prodeptName\" />\r\n      <el-table-column label=\"规格\" align=\"center\" prop=\"SPEC\" />\r\n      <el-table-column label=\"剂型名称\" align=\"center\" prop=\"dosformName\" />\r\n      <el-table-column label=\"单位\" align=\"center\" prop=\"UNIT\" />\r\n      <el-table-column label=\"数量\" align=\"center\" prop=\"CNT\" />\r\n      <el-table-column label=\"单价\" align=\"center\" prop=\"PRIC\" />\r\n      <el-table-column label=\"明细项目费用总额\" align=\"center\" prop=\"detItemFeeSumamt\" />\r\n      <el-table-column label=\"全自费金额\" align=\"center\" prop=\"fulamtOwnpayAmt\" />\r\n      <el-table-column label=\"先行自付金额\" align=\"center\" prop=\"preselfpayAmt\" />\r\n      <el-table-column label=\"符合范围金额\" align=\"center\" prop=\"inscpAmt\" />\r\n      <el-table-column label=\"自付比例\" align=\"center\" prop=\"selfpayProp\" />\r\n      <el-table-column label=\"超限价自费费用\" align=\"center\" prop=\"overlmtSelfpay\" />\r\n      <el-table-column label=\"收费项目等级\" align=\"center\" prop=\"chrgitmLv\" />\r\n      <el-table-column label=\"科室名称\" align=\"center\" prop=\"deptName\" />\r\n      <el-table-column label=\"病种编号\" align=\"center\" prop=\"diseNo\" />\r\n      <el-table-column label=\"病种名称\" align=\"center\" prop=\"diseName\" />\r\n      <el-table-column label=\"规则名称\" align=\"center\" prop=\"ruleName\" />\r\n      <el-table-column label=\"有效标志\" align=\"center\" prop=\"valaFlag\" />\r\n      <el-table-column label=\"违规类型\" align=\"center\" prop=\"volaType\" />\r\n      <el-table-column label=\"违规描述\" align=\"center\" prop=\"volaDesc\" />\r\n      <el-table-column label=\"违规依据\" align=\"center\" prop=\"volaAccor\" />\r\n      <el-table-column label=\"问题描述\" align=\"center\" prop=\"DESCRIBET\" />\r\n      <el-table-column label=\"疑似违规金额\" align=\"center\" prop=\"volaAmt\" />\r\n      <el-table-column label=\"创建人姓名\" align=\"center\" prop=\"crterName\" />\r\n      <el-table-column label=\"数据创建时间\" align=\"center\" prop=\"crteTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.crteTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"更新人姓名\" align=\"center\" prop=\"updtName\" />\r\n      <el-table-column label=\"数据更新时间\" align=\"center\" prop=\"updtTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.updtTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['basic:volaData:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['basic:volaData:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改违规信息对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"就诊ID\" prop=\"mdtrtId\">\r\n          <el-input v-model=\"form.mdtrtId\" placeholder=\"请输入就诊ID\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"结算ID\" prop=\"setlId\">\r\n          <el-input v-model=\"form.setlId\" placeholder=\"请输入结算ID\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"定点医药机构编号\" prop=\"fixmedinsCode\">\r\n          <el-input v-model=\"form.fixmedinsCode\" placeholder=\"请输入定点医药机构编号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"定点医药机构名称\" prop=\"fixmedinsName\">\r\n          <el-input v-model=\"form.fixmedinsName\" placeholder=\"请输入定点医药机构名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"人员编号\" prop=\"psnNo\">\r\n          <el-input v-model=\"form.psnNo\" placeholder=\"请输入人员编号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"证件号码\" prop=\"CERTNO\">\r\n          <el-input v-model=\"form.CERTNO\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"人员姓名\" prop=\"psnName\">\r\n          <el-input v-model=\"form.psnName\" placeholder=\"请输入人员姓名\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"性别\" prop=\"GEND\">\r\n          <el-input v-model=\"form.GEND\" placeholder=\"请输入性别\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"年龄\" prop=\"AGE\">\r\n          <el-input v-model=\"form.AGE\" placeholder=\"请输入年龄\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"基金支付总额\" prop=\"fundPaySumamt\">\r\n          <el-input v-model=\"form.fundPaySumamt\" placeholder=\"请输入基金支付总额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"个人账户支出\" prop=\"acctPay\">\r\n          <el-input v-model=\"form.acctPay\" placeholder=\"请输入个人账户支出\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"现金支付金额\" prop=\"cashPayamt\">\r\n          <el-input v-model=\"form.cashPayamt\" placeholder=\"请输入现金支付金额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"结算时间\" prop=\"setlTime\">\r\n          <el-date-picker clearable\r\n            v-model=\"form.setlTime\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择结算时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"医药机构目录编码\" prop=\"medinsListCodg\">\r\n          <el-input v-model=\"form.medinsListCodg\" placeholder=\"请输入医药机构目录编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"医药机构目录名称\" prop=\"medinsListName\">\r\n          <el-input v-model=\"form.medinsListName\" placeholder=\"请输入医药机构目录名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"医保目录编码\" prop=\"hilistCode\">\r\n          <el-input v-model=\"form.hilistCode\" placeholder=\"请输入医保目录编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"医保目录名称\" prop=\"hilistName\">\r\n          <el-input v-model=\"form.hilistName\" placeholder=\"请输入医保目录名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"药品厂家\" prop=\"prodeptName\">\r\n          <el-input v-model=\"form.prodeptName\" placeholder=\"请输入药品厂家\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"规格\" prop=\"SPEC\">\r\n          <el-input v-model=\"form.SPEC\" placeholder=\"请输入规格\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"剂型名称\" prop=\"dosformName\">\r\n          <el-input v-model=\"form.dosformName\" placeholder=\"请输入剂型名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"单位\" prop=\"UNIT\">\r\n          <el-input v-model=\"form.UNIT\" placeholder=\"请输入单位\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"数量\" prop=\"CNT\">\r\n          <el-input v-model=\"form.CNT\" placeholder=\"请输入数量\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"单价\" prop=\"PRIC\">\r\n          <el-input v-model=\"form.PRIC\" placeholder=\"请输入单价\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"明细项目费用总额\" prop=\"detItemFeeSumamt\">\r\n          <el-input v-model=\"form.detItemFeeSumamt\" placeholder=\"请输入明细项目费用总额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"全自费金额\" prop=\"fulamtOwnpayAmt\">\r\n          <el-input v-model=\"form.fulamtOwnpayAmt\" placeholder=\"请输入全自费金额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"先行自付金额\" prop=\"preselfpayAmt\">\r\n          <el-input v-model=\"form.preselfpayAmt\" placeholder=\"请输入先行自付金额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"符合范围金额\" prop=\"inscpAmt\">\r\n          <el-input v-model=\"form.inscpAmt\" placeholder=\"请输入符合范围金额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"自付比例\" prop=\"selfpayProp\">\r\n          <el-input v-model=\"form.selfpayProp\" placeholder=\"请输入自付比例\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"超限价自费费用\" prop=\"overlmtSelfpay\">\r\n          <el-input v-model=\"form.overlmtSelfpay\" placeholder=\"请输入超限价自费费用\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"收费项目等级\" prop=\"chrgitmLv\">\r\n          <el-input v-model=\"form.chrgitmLv\" placeholder=\"请输入收费项目等级\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"科室名称\" prop=\"deptName\">\r\n          <el-input v-model=\"form.deptName\" placeholder=\"请输入科室名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"病种编号\" prop=\"diseNo\">\r\n          <el-input v-model=\"form.diseNo\" placeholder=\"请输入病种编号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"病种名称\" prop=\"diseName\">\r\n          <el-input v-model=\"form.diseName\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"规则名称\" prop=\"ruleName\">\r\n          <el-input v-model=\"form.ruleName\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"有效标志\" prop=\"valaFlag\">\r\n          <el-input v-model=\"form.valaFlag\" placeholder=\"请输入有效标志\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"违规描述\" prop=\"volaDesc\">\r\n          <el-input v-model=\"form.volaDesc\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"违规依据\" prop=\"volaAccor\">\r\n          <el-input v-model=\"form.volaAccor\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"问题描述\" prop=\"DESCRIBET\">\r\n          <el-input v-model=\"form.DESCRIBET\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"疑似违规金额\" prop=\"volaAmt\">\r\n          <el-input v-model=\"form.volaAmt\" placeholder=\"请输入疑似违规金额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"创建人姓名\" prop=\"crterName\">\r\n          <el-input v-model=\"form.crterName\" placeholder=\"请输入创建人姓名\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"数据创建时间\" prop=\"crteTime\">\r\n          <el-date-picker clearable\r\n            v-model=\"form.crteTime\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择数据创建时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"更新人姓名\" prop=\"updtName\">\r\n          <el-input v-model=\"form.updtName\" placeholder=\"请输入更新人姓名\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"数据更新时间\" prop=\"updtTime\">\r\n          <el-date-picker clearable\r\n            v-model=\"form.updtTime\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择数据更新时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listVolaData, getVolaData, delVolaData, addVolaData, updateVolaData } from \"@/api/basic/volaData\";\r\n\r\nexport default {\r\n  name: \"VolaData\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 违规信息表格数据\r\n      volaDataList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        mdtrtId: null,\r\n        setlId: null,\r\n        fixmedinsCode: null,\r\n        fixmedinsName: null,\r\n        psnNo: null,\r\n        CERTNO: null,\r\n        psnName: null,\r\n        GEND: null,\r\n        AGE: null,\r\n        fundPaySumamt: null,\r\n        acctPay: null,\r\n        cashPayamt: null,\r\n        medType: null,\r\n        setlTime: null,\r\n        medinsListCodg: null,\r\n        medinsListName: null,\r\n        hilistCode: null,\r\n        hilistName: null,\r\n        prodeptName: null,\r\n        SPEC: null,\r\n        dosformName: null,\r\n        UNIT: null,\r\n        CNT: null,\r\n        PRIC: null,\r\n        detItemFeeSumamt: null,\r\n        fulamtOwnpayAmt: null,\r\n        preselfpayAmt: null,\r\n        inscpAmt: null,\r\n        selfpayProp: null,\r\n        overlmtSelfpay: null,\r\n        chrgitmLv: null,\r\n        deptName: null,\r\n        diseNo: null,\r\n        diseName: null,\r\n        ruleName: null,\r\n        valaFlag: null,\r\n        volaType: null,\r\n        volaDesc: null,\r\n        volaAccor: null,\r\n        DESCRIBET: null,\r\n        volaAmt: null,\r\n        crterName: null,\r\n        crteTime: null,\r\n        updtName: null,\r\n        updtTime: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        mdtrtId: [\r\n          { required: true, message: \"就诊ID不能为空\", trigger: \"blur\" }\r\n        ],\r\n        setlId: [\r\n          { required: true, message: \"结算ID不能为空\", trigger: \"blur\" }\r\n        ],\r\n        fixmedinsCode: [\r\n          { required: true, message: \"定点医药机构编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        fixmedinsName: [\r\n          { required: true, message: \"定点医药机构名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        psnNo: [\r\n          { required: true, message: \"人员编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        CERTNO: [\r\n          { required: true, message: \"证件号码不能为空\", trigger: \"blur\" }\r\n        ],\r\n        psnName: [\r\n          { required: true, message: \"人员姓名不能为空\", trigger: \"blur\" }\r\n        ],\r\n        fundPaySumamt: [\r\n          { required: true, message: \"基金支付总额不能为空\", trigger: \"blur\" }\r\n        ],\r\n        acctPay: [\r\n          { required: true, message: \"个人账户支出不能为空\", trigger: \"blur\" }\r\n        ],\r\n        cashPayamt: [\r\n          { required: true, message: \"现金支付金额不能为空\", trigger: \"blur\" }\r\n        ],\r\n        medType: [\r\n          { required: true, message: \"医疗类别不能为空\", trigger: \"change\" }\r\n        ],\r\n        setlTime: [\r\n          { required: true, message: \"结算时间不能为空\", trigger: \"blur\" }\r\n        ],\r\n        hilistCode: [\r\n          { required: true, message: \"医保目录编码不能为空\", trigger: \"blur\" }\r\n        ],\r\n        hilistName: [\r\n          { required: true, message: \"医保目录名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        CNT: [\r\n          { required: true, message: \"数量不能为空\", trigger: \"blur\" }\r\n        ],\r\n        PRIC: [\r\n          { required: true, message: \"单价不能为空\", trigger: \"blur\" }\r\n        ],\r\n        detItemFeeSumamt: [\r\n          { required: true, message: \"明细项目费用总额不能为空\", trigger: \"blur\" }\r\n        ],\r\n        valaFlag: [\r\n          { required: true, message: \"有效标志不能为空\", trigger: \"blur\" }\r\n        ],\r\n        volaType: [\r\n          { required: true, message: \"违规类型不能为空\", trigger: \"change\" }\r\n        ],\r\n        crteTime: [\r\n          { required: true, message: \"数据创建时间不能为空\", trigger: \"blur\" }\r\n        ],\r\n        updtTime: [\r\n          { required: true, message: \"数据更新时间不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询违规信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listVolaData(this.queryParams).then(response => {\r\n        this.volaDataList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        setlFeeInfoId: null,\r\n        mdtrtId: null,\r\n        setlId: null,\r\n        fixmedinsCode: null,\r\n        fixmedinsName: null,\r\n        psnNo: null,\r\n        CERTNO: null,\r\n        psnName: null,\r\n        GEND: null,\r\n        AGE: null,\r\n        fundPaySumamt: null,\r\n        acctPay: null,\r\n        cashPayamt: null,\r\n        medType: null,\r\n        setlTime: null,\r\n        medinsListCodg: null,\r\n        medinsListName: null,\r\n        hilistCode: null,\r\n        hilistName: null,\r\n        prodeptName: null,\r\n        SPEC: null,\r\n        dosformName: null,\r\n        UNIT: null,\r\n        CNT: null,\r\n        PRIC: null,\r\n        detItemFeeSumamt: null,\r\n        fulamtOwnpayAmt: null,\r\n        preselfpayAmt: null,\r\n        inscpAmt: null,\r\n        selfpayProp: null,\r\n        overlmtSelfpay: null,\r\n        chrgitmLv: null,\r\n        deptName: null,\r\n        diseNo: null,\r\n        diseName: null,\r\n        ruleName: null,\r\n        valaFlag: null,\r\n        volaType: null,\r\n        volaDesc: null,\r\n        volaAccor: null,\r\n        DESCRIBET: null,\r\n        volaAmt: null,\r\n        crterName: null,\r\n        crteTime: null,\r\n        updtName: null,\r\n        updtTime: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.setlFeeInfoId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加违规信息\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const setlFeeInfoId = row.setlFeeInfoId || this.ids\r\n      getVolaData(setlFeeInfoId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改违规信息\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.setlFeeInfoId != null) {\r\n            updateVolaData(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addVolaData(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const setlFeeInfoIds = row.setlFeeInfoId || this.ids;\r\n      this.$modal.confirm('是否确认删除违规信息编号为\"' + setlFeeInfoIds + '\"的数据项？').then(function() {\r\n        return delVolaData(setlFeeInfoIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('basic/volaData/export', {\r\n        ...this.queryParams\r\n      }, `volaData_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AAolBA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA;QACAC,MAAA;QACAC,aAAA;QACAC,aAAA;QACAC,KAAA;QACAC,MAAA;QACAC,OAAA;QACAC,IAAA;QACAC,GAAA;QACAC,aAAA;QACAC,OAAA;QACAC,UAAA;QACAC,OAAA;QACAC,QAAA;QACAC,cAAA;QACAC,cAAA;QACAC,UAAA;QACAC,UAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;QACAC,IAAA;QACAC,GAAA;QACAC,IAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,aAAA;QACAC,QAAA;QACAC,WAAA;QACAC,cAAA;QACAC,SAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACA9C,OAAA,GACA;UAAA+C,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhD,MAAA,GACA;UAAA8C,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA/C,aAAA,GACA;UAAA6C,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA9C,aAAA,GACA;UAAA4C,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA7C,KAAA,GACA;UAAA2C,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA5C,MAAA,GACA;UAAA0C,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA3C,OAAA,GACA;UAAAyC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAxC,aAAA,GACA;UAAAsC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAvC,OAAA,GACA;UAAAqC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAtC,UAAA,GACA;UAAAoC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACArC,OAAA,GACA;UAAAmC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACApC,QAAA,GACA;UAAAkC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAjC,UAAA,GACA;UAAA+B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhC,UAAA,GACA;UAAA8B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA3B,GAAA,GACA;UAAAyB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA1B,IAAA,GACA;UAAAwB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAzB,gBAAA,GACA;UAAAuB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,QAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,QAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,QAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAjE,OAAA;MACA,IAAAkE,sBAAA,OAAAzD,WAAA,EAAA0D,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA3D,YAAA,GAAA8D,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAA5D,KAAA,GAAA+D,QAAA,CAAA/D,KAAA;QACA4D,KAAA,CAAAjE,OAAA;MACA;IACA;IACA;IACAsE,MAAA,WAAAA,OAAA;MACA,KAAA9D,IAAA;MACA,KAAA+D,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAd,IAAA;QACAe,aAAA;QACA5D,OAAA;QACAC,MAAA;QACAC,aAAA;QACAC,aAAA;QACAC,KAAA;QACAC,MAAA;QACAC,OAAA;QACAC,IAAA;QACAC,GAAA;QACAC,aAAA;QACAC,OAAA;QACAC,UAAA;QACAC,OAAA;QACAC,QAAA;QACAC,cAAA;QACAC,cAAA;QACAC,UAAA;QACAC,UAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;QACAC,IAAA;QACAC,GAAA;QACAC,IAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,aAAA;QACAC,QAAA;QACAC,WAAA;QACAC,cAAA;QACAC,SAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACA,KAAAiB,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAjE,WAAA,CAAAC,OAAA;MACA,KAAAqD,OAAA;IACA;IACA,aACAY,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA5E,GAAA,GAAA4E,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAP,aAAA;MAAA;MACA,KAAAtE,MAAA,GAAA2E,SAAA,CAAAG,MAAA;MACA,KAAA7E,QAAA,IAAA0E,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAV,KAAA;MACA,KAAA/D,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA2E,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAb,KAAA;MACA,IAAAC,aAAA,GAAAW,GAAA,CAAAX,aAAA,SAAAvE,GAAA;MACA,IAAAoF,qBAAA,EAAAb,aAAA,EAAAL,IAAA,WAAAC,QAAA;QACAgB,MAAA,CAAA3B,IAAA,GAAAW,QAAA,CAAArE,IAAA;QACAqF,MAAA,CAAA5E,IAAA;QACA4E,MAAA,CAAA7E,KAAA;MACA;IACA;IACA,WACA+E,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA9B,IAAA,CAAAe,aAAA;YACA,IAAAmB,wBAAA,EAAAJ,MAAA,CAAA9B,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAmB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA/E,IAAA;cACA+E,MAAA,CAAAxB,OAAA;YACA;UACA;YACA,IAAA+B,qBAAA,EAAAP,MAAA,CAAA9B,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAmB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA/E,IAAA;cACA+E,MAAA,CAAAxB,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAgC,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,cAAA,GAAAd,GAAA,CAAAX,aAAA,SAAAvE,GAAA;MACA,KAAA2F,MAAA,CAAAM,OAAA,oBAAAD,cAAA,aAAA9B,IAAA;QACA,WAAAgC,qBAAA,EAAAF,cAAA;MACA,GAAA9B,IAAA;QACA6B,MAAA,CAAAjC,OAAA;QACAiC,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,8BAAAC,cAAA,CAAAC,OAAA,MACA,KAAA/F,WAAA,eAAAgG,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}