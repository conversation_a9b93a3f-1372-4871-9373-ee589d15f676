import request from '@/utils/request'

// 查询规则信息列表
export function listRuleInfo(query) {
  return request({
    url: '/basic/ruleInfo/list',
    method: 'get',
    params: query
  })
}

// 查询规则信息详细
export function getRuleInfo(ruleId) {
  return request({
    url: '/basic/ruleInfo/' + ruleId,
    method: 'get'
  })
}

// 新增规则信息
export function addRuleInfo(data) {
  return request({
    url: '/basic/ruleInfo',
    method: 'post',
    data: data
  })
}

// 修改规则信息
export function updateRuleInfo(data) {
  return request({
    url: '/basic/ruleInfo',
    method: 'put',
    data: data
  })
}

// 删除规则信息
export function delRuleInfo(ruleId) {
  return request({
    url: '/basic/ruleInfo/' + ruleId,
    method: 'delete'
  })
}
