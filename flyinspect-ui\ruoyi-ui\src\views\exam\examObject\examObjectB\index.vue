<template>
  <div class="app-container">
    <ry-breadcrumb :list="breadList" />
    <card>
      <search-form v-show="showSearch" :modelValue="queryParams" :fields="searchFields" :show-search="showSearch"
        @search="handleQuery" @reset="resetQuery" @update:model-value="val => queryParams = val" />
    </card>
    <card class="mt10">
      <el-row :gutter="10" class="mb8" style="display: flex;justify-content: flex-end;">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="objInput">名录导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['exam:examObject:remove']">批量删除</el-button>
        </el-col>
      </el-row>
      <common-table :loading="loading" :data="examObjectList" :total="total" :columns="columns"
        :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @selection-change="handleSelectionChange"
        @pagination="getList" :dict="dict">
        <template #operation="{ row }">
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(row)"
            v-hasPermi="['exam:examObject:remove']">删除</el-button>
        </template>
      </common-table>
    </card>

    <!-- 添加或修改检查对象名录管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1400px" append-to-body>
      <!-- 检查对象组件 -->
      <exam-obj-a :examTaskId="examTaskId" :examObjType="examObjType"></exam-obj-a>
    </el-dialog>

  </div>
</template>

<script>
import { queryTaskObj,batchDeleteTaskObj,deleteTaskObj } from "@/api/exam/eaxmPlan";
import { admdvsList } from "@/api/system/admdvs";
import CommonTable from "@/components/CommonTable/index.vue";
import SearchForm from "@/components/SearchForm/index.vue";
import card from '@/components/card/index.vue'
import columns from "./columns";
import searchFields from "./searchFields";
import {getToken} from "@/utils/auth";
import examObjA from "../examObjectC/index.vue"

export default {
  name: "ExamObject",
  components: {
    CommonTable,
    SearchForm,
    card,
    examObjA
  },
  dicts: ["whether_flag","legent_lv","exam_obj_type","exam_credit_lv","econ_type"],
  data() {
    return {
      breadList: [
        {
          label: "检查计划管理",
          path: "/exam/eaxmPlan"
        },
        {
          label: "计划任务信息",
          path: "/exam/examTaskInfo"
        },
        {
          label: "检查对象目录",
        }
      ],
      examTaskId: '',
      examObjType: '',
      // 表单输入框统一宽度
      inputWidth: 240,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检查对象名录管理表格数据
      examObjectList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表格列配置
      columns: columns,
      // 筛选字段配置
      searchFields: searchFields(this),
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/exam/examObject/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        examDpotName: null,
        uscc: null,
        admdvs: null,
        legentCode: null,
        legentName: null,
        legentLv: null,
        econType: null,
        aprvEstaDept: null,
        legrepName: null,
        legentAddr: null,
        regRegCode: null,
        aprvEstaDate: null,
        examObjType: null,
        bizScp: null,
        year: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null,
        importFlag: null,
        chronicFlag: null,
        specialFlag: null,
        desiFlag: null,
        examCreditLv: null,
        admdvsName: null,
        selcSeq: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        examDpotName: [
          { required: true, message: "检查库名称不能为空", trigger: "blur" }
        ],
        uscc: [
          { required: true, message: "统一社会信用代码不能为空", trigger: "blur" }
        ],
        admdvs: [
          { required: true, message: "医保区划不能为空", trigger: "blur" }
        ],
        legentCode: [
          { required: true, message: "检查对象编码不能为空", trigger: "blur" }
        ],
        legentName: [
          { required: true, message: "检查对象名称不能为空", trigger: "blur" }
        ],
        legentLv: [
          { required: true, message: "检查对象等级不能为空", trigger: "blur" }
        ],
        examObjType: [
          { required: true, message: "检查对象类型不能为空", trigger: "change" }
        ],
        importFlag: [
          { required: true, message: "导入标志不能为空", trigger: "blur" }
        ]
      },
      admdvsOptions: []
    };
  },
  created() {
    this.examTaskId = this.$route.query.examTaskId;
    this.examObjType = this.$route.query.examObjType;
    this.getList();
    this.getAdmdvs();
  },
  methods: {
    // 字典加载完成回调
    onDictReady(dict) {
      console.log('字典数据加载完成');
      this.initDictOptions();
    },
    // 初始化字典选项
    initDictOptions() {
      // 检查字段是否存在options属性，没有则添加
      const optionsFields = [
        { index: 3, dict: 'legent_lv', label: '检查对象等级' },
        { index: 6, dict: 'econ_type', label: '经济类型' },
        { index: 7, dict: 'exam_obj_type', label: '检查对象类型' },
        { index: 8, dict: 'whether_flag', label: '是否慢病' },
        { index: 9, dict: 'whether_flag', label: '是否特病' },
        { index: 10, dict: 'exam_credit_lv', label: '信用等级' }
      ];

      optionsFields.forEach(item => {
        if (this.searchFields[item.index]) {
          // 确保options属性存在
          if (!this.searchFields[item.index].options) {
            this.$set(this.searchFields[item.index], 'options', []);
          }
          // 获取字典数据
          const dictData = this.dict.type[item.dict];
          if (dictData && dictData.length > 0) {
            // 赋值字典
            this.searchFields[item.index].options = dictData;
          } else {
            console.warn(`字典数据为空: ${item.dict}`);
          }
        } else {
          console.warn(`未找到索引 ${item.index} 的搜索字段`);
        }
      });
    },
    getAdmdvs(){
      admdvsList({}).then(response => {
        this.searchFields[0].options = response.data
        this.admdvsOptions = response.data
      }).catch(error => {
        console.error('获取数据出错:', error);
      });
    },
    /** 查询检查对象名录管理列表 */
    getList() {
      this.loading = true;
      this.queryParams.examTaskId = this.examTaskId;
      queryTaskObj(this.queryParams).then(response => {
        this.examObjectList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取数据出错:', error);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        examObjListId: null,
        examDpotName: null,
        uscc: null,
        admdvs: null,
        legentCode: null,
        legentName: null,
        legentLv: null,
        econType: null,
        aprvEstaDept: null,
        legrepName: null,
        legentAddr: null,
        regRegCode: null,
        aprvEstaDate: null,
        examObjType: null,
        bizScp: null,
        year: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null,
        importFlag: null,
        chronicFlag: null,
        specialFlag: null,
        desiFlag: null,
        examCreditLv: null,
        admdvsName: null,
        selcSeq: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      if (params) {
        // 将搜索表单的数据合并到queryParams中
        Object.keys(params).forEach(key => {
          // 只合并有值的字段，忽略空值
          if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
            this.queryParams[key] = params[key];
          }
        });
      }
      if(this.queryParams.admdvs){
        this.queryParams.admdvs = this.fixedAdmdvs(this.queryParams.admdvs)
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置查询参数（保留分页参数）
      const pageNum = this.queryParams.pageNum;
      const pageSize = this.queryParams.pageSize;

      // 初始化查询参数
      this.queryParams = {
        pageNum: pageNum,
        pageSize: pageSize
      };

      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.examObjListId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.admdvs = this.fixedAdmdvs(this.form.admdvs)
          if (this.form.examObjListId != null) {
            updateExamObject(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addExamObject(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 名录导入
    objInput(){
      this.open = true;
      this.title = "名录导入";
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const examObjListIds = row.examObjListId || this.ids;
      this.$modal.confirm('是否确认删除选中的检查对象数据项？').then(function () {
        return batchDeleteTaskObj(examObjListIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('exam/examObject/importTemplate', {
      }, `exam_template_${new Date().getTime()}.xlsx`)
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    fixedAdmdvs(val) {
      if (typeof val == "string" && val.constructor == String) {
        return val;
      }
      if (val && val.length) {
        return val[val.length - 1];
      }
      return null;
    },
  }
};
</script>
