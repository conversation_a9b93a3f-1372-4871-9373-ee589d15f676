<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="调度任务名称" prop="volaTaskInfoName">
        <el-input
          v-model="queryParams.volaTaskInfoName"
          placeholder="请输入调度任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医疗机构等级" prop="medinsLv">
        <el-input
          v-model="queryParams.medinsLv"
          placeholder="请输入医疗机构等级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="结算开始时间" prop="setlTimeStart">
        <el-date-picker clearable
          v-model="queryParams.setlTimeStart"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择结算开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="结算结束时间" prop="setlTimeEnd">
        <el-date-picker clearable
          v-model="queryParams.setlTimeEnd"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择结算结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="规则ID集合" prop="ruleIds">
        <el-input
          v-model="queryParams.ruleIds"
          placeholder="请输入规则ID集合"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="建立任务时间" prop="taskTime">
        <el-date-picker clearable
          v-model="queryParams.taskTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择建立任务时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dataAnalysis:volaTask:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dataAnalysis:volaTask:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dataAnalysis:volaTask:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dataAnalysis:volaTask:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="volaTaskList"
      @selection-change="handleSelectionChange"
      fit
      table-layout="auto"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="调度任务名称"
        align="center"
        prop="volaTaskInfoName"
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column
        label="筛查医疗机构集合"
        align="center"
        prop="medinsInfo"
        min-width="180"
        show-overflow-tooltip
      />
      <el-table-column
        label="医疗机构等级"
        align="center"
        prop="medinsLv"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="医疗类别"
        align="center"
        prop="medType"
        width="100"
        show-overflow-tooltip
      />
      <el-table-column
        label="结算开始时间"
        align="center"
        prop="setlTimeStart"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.setlTimeStart, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="结算结束时间"
        align="center"
        prop="setlTimeEnd"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.setlTimeEnd, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="规则ID集合"
        align="center"
        prop="ruleIds"
        min-width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="规则名称集合"
        align="center"
        prop="ruleNames"
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column
        label="建立任务时间"
        align="center"
        prop="taskTime"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.taskTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
        min-width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="操作"
        align="center"
        width="120"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dataAnalysis:volaTask:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dataAnalysis:volaTask:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改调度任务信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <!-- 步骤条 -->
      <el-steps
        :active="currentStep"
        finish-status="success"
        align-center
        class="custom-steps">
        <el-step title="基本信息" description="填写调度任务基本信息">
          <i slot="icon" class="el-icon-edit-outline"></i>
        </el-step>
        <el-step title="规则选择" description="选择检查规则">
          <i slot="icon" class="el-icon-setting"></i>
        </el-step>
      </el-steps>

      <!-- 第一步：基本信息 -->
      <div v-show="currentStep === 0" class="step-content">
        <div class="step-header">
          <h3><i class="el-icon-edit-outline"></i> 基本信息</h3>
          <p>请填写调度任务的基本信息</p>
        </div>

        <el-form ref="form" :model="form" :rules="rules" label-width="140px" class="step-form">
          <div class="form-section">
            <div class="section-title">
              <i class="el-icon-document"></i>
              <span>任务信息</span>
            </div>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="调度任务名称" prop="volaTaskInfoName">
                  <el-input
                    v-model="form.volaTaskInfoName"
                    placeholder="请输入调度任务名称"
                    prefix-icon="el-icon-edit" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="医疗机构等级" prop="medinsLv">
                  <el-select
                    v-model="form.medinsLv"
                    placeholder="请选择医疗机构等级"
                    clearable
                    style="width: 100%">
                    <el-option
                      v-for="dict in dict.type.legent_lv"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="筛查医疗机构集合" prop="medinsInfo">
              <el-select
                v-model="form.medinsInfo"
                multiple
                filterable
                remote
                reserve-keyword
                placeholder="请选择筛查医疗机构"
                :remote-method="searchMedicalInstitutions"
                :loading="medListLoading"
                style="width: 100%"
                @focus="getMedicalInstitutionList">
                <el-option
                  v-for="item in medicalInstitutionList"
                  :key="item.fixmedinsCode"
                  :label="item.fixmedinsName"
                  :value="item.fixmedinsCode">
                  <span style="float: left">{{ item.fixmedinsName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ item.fixmedinsCode }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </div>

          <div class="form-section">
            <div class="section-title">
              <i class="el-icon-date"></i>
              <span>结算时间</span>
            </div>
            <el-form-item label="结算时间范围" prop="settlementTimeRange">
              <el-date-picker
                v-model="form.settlementTimeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期时间"
                end-placeholder="结束日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </div>
        </el-form>
      </div>

      <!-- 第二步：规则选择 -->
      <div v-show="currentStep === 1" class="step-content">
        <div class="step-header">
          <h3><i class="el-icon-setting"></i> 规则选择</h3>
          <p>请选择需要应用的检查规则</p>
        </div>

        <div class="transfer-container">
          <div class="transfer-tips">
            <el-alert
              title="操作提示"
              type="info"
              :closable="false"
              show-icon>
              <div slot="description">
                <p>• 从左侧选择需要的检查规则，点击右箭头添加到已选规则</p>
                <p>• 支持搜索功能，可快速定位所需规则</p>
                <p>• 至少需要选择一个规则才能完成创建</p>
              </div>
            </el-alert>
          </div>

          <div class="transfer-wrapper">
            <el-transfer
              v-model="transferValue"
              :data="allRules"
              :titles="['可选规则', '已选规则']"
              :button-texts="['移除', '添加']"
              :format="{
                noChecked: '共 ${total} 项',
                hasChecked: '已选 ${checked}/${total} 项'
              }"
              filterable
              filter-placeholder="搜索规则名称"
              class="custom-transfer">
              <span slot-scope="{ option }">
                <i class="el-icon-document-checked"></i>
                {{ option.label }}
              </span>
            </el-transfer>
          </div>

          <div class="selected-summary" v-if="transferValue.length > 0">
            <div class="summary-header">
              <i class="el-icon-success"></i>
              <span>已选择 {{ transferValue.length }} 个规则</span>
            </div>
            <div class="summary-tags">
              <el-tag
                v-for="ruleId in transferValue"
                :key="ruleId"
                type="success"
                size="small"
                style="margin: 2px 4px 2px 0;">
                {{ getRuleName(ruleId) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
        <el-button v-if="currentStep < 1" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-if="currentStep === 1" type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listVolaTask, getVolaTask, delVolaTask, addVolaTask, updateVolaTask } from "@/api/dataAnalysis/volaTask";
import request from '@/utils/request';

export default {
  name: "VolaTask",
  dicts: ['legent_lv'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 调度任务信息表格数据
      volaTaskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前步骤
      currentStep: 0,
      // 穿梭框数据
      transferData: [],
      // 穿梭框选中的值
      transferValue: [],
      // 所有可选规则列表
      allRules: [
        { key: '1', label: '规则1：医保目录外用药检查' },
        { key: '2', label: '规则2：超量用药检查' },
        { key: '3', label: '规则3：重复用药检查' },
        { key: '4', label: '规则4：配伍禁忌检查' },
        { key: '5', label: '规则5：适应症检查' },
        { key: '6', label: '规则6：诊疗项目合理性检查' },
        { key: '7', label: '规则7：医疗服务价格检查' },
        { key: '8', label: '规则8：住院天数合理性检查' }
      ],
      // 医疗机构列表
      medicalInstitutionList: [],
      // 医疗机构列表加载状态
      medListLoading: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        volaTaskInfoName: null,
        medinsInfo: null,
        medinsLv: null,
        medType: null,
        setlTimeStart: null,
        setlTimeEnd: null,
        ruleIds: null,
        ruleNames: null,
        taskTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        volaTaskInfoName: [
          { required: true, message: "调度任务名称不能为空", trigger: "blur" }
        ],
        medinsInfo: [
          { required: true, message: "筛查医疗机构集合不能为空", trigger: "blur" }
        ],
        settlementTimeRange: [
          { required: true, message: "结算时间范围不能为空", trigger: "change" }
        ],
        // 注意：ruleIds、ruleNames、taskTime 由穿梭框和系统自动生成，不需要在第一步验证
        // delFlag、createBy、createTime、updateBy、updateTime 由系统自动处理，不需要验证
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询调度任务信息列表 */
    getList() {
      this.loading = true;
      listVolaTask(this.queryParams).then(response => {
        this.volaTaskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.currentStep = 0;
      this.transferValue = [];
      this.reset();
    },
    // 下一步
    nextStep() {
      if (this.currentStep === 0) {
        // 验证第一步表单
        this.$refs["form"].validate(valid => {
          if (valid) {
            this.currentStep = 1;
          }
        });
      }
    },
    // 上一步
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--;
      }
    },
    // 获取规则名称
    getRuleName(ruleId) {
      const rule = this.allRules.find(r => r.key === ruleId);
      return rule ? rule.label : '';
    },
    // 获取医疗机构列表
    getMedicalInstitutionList() {
      if (this.medicalInstitutionList.length > 0) {
        return; // 如果已经有数据，不重复请求
      }
      this.medListLoading = true;
      request({
        url: '/dataAnalysis/volaTask/medList',
        method: 'get'
      }).then(response => {
        if (response.code === 200) {
          this.medicalInstitutionList = response.data || [];
        } else {
          this.$modal.msgError(response.msg || '获取医疗机构列表失败');
        }
      }).catch(error => {
        console.error('获取医疗机构列表失败:', error);
        this.$modal.msgError('获取医疗机构列表失败');
      }).finally(() => {
        this.medListLoading = false;
      });
    },
    // 搜索医疗机构
    searchMedicalInstitutions(query) {
      if (query !== '') {
        this.medListLoading = true;
        request({
          url: '/dataAnalysis/volaTask/medList',
          method: 'get',
          params: { keyword: query }
        }).then(response => {
          if (response.code === 200) {
            this.medicalInstitutionList = response.data || [];
          }
        }).catch(error => {
          console.error('搜索医疗机构失败:', error);
        }).finally(() => {
          this.medListLoading = false;
        });
      } else {
        this.getMedicalInstitutionList();
      }
    },
    // 表单重置
    reset() {
      this.currentStep = 0;
      this.transferValue = [];
      this.form = {
        volaTaskInfoId: null,
        volaTaskInfoName: null,
        medinsInfo: [], // 改为数组类型，支持多选
        medinsLv: null,
        medType: null,
        setlTimeStart: null,
        setlTimeEnd: null,
        settlementTimeRange: [], // 新增时间范围字段
        ruleIds: null,
        ruleNames: null,
        taskTime: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.volaTaskInfoId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加调度任务信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const volaTaskInfoId = row.volaTaskInfoId || this.ids
      getVolaTask(volaTaskInfoId).then(response => {
        this.form = response.data;
        // 回显穿梭框数据
        if (this.form.ruleIds) {
          this.transferValue = this.form.ruleIds.split(',');
        }
        // 回显医疗机构数据
        if (this.form.medinsInfo) {
          // 如果是字符串，转换为数组
          if (typeof this.form.medinsInfo === 'string') {
            this.form.medinsInfo = this.form.medinsInfo.split(',');
          }
        } else {
          this.form.medinsInfo = [];
        }
        // 回显时间范围数据
        if (this.form.setlTimeStart && this.form.setlTimeEnd) {
          this.form.settlementTimeRange = [this.form.setlTimeStart, this.form.setlTimeEnd];
        } else {
          this.form.settlementTimeRange = [];
        }
        this.open = true;
        this.title = "修改调度任务信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      // 处理穿梭框选中的规则
      if (this.transferValue.length === 0) {
        this.$modal.msgWarning("请至少选择一个检查规则");
        return;
      }

      // 处理医疗机构数据
      if (!this.form.medinsInfo || this.form.medinsInfo.length === 0) {
        this.$modal.msgWarning("请至少选择一个医疗机构");
        return;
      }

      // 处理时间范围数据
      if (!this.form.settlementTimeRange || this.form.settlementTimeRange.length !== 2) {
        this.$modal.msgWarning("请选择结算时间范围");
        return;
      }

      // 创建提交数据的副本
      const submitData = { ...this.form };

      // 将医疗机构数组转换为逗号分隔的字符串
      if (Array.isArray(submitData.medinsInfo)) {
        submitData.medinsInfo = submitData.medinsInfo.join(',');
      }

      // 处理时间范围数据
      if (submitData.settlementTimeRange && submitData.settlementTimeRange.length === 2) {
        submitData.setlTimeStart = submitData.settlementTimeRange[0];
        submitData.setlTimeEnd = submitData.settlementTimeRange[1];
      }
      // 移除时间范围字段，因为后端不需要这个字段
      delete submitData.settlementTimeRange;

      // 将选中的规则ID和名称设置到表单中
      submitData.ruleIds = this.transferValue.join(',');
      const selectedRuleNames = this.allRules
        .filter(rule => this.transferValue.includes(rule.key))
        .map(rule => rule.label)
        .join(',');
      submitData.ruleNames = selectedRuleNames;

      // 设置当前时间为建立任务时间
      submitData.taskTime = new Date().toISOString().split('T')[0];

      this.$refs["form"].validate(valid => {
        if (valid) {
          if (submitData.volaTaskInfoId != null) {
            updateVolaTask(submitData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.currentStep = 0;
              this.transferValue = [];
              this.getList();
            });
          } else {
            addVolaTask(submitData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.currentStep = 0;
              this.transferValue = [];
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const volaTaskInfoIds = row.volaTaskInfoId || this.ids;
      this.$modal.confirm('是否确认删除调度任务信息编号为"' + volaTaskInfoIds + '"的数据项？').then(function() {
        return delVolaTask(volaTaskInfoIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dataAnalysis/volaTask/export', {
        ...this.queryParams
      }, `volaTask_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

/* 表格自适应样式 */
.el-table {
  width: 100%;
}

/* 确保表格在小屏幕上的显示 */
@media (max-width: 768px) {
  .el-table {
    font-size: 12px;
  }

  .el-table .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

/* 表格行高优化 */
.el-table .el-table__row {
  height: auto;
}

/* 操作按钮样式优化 */
.el-table .el-button--mini {
  margin: 0 2px;
}

/* 步骤条样式优化 */
.custom-steps {
  margin-bottom: 40px;
  padding: 0 20px;
}

.custom-steps .el-step__title {
  font-size: 16px;
  font-weight: 600;
}

.custom-steps .el-step__description {
  font-size: 13px;
  color: #909399;
}

/* 步骤内容区域 */
.step-content {
  min-height: 400px;
  padding: 0 20px;
}

.step-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.step-header h3 {
  font-size: 20px;
  color: #303133;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.step-header h3 i {
  margin-right: 8px;
  color: #409eff;
}

.step-header p {
  color: #606266;
  margin: 0;
  font-size: 14px;
}

/* 表单样式优化 */
.step-form {
  max-width: 100%;
}

.form-section {
  margin-bottom: 30px;
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #ebeef5;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.section-title i {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.step-form .el-form-item {
  margin-bottom: 24px;
}

.step-form .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* 穿梭框容器样式 */
.transfer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.transfer-tips {
  width: 100%;
  margin-bottom: 24px;
}

.transfer-wrapper {
  margin-bottom: 24px;
}

.custom-transfer {
  text-align: center;
}

.custom-transfer .el-transfer-panel {
  width: 280px;
  height: 350px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.custom-transfer .el-transfer-panel__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px 8px 0 0;
  font-weight: 600;
}

.custom-transfer .el-transfer-panel__header .el-checkbox {
  color: white;
}

.custom-transfer .el-transfer-panel__filter {
  padding: 12px;
  background: #f8f9fa;
}

.custom-transfer .el-transfer-panel__list {
  height: 240px;
}

.custom-transfer .el-transfer-panel__item {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.custom-transfer .el-transfer-panel__item:hover {
  background: #f5f7fa;
}

.custom-transfer .el-transfer-panel__item i {
  margin-right: 8px;
  color: #409eff;
}

/* 已选规则摘要 */
.selected-summary {
  width: 100%;
  max-width: 600px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 8px;
  padding: 16px;
}

.summary-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 600;
  color: #409eff;
}

.summary-header i {
  margin-right: 8px;
  font-size: 16px;
}

.summary-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 对话框底部按钮样式 */
.dialog-footer {
  text-align: right;
  padding: 20px 24px;
  background: #fafafa;
  border-top: 1px solid #ebeef5;
}

.dialog-footer .el-button {
  margin-left: 12px;
  min-width: 80px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .step-content {
    padding: 0 10px;
  }

  .custom-transfer .el-transfer-panel {
    width: 240px;
    height: 300px;
  }

  .form-section {
    padding: 15px;
  }
}
</style>
