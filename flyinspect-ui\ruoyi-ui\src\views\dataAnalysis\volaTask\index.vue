<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="调度任务名称" prop="volaTaskInfoName">
        <el-input
          v-model="queryParams.volaTaskInfoName"
          placeholder="请输入调度任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医疗机构等级" prop="medinsLv">
        <el-input
          v-model="queryParams.medinsLv"
          placeholder="请输入医疗机构等级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="结算开始时间" prop="setlTimeStart">
        <el-date-picker clearable
          v-model="queryParams.setlTimeStart"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择结算开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="结算结束时间" prop="setlTimeEnd">
        <el-date-picker clearable
          v-model="queryParams.setlTimeEnd"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择结算结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="规则ID集合" prop="ruleIds">
        <el-input
          v-model="queryParams.ruleIds"
          placeholder="请输入规则ID集合"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="建立任务时间" prop="taskTime">
        <el-date-picker clearable
          v-model="queryParams.taskTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择建立任务时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dataAnalysis:volaTask:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dataAnalysis:volaTask:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dataAnalysis:volaTask:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dataAnalysis:volaTask:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="volaTaskList"
      @selection-change="handleSelectionChange"
      fit
      table-layout="auto"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="调度任务名称"
        align="center"
        prop="volaTaskInfoName"
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column
        label="筛查医疗机构集合"
        align="center"
        prop="medinsInfo"
        min-width="180"
        show-overflow-tooltip
      />
      <el-table-column
        label="医疗机构等级"
        align="center"
        prop="medinsLv"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="医疗类别"
        align="center"
        prop="medType"
        width="100"
        show-overflow-tooltip
      />
      <el-table-column
        label="结算开始时间"
        align="center"
        prop="setlTimeStart"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.setlTimeStart, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="结算结束时间"
        align="center"
        prop="setlTimeEnd"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.setlTimeEnd, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="规则ID集合"
        align="center"
        prop="ruleIds"
        min-width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="规则名称集合"
        align="center"
        prop="ruleNames"
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column
        label="建立任务时间"
        align="center"
        prop="taskTime"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.taskTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
        min-width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="操作"
        align="center"
        width="120"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dataAnalysis:volaTask:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dataAnalysis:volaTask:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改调度任务信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <!-- 步骤条 -->
      <el-steps :active="currentStep" finish-status="success" style="margin-bottom: 30px;">
        <el-step title="基本信息" description="填写调度任务基本信息"></el-step>
        <el-step title="规则选择" description="选择检查规则"></el-step>
      </el-steps>

      <!-- 第一步：基本信息 -->
      <div v-show="currentStep === 0">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="调度任务名称" prop="volaTaskInfoName">
                <el-input v-model="form.volaTaskInfoName" placeholder="请输入调度任务名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="医疗机构等级" prop="medinsLv">
                <el-input v-model="form.medinsLv" placeholder="请输入医疗机构等级" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="筛查医疗机构集合" prop="medinsInfo">
                <el-input v-model="form.medinsInfo" type="textarea" :rows="3" placeholder="请输入筛查医疗机构集合" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="结算开始时间" prop="setlTimeStart">
                <el-date-picker
                  clearable
                  v-model="form.setlTimeStart"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择结算开始时间"
                  style="width: 100%">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结算结束时间" prop="setlTimeEnd">
                <el-date-picker
                  clearable
                  v-model="form.setlTimeEnd"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择结算结束时间"
                  style="width: 100%">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 第二步：规则选择 -->
      <div v-show="currentStep === 1">
        <div style="margin-bottom: 20px;">
          <h4>选择检查规则</h4>
          <p style="color: #666; font-size: 14px;">请从左侧选择需要的检查规则，移动到右侧</p>
        </div>
        <el-transfer
          v-model="transferValue"
          :data="allRules"
          :titles="['可选规则', '已选规则']"
          :button-texts="['移除', '添加']"
          :format="{
            noChecked: '${total}',
            hasChecked: '${checked}/${total}'
          }"
          filterable
          filter-placeholder="搜索规则"
          style="text-align: left; display: inline-block">
        </el-transfer>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
        <el-button v-if="currentStep < 1" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-if="currentStep === 1" type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listVolaTask, getVolaTask, delVolaTask, addVolaTask, updateVolaTask } from "@/api/dataAnalysis/volaTask";

export default {
  name: "VolaTask",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 调度任务信息表格数据
      volaTaskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前步骤
      currentStep: 0,
      // 穿梭框数据
      transferData: [],
      // 穿梭框选中的值
      transferValue: [],
      // 所有可选规则列表
      allRules: [
        { key: '1', label: '规则1：医保目录外用药检查' },
        { key: '2', label: '规则2：超量用药检查' },
        { key: '3', label: '规则3：重复用药检查' },
        { key: '4', label: '规则4：配伍禁忌检查' },
        { key: '5', label: '规则5：适应症检查' },
        { key: '6', label: '规则6：诊疗项目合理性检查' },
        { key: '7', label: '规则7：医疗服务价格检查' },
        { key: '8', label: '规则8：住院天数合理性检查' }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        volaTaskInfoName: null,
        medinsInfo: null,
        medinsLv: null,
        medType: null,
        setlTimeStart: null,
        setlTimeEnd: null,
        ruleIds: null,
        ruleNames: null,
        taskTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        volaTaskInfoName: [
          { required: true, message: "调度任务名称不能为空", trigger: "blur" }
        ],
        medinsInfo: [
          { required: true, message: "筛查医疗机构集合不能为空", trigger: "blur" }
        ],
        setlTimeStart: [
          { required: true, message: "结算开始时间不能为空", trigger: "blur" }
        ],
        setlTimeEnd: [
          { required: true, message: "结算结束时间不能为空", trigger: "blur" }
        ],
        ruleIds: [
          { required: true, message: "规则ID集合不能为空", trigger: "blur" }
        ],
        ruleNames: [
          { required: true, message: "规则名称集合不能为空", trigger: "blur" }
        ],
        taskTime: [
          { required: true, message: "建立任务时间不能为空", trigger: "blur" }
        ],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" }
        ],
        createBy: [
          { required: true, message: "创建者不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updateBy: [
          { required: true, message: "更新者不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "更新时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询调度任务信息列表 */
    getList() {
      this.loading = true;
      listVolaTask(this.queryParams).then(response => {
        this.volaTaskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.currentStep = 0;
      this.transferValue = [];
      this.reset();
    },
    // 下一步
    nextStep() {
      if (this.currentStep === 0) {
        // 验证第一步表单
        this.$refs["form"].validate(valid => {
          if (valid) {
            this.currentStep = 1;
          }
        });
      }
    },
    // 上一步
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--;
      }
    },
    // 表单重置
    reset() {
      this.currentStep = 0;
      this.transferValue = [];
      this.form = {
        volaTaskInfoId: null,
        volaTaskInfoName: null,
        medinsInfo: null,
        medinsLv: null,
        medType: null,
        setlTimeStart: null,
        setlTimeEnd: null,
        ruleIds: null,
        ruleNames: null,
        taskTime: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.volaTaskInfoId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加调度任务信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const volaTaskInfoId = row.volaTaskInfoId || this.ids
      getVolaTask(volaTaskInfoId).then(response => {
        this.form = response.data;
        // 回显穿梭框数据
        if (this.form.ruleIds) {
          this.transferValue = this.form.ruleIds.split(',');
        }
        this.open = true;
        this.title = "修改调度任务信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      // 处理穿梭框选中的规则
      if (this.transferValue.length === 0) {
        this.$modal.msgWarning("请至少选择一个检查规则");
        return;
      }

      // 将选中的规则ID和名称设置到表单中
      this.form.ruleIds = this.transferValue.join(',');
      const selectedRuleNames = this.allRules
        .filter(rule => this.transferValue.includes(rule.key))
        .map(rule => rule.label)
        .join(',');
      this.form.ruleNames = selectedRuleNames;

      // 设置当前时间为建立任务时间
      this.form.taskTime = new Date().toISOString().split('T')[0];

      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.volaTaskInfoId != null) {
            updateVolaTask(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.currentStep = 0;
              this.transferValue = [];
              this.getList();
            });
          } else {
            addVolaTask(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.currentStep = 0;
              this.transferValue = [];
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const volaTaskInfoIds = row.volaTaskInfoId || this.ids;
      this.$modal.confirm('是否确认删除调度任务信息编号为"' + volaTaskInfoIds + '"的数据项？').then(function() {
        return delVolaTask(volaTaskInfoIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dataAnalysis/volaTask/export', {
        ...this.queryParams
      }, `volaTask_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

/* 表格自适应样式 */
.el-table {
  width: 100%;
}

/* 确保表格在小屏幕上的显示 */
@media (max-width: 768px) {
  .el-table {
    font-size: 12px;
  }

  .el-table .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

/* 表格行高优化 */
.el-table .el-table__row {
  height: auto;
}

/* 操作按钮样式优化 */
.el-table .el-button--mini {
  margin: 0 2px;
}

/* 步骤表单样式 */
.el-steps {
  margin-bottom: 30px;
}

.el-transfer {
  text-align: center;
}

.el-transfer-panel {
  width: 300px;
}

/* 对话框内容区域样式 */
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 表单布局优化 */
.el-form .el-row {
  margin-bottom: 10px;
}

.el-form .el-form-item {
  margin-bottom: 20px;
}
</style>
