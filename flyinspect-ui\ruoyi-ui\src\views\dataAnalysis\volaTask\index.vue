<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="调度任务名称" prop="volaTaskInfoName">
        <el-input
          v-model="queryParams.volaTaskInfoName"
          placeholder="请输入调度任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医疗机构等级" prop="medinsLv">
        <el-input
          v-model="queryParams.medinsLv"
          placeholder="请输入医疗机构等级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="结算开始时间" prop="setlTimeStart">
        <el-date-picker clearable
          v-model="queryParams.setlTimeStart"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择结算开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="结算结束时间" prop="setlTimeEnd">
        <el-date-picker clearable
          v-model="queryParams.setlTimeEnd"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择结算结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="规则ID集合" prop="ruleIds">
        <el-input
          v-model="queryParams.ruleIds"
          placeholder="请输入规则ID集合"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="建立任务时间" prop="taskTime">
        <el-date-picker clearable
          v-model="queryParams.taskTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择建立任务时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dataAnalysis:volaTask:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dataAnalysis:volaTask:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dataAnalysis:volaTask:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dataAnalysis:volaTask:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="volaTaskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="调度任务ID" align="center" prop="volaTaskInfoId" />
      <el-table-column label="调度任务名称" align="center" prop="volaTaskInfoName" />
      <el-table-column label="筛查医疗机构集合" align="center" prop="medinsInfo" />
      <el-table-column label="医疗机构等级" align="center" prop="medinsLv" />
      <el-table-column label="医疗类别" align="center" prop="medType" />
      <el-table-column label="结算开始时间" align="center" prop="setlTimeStart" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.setlTimeStart, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结算结束时间" align="center" prop="setlTimeEnd" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.setlTimeEnd, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="规则ID集合" align="center" prop="ruleIds" />
      <el-table-column label="规则名称集合" align="center" prop="ruleNames" />
      <el-table-column label="建立任务时间" align="center" prop="taskTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.taskTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dataAnalysis:volaTask:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dataAnalysis:volaTask:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改调度任务信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="调度任务名称" prop="volaTaskInfoName">
          <el-input v-model="form.volaTaskInfoName" placeholder="请输入调度任务名称" />
        </el-form-item>
        <el-form-item label="筛查医疗机构集合" prop="medinsInfo">
          <el-input v-model="form.medinsInfo" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="医疗机构等级" prop="medinsLv">
          <el-input v-model="form.medinsLv" placeholder="请输入医疗机构等级" />
        </el-form-item>
        <el-form-item label="结算开始时间" prop="setlTimeStart">
          <el-date-picker clearable
            v-model="form.setlTimeStart"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择结算开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结算结束时间" prop="setlTimeEnd">
          <el-date-picker clearable
            v-model="form.setlTimeEnd"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择结算结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="规则ID集合" prop="ruleIds">
          <el-input v-model="form.ruleIds" placeholder="请输入规则ID集合" />
        </el-form-item>
        <el-form-item label="规则名称集合" prop="ruleNames">
          <el-input v-model="form.ruleNames" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="建立任务时间" prop="taskTime">
          <el-date-picker clearable
            v-model="form.taskTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择建立任务时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listVolaTask, getVolaTask, delVolaTask, addVolaTask, updateVolaTask } from "@/api/dataAnalysis/volaTask";

export default {
  name: "VolaTask",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 调度任务信息表格数据
      volaTaskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        volaTaskInfoName: null,
        medinsInfo: null,
        medinsLv: null,
        medType: null,
        setlTimeStart: null,
        setlTimeEnd: null,
        ruleIds: null,
        ruleNames: null,
        taskTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        volaTaskInfoName: [
          { required: true, message: "调度任务名称不能为空", trigger: "blur" }
        ],
        medinsInfo: [
          { required: true, message: "筛查医疗机构集合不能为空", trigger: "blur" }
        ],
        setlTimeStart: [
          { required: true, message: "结算开始时间不能为空", trigger: "blur" }
        ],
        setlTimeEnd: [
          { required: true, message: "结算结束时间不能为空", trigger: "blur" }
        ],
        ruleIds: [
          { required: true, message: "规则ID集合不能为空", trigger: "blur" }
        ],
        ruleNames: [
          { required: true, message: "规则名称集合不能为空", trigger: "blur" }
        ],
        taskTime: [
          { required: true, message: "建立任务时间不能为空", trigger: "blur" }
        ],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" }
        ],
        createBy: [
          { required: true, message: "创建者不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updateBy: [
          { required: true, message: "更新者不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "更新时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询调度任务信息列表 */
    getList() {
      this.loading = true;
      listVolaTask(this.queryParams).then(response => {
        this.volaTaskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        volaTaskInfoId: null,
        volaTaskInfoName: null,
        medinsInfo: null,
        medinsLv: null,
        medType: null,
        setlTimeStart: null,
        setlTimeEnd: null,
        ruleIds: null,
        ruleNames: null,
        taskTime: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.volaTaskInfoId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加调度任务信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const volaTaskInfoId = row.volaTaskInfoId || this.ids
      getVolaTask(volaTaskInfoId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改调度任务信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.volaTaskInfoId != null) {
            updateVolaTask(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addVolaTask(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const volaTaskInfoIds = row.volaTaskInfoId || this.ids;
      this.$modal.confirm('是否确认删除调度任务信息编号为"' + volaTaskInfoIds + '"的数据项？').then(function() {
        return delVolaTask(volaTaskInfoIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dataAnalysis/volaTask/export', {
        ...this.queryParams
      }, `volaTask_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
