<template>
  <div>
    <el-table v-loading="loading" :data="tableData" @selection-change="handleSelectionChange" v-bind="$attrs" border>
      <el-table-column v-if="showSelection" type="selection" width="55" align="center" />
      <el-table-column v-if="showIndex" type="index" width="55" label="序号" align="center" :index="indexMethod" />
      <el-table-column v-for="(column, index) in columns" :prop="column.prop" :key="column.prop" :label="column.label"
        :align="column.align || 'center'" :width="column.width" v-bind="column.attrs || {}"
        :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <template v-if="column.slot">
            <slot :name="column.slot" :row="scope.row" :$index="scope.$index"></slot>
          </template>
          <template v-else-if="column.formatter">
            {{ column.formatter(scope.row, scope.column, scope.$index) }}
          </template>
          <template v-else-if="column.dictType && dict && dict.type[column.dictType]">
            <dict-tag :options="dict.type[column.dictType]" :value="scope.row[column.prop]" />
          </template>
          <template v-else-if="column.type === 'date'">
            <span>{{ parseTime(scope.row[column.prop], '{y}-{m}-{d}') }}</span>
          </template>
          <template v-else>
            {{ scope.row[column.prop] }}
          </template>
        </template>
      </el-table-column>
      <el-table-column fixed="right" :width="columnWidth" v-if="showOperation" label="操作" align="center"
        class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <slot name="operation" :row="scope.row" :$index="scope.$index"></slot>
        </template>
      </el-table-column>
    </el-table>

    <pagination :total="total" :page.sync="currentPage" :limit.sync="pageSize" @pagination="handlePagination" />
  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import Pagination from '@/components/Pagination'
import DictTag from '@/components/DictTag'

export default {
  name: 'CommonTable',
  components: {
    Pagination,
    DictTag
  },
  props: {
    // 表格数据
    data: {
      type: Array,
      required: true
    },
    // 表格列配置
    columns: {
      type: Array,
      required: true
    },
    // 是否显示序号列
    showIndex: {
      type: Boolean,
      default: true
    },
    // 是否显示选择列
    showSelection: {
      type: Boolean,
      default: true
    },
    // 是否显示操作列
    showOperation: {
      type: Boolean,
      default: true
    },
    // 是否展示分页
    showPagination: {
      type: Boolean,
      default: true
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 总条数
    total: {
      type: Number,
      default: 0
    },
    // 当前页码
    page: {
      type: Number,
      default: 1
    },
    // 每页数量
    limit: {
      type: Number,
      default: 10
    },
    // 尾列宽度
    columnWidth: {
      type: Number,
      default: 200
    },
    // 字典数据对象
    dict: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 当前页
      currentPage: this.page,
      // 每页显示条数
      pageSize: this.limit,
      // 选中的行
      selectedRows: []
    }
  },
  computed: {
    tableData() {
      return this.data || []
    }
  },
  watch: {
    page(val) {
      this.currentPage = val
    },
    limit(val) {
      this.pageSize = val
    },
    data: {
      handler(newVal) {
      },
      deep: true,
      immediate: true
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    // 序号计算方法
    indexMethod(index) {
      // 当前页数乘以每页条数加上当前索引加一
      return (this.currentPage - 1) * this.pageSize + index + 1
    },
    // 处理分页
    handlePagination() {
      this.$emit('update:page', this.currentPage)
      this.$emit('update:limit', this.pageSize)
      this.$emit('pagination', {
        page: this.currentPage,
        limit: this.pageSize
      })
    },
    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
      this.$emit('selection-change', selection)
    },
    // 暴露获取选中行的方法
    getSelectedRows() {
      return this.selectedRows
    },
    // 格式化时间
    parseTime
  }
}
</script>
