import request from '@/utils/request'

// 查询检查人员名录列表
export function listExamPsnInfo(query) {
  return request({
    url: '/exam/examPsnInfo/list',
    method: 'get',
    params: query
  })
}

// 查询检查人员名录详细
export function getExamPsnInfo(examPsnListId) {
  return request({
    url: '/exam/examPsnInfo/' + examPsnListId,
    method: 'get'
  })
}

// 新增检查人员名录
export function addExamPsnInfo(data) {
  return request({
    url: '/exam/examPsnInfo',
    method: 'post',
    data: data
  })
}

// 修改检查人员名录
export function updateExamPsnInfo(data) {
  return request({
    url: '/exam/examPsnInfo',
    method: 'put',
    data: data
  })
}

// 删除检查人员名录
export function delExamPsnInfo(examPsnListId) {
  return request({
    url: '/exam/examPsnInfo/' + examPsnListId,
    method: 'delete'
  })
}
