{"remainingRequest": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\api\\dataAnalysis\\volaTask.js", "dependencies": [{"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\api\\dataAnalysis\\volaTask.js", "mtime": 1750383268000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\babel.config.js", "mtime": 1748414362378}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9Xb3JrU3BhY2Uvd29ya3NwYWNlLXJhbmRvbS9mbHlpbnNwZWN0L2ZseWluc3BlY3QtdWkvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmFkZFZvbGFUYXNrID0gYWRkVm9sYVRhc2s7CmV4cG9ydHMuZGVsVm9sYVRhc2sgPSBkZWxWb2xhVGFzazsKZXhwb3J0cy5nZXRWb2xhVGFzayA9IGdldFZvbGFUYXNrOwpleHBvcnRzLmxpc3RWb2xhVGFzayA9IGxpc3RWb2xhVGFzazsKZXhwb3J0cy51cGRhdGVWb2xhVGFzayA9IHVwZGF0ZVZvbGFUYXNrOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i6LCD5bqm5Lu75Yqh5L+h5oGv5YiX6KGoCmZ1bmN0aW9uIGxpc3RWb2xhVGFzayhxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2RhdGFBbmFseXNpcy92b2xhVGFzay9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouiwg+W6puS7u+WKoeS/oeaBr+ivpue7hgpmdW5jdGlvbiBnZXRWb2xhVGFzayh2b2xhVGFza0luZm9JZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2RhdGFBbmFseXNpcy92b2xhVGFzay8nICsgdm9sYVRhc2tJbmZvSWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinuiwg+W6puS7u+WKoeS/oeaBrwpmdW5jdGlvbiBhZGRWb2xhVGFzayhkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvZGF0YUFuYWx5c2lzL3ZvbGFUYXNrJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnosIPluqbku7vliqHkv6Hmga8KZnVuY3Rpb24gdXBkYXRlVm9sYVRhc2soZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2RhdGFBbmFseXNpcy92b2xhVGFzaycsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTosIPluqbku7vliqHkv6Hmga8KZnVuY3Rpb24gZGVsVm9sYVRhc2sodm9sYVRhc2tJbmZvSWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9kYXRhQW5hbHlzaXMvdm9sYVRhc2svJyArIHZvbGFUYXNrSW5mb0lkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listVolaTask", "query", "request", "url", "method", "params", "getVolaTask", "volaTaskInfoId", "addVolaTask", "data", "updateVolaTask", "delVolaTask"], "sources": ["E:/WorkSpace/workspace-random/flyinspect/flyinspect-ui/ruoyi-ui/src/api/dataAnalysis/volaTask.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询调度任务信息列表\r\nexport function listVolaTask(query) {\r\n  return request({\r\n    url: '/dataAnalysis/volaTask/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询调度任务信息详细\r\nexport function getVolaTask(volaTaskInfoId) {\r\n  return request({\r\n    url: '/dataAnalysis/volaTask/' + volaTaskInfoId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增调度任务信息\r\nexport function addVolaTask(data) {\r\n  return request({\r\n    url: '/dataAnalysis/volaTask',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改调度任务信息\r\nexport function updateVolaTask(data) {\r\n  return request({\r\n    url: '/dataAnalysis/volaTask',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除调度任务信息\r\nexport function delVolaTask(volaTaskInfoId) {\r\n  return request({\r\n    url: '/dataAnalysis/volaTask/' + volaTaskInfoId,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,WAAWA,CAACC,cAAc,EAAE;EAC1C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGI,cAAc;IAC/CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,cAAcA,CAACD,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,WAAWA,CAACJ,cAAc,EAAE;EAC1C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGI,cAAc;IAC/CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}