<template>
  <div class="app-container">
    <card>
      <search-form v-show="showSearch" :modelValue="queryParams" :fields="searchFields" :show-search="showSearch"
        @search="handleQuery" @reset="resetQuery" @update:model-value="val => queryParams = val" />
    </card>
    <card class="mt10">
      <el-row :gutter="10" class="mb8" style="display: flex;justify-content: flex-end;">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['exam:eaxmMatt:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['exam:eaxmMatt:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['exam:eaxmMatt:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['exam:eaxmMatt:export']">导出</el-button>
        </el-col>
      </el-row>
      <common-table :loading="loading" :data="eaxmMattList" :total="total" :columns="columns"
        :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @selection-change="handleSelectionChange"
        @pagination="getList">
        <template #operation="{ row }">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(row)"
            v-hasPermi="['exam:eaxmMatt:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(row)"
            v-hasPermi="['exam:eaxmMatt:remove']">删除</el-button>
        </template>
      </common-table>
    </card>

    <!-- 添加或修改检查事项管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="auto" inline>
        <el-form-item label="医保区划" prop="admdvs">
          <el-cascader
            v-model="form.admdvs"
            :style="`width: ${inputWidth}px`"
            :options="admdvsOptions"
            placeholder="请选择医保区划"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="检查事项" prop="examMatt">
          <el-input v-model="form.examMatt" placeholder="请输入检查事项" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="检查类别" prop="examMattType">
          <el-input v-model="form.examMattType" placeholder="请输入检查类别" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="检查对象名称" prop="examObjName">
          <el-input v-model="form.examObjName" placeholder="请输入检查对象名称" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="检查方式" prop="examWay">
          <el-input v-model="form.examWay" placeholder="请输入检查方式" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="检查主体名称" prop="examMainName">
          <el-input v-model="form.examMainName" placeholder="请输入检查主体名称" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="检查依据" prop="examEvid">
          <el-input v-model="form.examEvid" placeholder="请输入检查依据" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="所属部门编码" prop="locDeptCodg">
          <el-input v-model="form.locDeptCodg" placeholder="请输入所属部门编码" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="所属部门名称" prop="locDeptName">
          <el-input v-model="form.locDeptName" placeholder="请输入所属部门名称" :style="`width: ${inputWidth}px`" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listEaxmMatt, getEaxmMatt, delEaxmMatt, addEaxmMatt, updateEaxmMatt } from "@/api/exam/eaxmMatt";
import CommonTable from "@/components/CommonTable";
import SearchForm from "@/components/SearchForm";
import card from '@/components/card'
import columns from "./columns";
import searchFields from "./searchFields";
import {admdvsList} from "@/api/system/admdvs";

export default {
  name: "EaxmMatt",
  components: {
    CommonTable,
    SearchForm,
    card
  },
  data() {
    return {
      // 表单输入框统一宽度
      inputWidth: 240,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检查事项管理表格数据
      eaxmMattList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表格列配置
      columns: columns,
      // 筛选字段配置
      searchFields: searchFields,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        examMattListId: null,
        admdvs: null,
        examMattType: null,
        examMatt: null,
        examObjName: null,
        mattType: null,
        examWay: null,
        examMainName: null,
        examEvid: null,
        locDeptCodg: null,
        locDeptName: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        examMattListId: [
          { required: true, message: "检查事项清单id不能为空", trigger: "blur" }
        ],
        admdvs: [
          { required: true, message: "医保区划不能为空", trigger: "blur" }
        ],
        valiFlag: [
          { required: true, message: "有效标志不能为空", trigger: "blur" }
        ],
        rid: [
          { required: true, message: "数据唯一记录号不能为空", trigger: "blur" }
        ],
        crterId: [
          { required: true, message: "创建人id不能为空", trigger: "blur" }
        ],
        crterName: [
          { required: true, message: "创建人姓名不能为空", trigger: "blur" }
        ],
        crteTime: [
          { required: true, message: "数据创建时间不能为空", trigger: "blur" }
        ],
        crteOptinsNo: [
          { required: true, message: "创建机构编号不能为空", trigger: "blur" }
        ],
        opterId: [
          { required: true, message: "经办人id不能为空", trigger: "blur" }
        ],
        opterName: [
          { required: true, message: "经办人姓名不能为空", trigger: "blur" }
        ],
        optTime: [
          { required: true, message: "经办时间不能为空", trigger: "blur" }
        ],
        optinsNo: [
          { required: true, message: "经办机构编号不能为空", trigger: "blur" }
        ],
        updtTime: [
          { required: true, message: "数据更新时间不能为空", trigger: "blur" }
        ]
      },
      admdvsOptions: []
    };
  },
  created() {
    this.getList();
    this.getAdmdvs();
  },
  methods: {
    getAdmdvs(){
      admdvsList({}).then(response => {
        this.searchFields[0].options = response.data
        this.admdvsOptions = response.data
      }).catch(error => {
        console.error('获取数据出错:', error);
      });
    },
    /** 查询检查事项管理列表 */
    getList() {
      this.loading = true;
      listEaxmMatt(this.queryParams).then(response => {
        this.eaxmMattList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取数据出错:', error);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        examMattListId: null,
        admdvs: null,
        examMattType: null,
        examMatt: null,
        examObjName: null,
        mattType: null,
        examWay: null,
        examMainName: null,
        examEvid: null,
        locDeptCodg: null,
        locDeptName: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      if (params) {
        // 将搜索表单的数据合并到queryParams中
        Object.keys(params).forEach(key => {
          // 只合并有值的字段，忽略空值
          if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
            this.queryParams[key] = params[key];
          }
        });
      }
      if(this.queryParams.admdvs){
        this.queryParams.admdvs = this.fixedAdmdvs(this.queryParams.admdvs)
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置查询参数（保留分页参数）
      const pageNum = this.queryParams.pageNum;
      const pageSize = this.queryParams.pageSize;

      // 初始化查询参数
      this.queryParams = {
        pageNum: pageNum,
        pageSize: pageSize
      };

      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.examMattListId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检查事项管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const examMattListId = row.examMattListId || this.ids
      getEaxmMatt(examMattListId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改检查事项管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.admdvs = this.fixedAdmdvs(this.form.admdvs)
          if (this.form.examMattListId != null) {
            updateEaxmMatt(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEaxmMatt(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const examMattListIds = row.examMattListId || this.ids;
      this.$modal.confirm('是否确认删除检查事项管理编号为"' + examMattListIds + '"的数据项？').then(function () {
        return delEaxmMatt(examMattListIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('exam/eaxmMatt/export', {
        ...this.queryParams
      }, `eaxmMatt_${new Date().getTime()}.xlsx`)
    },
    fixedAdmdvs(val) {
      if (typeof val == "string" && val.constructor == String) {
        return val;
      }
      if (val && val.length) {
        return val[val.length - 1];
      }
      return null;
    }
  }
};
</script>
