{"remainingRequest": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\basic\\volaBook\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\basic\\volaBook\\index.vue", "mtime": 1750383268000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0Vm9sYUJvb2ssIGdldFZvbGFCb29rLCBkZWxWb2xhQm9vaywgYWRkVm9sYUJvb2ssIHVwZGF0ZVZvbGFCb29rIH0gZnJvbSAiQC9hcGkvYmFzaWMvdm9sYUJvb2siOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJWb2xhQm9vayIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDnianku7fkuabnn6Xor4bkv6Hmga/ooajmoLzmlbDmja4NCiAgICAgIHZvbGFCb29rTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgcnVsZUlkOiBudWxsLA0KICAgICAgICBydWxlTmFtZTogbnVsbCwNCiAgICAgICAgYWRtZHZzTmFtZTogbnVsbCwNCiAgICAgICAgaGlsaXN0TmFtZUE6IG51bGwsDQogICAgICAgIGhpbGlzdENvZGVBOiBudWxsLA0KICAgICAgICBoaWxpc3ROYW1lQjogbnVsbCwNCiAgICAgICAgaGlsaXN0Q29kZUI6IG51bGwsDQogICAgICAgIGFnZTogbnVsbCwNCiAgICAgICAgZ2VuZDogbnVsbCwNCiAgICAgICAgY250OiBudWxsLA0KICAgICAgICB0aW1lczogbnVsbCwNCiAgICAgICAgZGVwdDogbnVsbCwNCiAgICAgICAgZGlhZzogbnVsbCwNCiAgICAgICAgbWVkVHlwZTogbnVsbCwNCiAgICAgICAgdHlwZTogbnVsbCwNCiAgICAgICAgdm9sYVR5cGU6IG51bGwsDQogICAgICAgIHZvbGFOYW1lOiBudWxsLA0KICAgICAgICB2ZXJzaW9uOiBudWxsLA0KICAgICAgICBkZXNjcmliZXQ6IG51bGwsDQogICAgICAgIGFjY29yZGluZzogbnVsbCwNCiAgICAgICAgdmFsaUZsYWc6IG51bGwsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivoueJqeS7t+S5puefpeivhuS/oeaBr+WIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdFZvbGFCb29rKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnZvbGFCb29rTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIHZvbGFCb29rSWQ6IG51bGwsDQogICAgICAgIHJ1bGVJZDogbnVsbCwNCiAgICAgICAgcnVsZU5hbWU6IG51bGwsDQogICAgICAgIGFkbWR2c05hbWU6IG51bGwsDQogICAgICAgIGhpbGlzdE5hbWVBOiBudWxsLA0KICAgICAgICBoaWxpc3RDb2RlQTogbnVsbCwNCiAgICAgICAgaGlsaXN0TmFtZUI6IG51bGwsDQogICAgICAgIGhpbGlzdENvZGVCOiBudWxsLA0KICAgICAgICBhZ2U6IG51bGwsDQogICAgICAgIGdlbmQ6IG51bGwsDQogICAgICAgIGNudDogbnVsbCwNCiAgICAgICAgdGltZXM6IG51bGwsDQogICAgICAgIGRlcHQ6IG51bGwsDQogICAgICAgIGRpYWc6IG51bGwsDQogICAgICAgIG1lZFR5cGU6IG51bGwsDQogICAgICAgIHR5cGU6IG51bGwsDQogICAgICAgIHZvbGFUeXBlOiBudWxsLA0KICAgICAgICB2b2xhTmFtZTogbnVsbCwNCiAgICAgICAgdmVyc2lvbjogbnVsbCwNCiAgICAgICAgZGVzY3JpYmV0OiBudWxsLA0KICAgICAgICBhY2NvcmRpbmc6IG51bGwsDQogICAgICAgIHZhbGlGbGFnOiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0udm9sYUJvb2tJZCkNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9PTENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOeJqeS7t+S5puefpeivhuS/oeaBryI7DQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgY29uc3Qgdm9sYUJvb2tJZCA9IHJvdy52b2xhQm9va0lkIHx8IHRoaXMuaWRzDQogICAgICBnZXRWb2xhQm9vayh2b2xhQm9va0lkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnnianku7fkuabnn6Xor4bkv6Hmga8iOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0udm9sYUJvb2tJZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVWb2xhQm9vayh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZFZvbGFCb29rKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IHZvbGFCb29rSWRzID0gcm93LnZvbGFCb29rSWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTnianku7fkuabnn6Xor4bkv6Hmga/nvJblj7fkuLoiJyArIHZvbGFCb29rSWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gZGVsVm9sYUJvb2sodm9sYUJvb2tJZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdiYXNpYy92b2xhQm9vay9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0sIGB2b2xhQm9va18ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8RA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/basic/volaBook", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"规则ID\" prop=\"ruleId\">\r\n        <el-input\r\n          v-model=\"queryParams.ruleId\"\r\n          placeholder=\"请输入规则ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"规则名称\" prop=\"ruleName\">\r\n        <el-input\r\n          v-model=\"queryParams.ruleName\"\r\n          placeholder=\"请输入规则名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"区划名称\" prop=\"admdvsName\">\r\n        <el-input\r\n          v-model=\"queryParams.admdvsName\"\r\n          placeholder=\"请输入区划名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"医保项目A编码\" prop=\"hilistCodeA\">\r\n        <el-input\r\n          v-model=\"queryParams.hilistCodeA\"\r\n          placeholder=\"请输入医保项目A编码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"医保项目B编码\" prop=\"hilistCodeB\">\r\n        <el-input\r\n          v-model=\"queryParams.hilistCodeB\"\r\n          placeholder=\"请输入医保项目B编码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"年龄\" prop=\"age\">\r\n        <el-input\r\n          v-model=\"queryParams.age\"\r\n          placeholder=\"请输入年龄\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"性别\" prop=\"gend\">\r\n        <el-input\r\n          v-model=\"queryParams.gend\"\r\n          placeholder=\"请输入性别\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"数量\" prop=\"cnt\">\r\n        <el-input\r\n          v-model=\"queryParams.cnt\"\r\n          placeholder=\"请输入数量\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"时长\" prop=\"times\">\r\n        <el-input\r\n          v-model=\"queryParams.times\"\r\n          placeholder=\"请输入时长\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"科室\" prop=\"dept\">\r\n        <el-input\r\n          v-model=\"queryParams.dept\"\r\n          placeholder=\"请输入科室\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"诊断\" prop=\"diag\">\r\n        <el-input\r\n          v-model=\"queryParams.diag\"\r\n          placeholder=\"请输入诊断\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"违规大类\" prop=\"volaName\">\r\n        <el-input\r\n          v-model=\"queryParams.volaName\"\r\n          placeholder=\"请输入违规大类\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"物价书版本号\" prop=\"version\">\r\n        <el-input\r\n          v-model=\"queryParams.version\"\r\n          placeholder=\"请输入物价书版本号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"有效标志\" prop=\"valiFlag\">\r\n        <el-input\r\n          v-model=\"queryParams.valiFlag\"\r\n          placeholder=\"请输入有效标志\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['basic:volaBook:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['basic:volaBook:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['basic:volaBook:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['basic:volaBook:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"volaBookList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"物价书知识信息ID\" align=\"center\" prop=\"volaBookId\" />\r\n      <el-table-column label=\"规则ID\" align=\"center\" prop=\"ruleId\" />\r\n      <el-table-column label=\"规则名称\" align=\"center\" prop=\"ruleName\" />\r\n      <el-table-column label=\"区划名称\" align=\"center\" prop=\"admdvsName\" />\r\n      <el-table-column label=\"医保项目A名称\" align=\"center\" prop=\"hilistNameA\" />\r\n      <el-table-column label=\"医保项目A编码\" align=\"center\" prop=\"hilistCodeA\" />\r\n      <el-table-column label=\"医保项目B名称\" align=\"center\" prop=\"hilistNameB\" />\r\n      <el-table-column label=\"医保项目B编码\" align=\"center\" prop=\"hilistCodeB\" />\r\n      <el-table-column label=\"年龄\" align=\"center\" prop=\"age\" />\r\n      <el-table-column label=\"性别\" align=\"center\" prop=\"gend\" />\r\n      <el-table-column label=\"数量\" align=\"center\" prop=\"cnt\" />\r\n      <el-table-column label=\"时长\" align=\"center\" prop=\"times\" />\r\n      <el-table-column label=\"科室\" align=\"center\" prop=\"dept\" />\r\n      <el-table-column label=\"诊断\" align=\"center\" prop=\"diag\" />\r\n      <el-table-column label=\"医疗类别\" align=\"center\" prop=\"medType\" />\r\n      <el-table-column label=\"违规类别\" align=\"center\" prop=\"type\" />\r\n      <el-table-column label=\"违规类型\" align=\"center\" prop=\"volaType\" />\r\n      <el-table-column label=\"违规大类\" align=\"center\" prop=\"volaName\" />\r\n      <el-table-column label=\"物价书版本号\" align=\"center\" prop=\"version\" />\r\n      <el-table-column label=\"问题描述\" align=\"center\" prop=\"describet\" />\r\n      <el-table-column label=\"政策依据\" align=\"center\" prop=\"according\" />\r\n      <el-table-column label=\"有效标志\" align=\"center\" prop=\"valiFlag\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['basic:volaBook:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['basic:volaBook:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改物价书知识信息对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"规则ID\" prop=\"ruleId\">\r\n          <el-input v-model=\"form.ruleId\" placeholder=\"请输入规则ID\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"规则名称\" prop=\"ruleName\">\r\n          <el-input v-model=\"form.ruleName\" placeholder=\"请输入规则名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"区划名称\" prop=\"admdvsName\">\r\n          <el-input v-model=\"form.admdvsName\" placeholder=\"请输入区划名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"医保项目A名称\" prop=\"hilistNameA\">\r\n          <el-input v-model=\"form.hilistNameA\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"医保项目A编码\" prop=\"hilistCodeA\">\r\n          <el-input v-model=\"form.hilistCodeA\" placeholder=\"请输入医保项目A编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"医保项目B名称\" prop=\"hilistNameB\">\r\n          <el-input v-model=\"form.hilistNameB\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"医保项目B编码\" prop=\"hilistCodeB\">\r\n          <el-input v-model=\"form.hilistCodeB\" placeholder=\"请输入医保项目B编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"年龄\" prop=\"age\">\r\n          <el-input v-model=\"form.age\" placeholder=\"请输入年龄\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"性别\" prop=\"gend\">\r\n          <el-input v-model=\"form.gend\" placeholder=\"请输入性别\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"数量\" prop=\"cnt\">\r\n          <el-input v-model=\"form.cnt\" placeholder=\"请输入数量\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"时长\" prop=\"times\">\r\n          <el-input v-model=\"form.times\" placeholder=\"请输入时长\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"科室\" prop=\"dept\">\r\n          <el-input v-model=\"form.dept\" placeholder=\"请输入科室\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"诊断\" prop=\"diag\">\r\n          <el-input v-model=\"form.diag\" placeholder=\"请输入诊断\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"违规大类\" prop=\"volaName\">\r\n          <el-input v-model=\"form.volaName\" placeholder=\"请输入违规大类\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"物价书版本号\" prop=\"version\">\r\n          <el-input v-model=\"form.version\" placeholder=\"请输入物价书版本号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"问题描述\" prop=\"describet\">\r\n          <el-input v-model=\"form.describet\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"政策依据\" prop=\"according\">\r\n          <el-input v-model=\"form.according\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"有效标志\" prop=\"valiFlag\">\r\n          <el-input v-model=\"form.valiFlag\" placeholder=\"请输入有效标志\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listVolaBook, getVolaBook, delVolaBook, addVolaBook, updateVolaBook } from \"@/api/basic/volaBook\";\r\n\r\nexport default {\r\n  name: \"VolaBook\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 物价书知识信息表格数据\r\n      volaBookList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        ruleId: null,\r\n        ruleName: null,\r\n        admdvsName: null,\r\n        hilistNameA: null,\r\n        hilistCodeA: null,\r\n        hilistNameB: null,\r\n        hilistCodeB: null,\r\n        age: null,\r\n        gend: null,\r\n        cnt: null,\r\n        times: null,\r\n        dept: null,\r\n        diag: null,\r\n        medType: null,\r\n        type: null,\r\n        volaType: null,\r\n        volaName: null,\r\n        version: null,\r\n        describet: null,\r\n        according: null,\r\n        valiFlag: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询物价书知识信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listVolaBook(this.queryParams).then(response => {\r\n        this.volaBookList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        volaBookId: null,\r\n        ruleId: null,\r\n        ruleName: null,\r\n        admdvsName: null,\r\n        hilistNameA: null,\r\n        hilistCodeA: null,\r\n        hilistNameB: null,\r\n        hilistCodeB: null,\r\n        age: null,\r\n        gend: null,\r\n        cnt: null,\r\n        times: null,\r\n        dept: null,\r\n        diag: null,\r\n        medType: null,\r\n        type: null,\r\n        volaType: null,\r\n        volaName: null,\r\n        version: null,\r\n        describet: null,\r\n        according: null,\r\n        valiFlag: null,\r\n        createBy: null,\r\n        createTime: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.volaBookId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加物价书知识信息\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const volaBookId = row.volaBookId || this.ids\r\n      getVolaBook(volaBookId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改物价书知识信息\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.volaBookId != null) {\r\n            updateVolaBook(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addVolaBook(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const volaBookIds = row.volaBookId || this.ids;\r\n      this.$modal.confirm('是否确认删除物价书知识信息编号为\"' + volaBookIds + '\"的数据项？').then(function() {\r\n        return delVolaBook(volaBookIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('basic/volaBook/export', {\r\n        ...this.queryParams\r\n      }, `volaBook_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}