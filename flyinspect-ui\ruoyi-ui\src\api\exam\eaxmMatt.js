import request from '@/utils/request'

// 查询检查事项管理列表
export function listEaxmMatt(query) {
  return request({
    url: '/exam/eaxmMatt/list',
    method: 'get',
    params: query
  })
}

// 查询检查事项管理详细
export function getEaxmMatt(examMattListId) {
  return request({
    url: '/exam/eaxmMatt/' + examMattListId,
    method: 'get'
  })
}

// 新增检查事项管理
export function addEaxmMatt(data) {
  return request({
    url: '/exam/eaxmMatt',
    method: 'post',
    data: data
  })
}

// 修改检查事项管理
export function updateEaxmMatt(data) {
  return request({
    url: '/exam/eaxmMatt',
    method: 'put',
    data: data
  })
}

// 删除检查事项管理
export function delEaxmMatt(examMattListId) {
  return request({
    url: '/exam/eaxmMatt/' + examMattListId,
    method: 'delete'
  })
}
