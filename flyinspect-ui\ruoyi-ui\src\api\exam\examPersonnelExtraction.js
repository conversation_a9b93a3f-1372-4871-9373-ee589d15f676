import request from '@/utils/request'

export function personTaskRandom(query) {
    return request({
        url: '/exam/examTask/personTaskRandom',
        method: 'post',
        data: query
    })
}

export function queryPersonTaskList(query) {
    return request({
        url: '/exam/examTask/queryPersonTaskList',
        method: 'post',
        data: query
    })
}

export function updateExamFlag(query) {
    return request({
        url: '/exam/examTask/updateExamFlag',
        method: 'post',
        data: query
    })
}