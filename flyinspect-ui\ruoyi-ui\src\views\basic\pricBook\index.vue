<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目编码" prop="hilistCode">
        <el-input
          v-model="queryParams.hilistCode"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计价单位" prop="uint">
        <el-input
          v-model="queryParams.uint"
          placeholder="请输入计价单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="特定价格" prop="specPric">
        <el-input
          v-model="queryParams.specPric"
          placeholder="请输入特定价格"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="三级价格" prop="thPric">
        <el-input
          v-model="queryParams.thPric"
          placeholder="请输入三级价格"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="二级价格" prop="twPric">
        <el-input
          v-model="queryParams.twPric"
          placeholder="请输入二级价格"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一级价格" prop="onPric">
        <el-input
          v-model="queryParams.onPric"
          placeholder="请输入一级价格"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="有效标志" prop="volaFlag">
        <el-input
          v-model="queryParams.volaFlag"
          placeholder="请输入有效标志"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['basic:pricBook:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['basic:pricBook:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['basic:pricBook:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['basic:pricBook:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="pricBookList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="物价书ID" align="center" prop="pricBookId" />
      <el-table-column label="项目编码" align="center" prop="hilistCode" />
      <el-table-column label="项目名称" align="center" prop="hilistName" />
      <el-table-column label="计价单位" align="center" prop="uint" />
      <el-table-column label="特定价格" align="center" prop="specPric" />
      <el-table-column label="三级价格" align="center" prop="thPric" />
      <el-table-column label="二级价格" align="center" prop="twPric" />
      <el-table-column label="一级价格" align="center" prop="onPric" />
      <el-table-column label="项目内涵" align="center" prop="projectConnnation" />
      <el-table-column label="除外内容" align="center" prop="excludedContent" />
      <el-table-column label="说明" align="center" prop="memo" />
      <el-table-column label="有效标志" align="center" prop="volaFlag" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['basic:pricBook:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['basic:pricBook:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改物价书信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目编码" prop="hilistCode">
          <el-input v-model="form.hilistCode" placeholder="请输入项目编码" />
        </el-form-item>
        <el-form-item label="项目名称" prop="hilistName">
          <el-input v-model="form.hilistName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="计价单位" prop="uint">
          <el-input v-model="form.uint" placeholder="请输入计价单位" />
        </el-form-item>
        <el-form-item label="特定价格" prop="specPric">
          <el-input v-model="form.specPric" placeholder="请输入特定价格" />
        </el-form-item>
        <el-form-item label="三级价格" prop="thPric">
          <el-input v-model="form.thPric" placeholder="请输入三级价格" />
        </el-form-item>
        <el-form-item label="二级价格" prop="twPric">
          <el-input v-model="form.twPric" placeholder="请输入二级价格" />
        </el-form-item>
        <el-form-item label="一级价格" prop="onPric">
          <el-input v-model="form.onPric" placeholder="请输入一级价格" />
        </el-form-item>
        <el-form-item label="项目内涵" prop="projectConnnation">
          <el-input v-model="form.projectConnnation" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="除外内容">
          <editor v-model="form.excludedContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="说明" prop="memo">
          <el-input v-model="form.memo" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="有效标志" prop="volaFlag">
          <el-input v-model="form.volaFlag" placeholder="请输入有效标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPricBook, getPricBook, delPricBook, addPricBook, updatePricBook } from "@/api/basic/pricBook";

export default {
  name: "PricBook",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物价书信息表格数据
      pricBookList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        hilistCode: null,
        hilistName: null,
        uint: null,
        specPric: null,
        thPric: null,
        twPric: null,
        onPric: null,
        projectConnnation: null,
        excludedContent: null,
        memo: null,
        volaFlag: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        hilistCode: [
          { required: true, message: "项目编码不能为空", trigger: "blur" }
        ],
        hilistName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" }
        ],
        volaFlag: [
          { required: true, message: "有效标志不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询物价书信息列表 */
    getList() {
      this.loading = true;
      listPricBook(this.queryParams).then(response => {
        this.pricBookList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        pricBookId: null,
        hilistCode: null,
        hilistName: null,
        uint: null,
        specPric: null,
        thPric: null,
        twPric: null,
        onPric: null,
        projectConnnation: null,
        excludedContent: null,
        memo: null,
        volaFlag: null,
        createBy: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.pricBookId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物价书信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const pricBookId = row.pricBookId || this.ids
      getPricBook(pricBookId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改物价书信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.pricBookId != null) {
            updatePricBook(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPricBook(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const pricBookIds = row.pricBookId || this.ids;
      this.$modal.confirm('是否确认删除物价书信息编号为"' + pricBookIds + '"的数据项？').then(function() {
        return delPricBook(pricBookIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('basic/pricBook/export', {
        ...this.queryParams
      }, `pricBook_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
