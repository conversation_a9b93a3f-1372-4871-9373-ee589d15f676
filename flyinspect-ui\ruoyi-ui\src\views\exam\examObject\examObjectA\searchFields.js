// 定义筛选字段配置
export default self => [
  {
    label: '医保区划',
    prop: 'admdvs',
    component: 'el-cascader',
    multiple: false,
    checkStrictly: false,
    options: [],
    attrs: {
      placeholder: '请选择医保区划',
      clearable: true
    }
  },
  {
    label: '检查年月',
    prop: 'year',
    type: 'month',
    component: 'el-date-picker',
    attrs: {
      placeholder: '请输入检查年月',
      clearable: true,
      valueFormat: 'yyyyMM'
    }
  },
  {
    label: '检查对象名称',
    prop: 'legentName',
    component: 'el-input',
    attrs: {
      placeholder: '请输入检查对象名称',
      clearable: true
    }
  },
  {
    label: '检查对象等级',
    prop: 'legentLv',
    component: 'el-select',
    options: [],
    attrs: {
      placeholder: '请选择检查对象等级',
      clearable: true
    }
  },
  {
    label: '社会信用代码',
    prop: 'uscc',
    component: 'el-input',
    attrs: {
      placeholder: '请输入社会信用代码',
      clearable: true
    }
  },
  {
    label: '登记机关',
    prop: 'aprvEstaDept',
    component: 'el-input',
    attrs: {
      placeholder: '请输入登记机关',
      clearable: true
    }
  },
  {
    label: '经济类型',
    prop: 'econType',
    component: 'el-select',
    options: [],
    attrs: {
      placeholder: '请选择经济类型',
      clearable: true
    }
  },
  {
    label: '检查对象类型',
    prop: 'examObjType',
    component: 'el-select',
    options: [],
    attrs: {
      placeholder: '请选择检查对象类型',
      clearable: true
    }
  },
  {
    label: '是否慢病',
    prop: 'chronicFlag',
    component: 'el-select',
    options: [],
    attrs: {
      placeholder: '请选择是否慢病',
      clearable: true
    }
  },
  {
    label: '是否特病',
    prop: 'specialFlag',
    component: 'el-select',
    options: [],
    attrs: {
      placeholder: '请选择是否特病',
      clearable: true
    }
  },
  {
    label: '信用等级',
    prop: 'examCreditLv',
    component: 'el-select',
    options: [],
    attrs: {
      placeholder: '请选择信用等级',
      clearable: true
    }
  }
];
