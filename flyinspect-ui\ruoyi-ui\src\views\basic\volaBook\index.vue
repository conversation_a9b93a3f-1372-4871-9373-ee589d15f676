<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="规则ID" prop="ruleId">
        <el-input
          v-model="queryParams.ruleId"
          placeholder="请输入规则ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="规则名称" prop="ruleName">
        <el-input
          v-model="queryParams.ruleName"
          placeholder="请输入规则名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="区划名称" prop="admdvsName">
        <el-input
          v-model="queryParams.admdvsName"
          placeholder="请输入区划名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医保项目A编码" prop="hilistCodeA">
        <el-input
          v-model="queryParams.hilistCodeA"
          placeholder="请输入医保项目A编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医保项目B编码" prop="hilistCodeB">
        <el-input
          v-model="queryParams.hilistCodeB"
          placeholder="请输入医保项目B编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年龄" prop="age">
        <el-input
          v-model="queryParams.age"
          placeholder="请输入年龄"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="性别" prop="gend">
        <el-input
          v-model="queryParams.gend"
          placeholder="请输入性别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数量" prop="cnt">
        <el-input
          v-model="queryParams.cnt"
          placeholder="请输入数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="时长" prop="times">
        <el-input
          v-model="queryParams.times"
          placeholder="请输入时长"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="科室" prop="dept">
        <el-input
          v-model="queryParams.dept"
          placeholder="请输入科室"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="诊断" prop="diag">
        <el-input
          v-model="queryParams.diag"
          placeholder="请输入诊断"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="违规大类" prop="volaName">
        <el-input
          v-model="queryParams.volaName"
          placeholder="请输入违规大类"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物价书版本号" prop="version">
        <el-input
          v-model="queryParams.version"
          placeholder="请输入物价书版本号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="有效标志" prop="valiFlag">
        <el-input
          v-model="queryParams.valiFlag"
          placeholder="请输入有效标志"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['basic:volaBook:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['basic:volaBook:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['basic:volaBook:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['basic:volaBook:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="volaBookList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="物价书知识信息ID" align="center" prop="volaBookId" />
      <el-table-column label="规则ID" align="center" prop="ruleId" />
      <el-table-column label="规则名称" align="center" prop="ruleName" />
      <el-table-column label="区划名称" align="center" prop="admdvsName" />
      <el-table-column label="医保项目A名称" align="center" prop="hilistNameA" />
      <el-table-column label="医保项目A编码" align="center" prop="hilistCodeA" />
      <el-table-column label="医保项目B名称" align="center" prop="hilistNameB" />
      <el-table-column label="医保项目B编码" align="center" prop="hilistCodeB" />
      <el-table-column label="年龄" align="center" prop="age" />
      <el-table-column label="性别" align="center" prop="gend" />
      <el-table-column label="数量" align="center" prop="cnt" />
      <el-table-column label="时长" align="center" prop="times" />
      <el-table-column label="科室" align="center" prop="dept" />
      <el-table-column label="诊断" align="center" prop="diag" />
      <el-table-column label="医疗类别" align="center" prop="medType" />
      <el-table-column label="违规类别" align="center" prop="type" />
      <el-table-column label="违规类型" align="center" prop="volaType" />
      <el-table-column label="违规大类" align="center" prop="volaName" />
      <el-table-column label="物价书版本号" align="center" prop="version" />
      <el-table-column label="问题描述" align="center" prop="describet" />
      <el-table-column label="政策依据" align="center" prop="according" />
      <el-table-column label="有效标志" align="center" prop="valiFlag" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['basic:volaBook:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['basic:volaBook:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改物价书知识信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="规则ID" prop="ruleId">
          <el-input v-model="form.ruleId" placeholder="请输入规则ID" />
        </el-form-item>
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="区划名称" prop="admdvsName">
          <el-input v-model="form.admdvsName" placeholder="请输入区划名称" />
        </el-form-item>
        <el-form-item label="医保项目A名称" prop="hilistNameA">
          <el-input v-model="form.hilistNameA" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="医保项目A编码" prop="hilistCodeA">
          <el-input v-model="form.hilistCodeA" placeholder="请输入医保项目A编码" />
        </el-form-item>
        <el-form-item label="医保项目B名称" prop="hilistNameB">
          <el-input v-model="form.hilistNameB" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="医保项目B编码" prop="hilistCodeB">
          <el-input v-model="form.hilistCodeB" placeholder="请输入医保项目B编码" />
        </el-form-item>
        <el-form-item label="年龄" prop="age">
          <el-input v-model="form.age" placeholder="请输入年龄" />
        </el-form-item>
        <el-form-item label="性别" prop="gend">
          <el-input v-model="form.gend" placeholder="请输入性别" />
        </el-form-item>
        <el-form-item label="数量" prop="cnt">
          <el-input v-model="form.cnt" placeholder="请输入数量" />
        </el-form-item>
        <el-form-item label="时长" prop="times">
          <el-input v-model="form.times" placeholder="请输入时长" />
        </el-form-item>
        <el-form-item label="科室" prop="dept">
          <el-input v-model="form.dept" placeholder="请输入科室" />
        </el-form-item>
        <el-form-item label="诊断" prop="diag">
          <el-input v-model="form.diag" placeholder="请输入诊断" />
        </el-form-item>
        <el-form-item label="违规大类" prop="volaName">
          <el-input v-model="form.volaName" placeholder="请输入违规大类" />
        </el-form-item>
        <el-form-item label="物价书版本号" prop="version">
          <el-input v-model="form.version" placeholder="请输入物价书版本号" />
        </el-form-item>
        <el-form-item label="问题描述" prop="describet">
          <el-input v-model="form.describet" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="政策依据" prop="according">
          <el-input v-model="form.according" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="有效标志" prop="valiFlag">
          <el-input v-model="form.valiFlag" placeholder="请输入有效标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listVolaBook, getVolaBook, delVolaBook, addVolaBook, updateVolaBook } from "@/api/basic/volaBook";

export default {
  name: "VolaBook",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物价书知识信息表格数据
      volaBookList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ruleId: null,
        ruleName: null,
        admdvsName: null,
        hilistNameA: null,
        hilistCodeA: null,
        hilistNameB: null,
        hilistCodeB: null,
        age: null,
        gend: null,
        cnt: null,
        times: null,
        dept: null,
        diag: null,
        medType: null,
        type: null,
        volaType: null,
        volaName: null,
        version: null,
        describet: null,
        according: null,
        valiFlag: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询物价书知识信息列表 */
    getList() {
      this.loading = true;
      listVolaBook(this.queryParams).then(response => {
        this.volaBookList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        volaBookId: null,
        ruleId: null,
        ruleName: null,
        admdvsName: null,
        hilistNameA: null,
        hilistCodeA: null,
        hilistNameB: null,
        hilistCodeB: null,
        age: null,
        gend: null,
        cnt: null,
        times: null,
        dept: null,
        diag: null,
        medType: null,
        type: null,
        volaType: null,
        volaName: null,
        version: null,
        describet: null,
        according: null,
        valiFlag: null,
        createBy: null,
        createTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.volaBookId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物价书知识信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const volaBookId = row.volaBookId || this.ids
      getVolaBook(volaBookId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改物价书知识信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.volaBookId != null) {
            updateVolaBook(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addVolaBook(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const volaBookIds = row.volaBookId || this.ids;
      this.$modal.confirm('是否确认删除物价书知识信息编号为"' + volaBookIds + '"的数据项？').then(function() {
        return delVolaBook(volaBookIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('basic/volaBook/export', {
        ...this.queryParams
      }, `volaBook_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
