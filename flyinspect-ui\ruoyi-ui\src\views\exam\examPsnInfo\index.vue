<template>
  <div class="app-container">
    <card>
      <search-form v-show="showSearch" :modelValue="queryParams" :fields="searchFields" :show-search="showSearch"
        @search="handleQuery" @reset="resetQuery" @update:model-value="val => queryParams = val" />
    </card>
    <card class="mt10">
      <el-row :gutter="10" class="mb8" style="display: flex;justify-content: flex-end;">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['exam:examPsnInfo:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['exam:examPsnInfo:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['exam:examPsnInfo:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['exam:examPsnInfo:export']"
          >导出</el-button>
        </el-col>
      </el-row>
      <common-table v-loading="loading" :data="examPsnInfoList" :total="total" :columns="columns"
        :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @selection-change="handleSelectionChange"
        @pagination="getList" :dict="dict">
        <template #operation="{ row }">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
            v-hasPermi="['exam:examPsnInfo:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(row)"
            v-hasPermi="['exam:examPsnInfo:remove']"
          >删除</el-button>
        </template>
      </common-table>
    </card>

    <!-- 添加或修改检查人员名录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="auto" inline>
        <el-form-item label="医保区划" prop="admdvs">
          <el-cascader v-model="form.admdvs" :style="`width: ${inputWidth}px`"  :options="admdvsOptions"
                       placeholder="请选择医保区划"></el-cascader>
        </el-form-item>
        <el-form-item label="检查人员帐号" prop="examPsnAcct">
          <el-input v-model="form.examPsnAcct" :style="`width: ${inputWidth}px`"  placeholder="请输入检查人员帐号" />
        </el-form-item>
        <el-form-item label="检查人员姓名" prop="examPsnName">
          <el-input v-model="form.examPsnName" :style="`width: ${inputWidth}px`"  placeholder="请输入检查人员姓名" />
        </el-form-item>
        <el-form-item label="人员性质" prop="psnNatu">
          <el-select v-model="form.psnNatu" placeholder="请选择人员性质" :style="`width: ${inputWidth}px`"h>
            <el-option v-for="dict in dict.type.psn_natu" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属部门编码" prop="locDeptCodg">
          <el-input v-model="form.locDeptCodg" placeholder="请输入所属部门编码" :style="`width: ${inputWidth}px`"  />
        </el-form-item>
        <el-form-item label="所属部门名称" prop="locDeptName">
          <el-input v-model="form.locDeptName" placeholder="请输入所属部门名称"  :style="`width: ${inputWidth}px`" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listExamPsnInfo, getExamPsnInfo, delExamPsnInfo, addExamPsnInfo, updateExamPsnInfo } from "@/api/exam/examPsnInfo";
import CommonTable from "@/components/CommonTable";
import SearchForm from "@/components/SearchForm";
import card from '@/components/card';
import columns from "./columns";
import searchFields from "./searchFields";
import {admdvsList} from "@/api/system/admdvs";


export default {
  name: "ExamPsnInfo",
  components: {
    CommonTable,
    SearchForm,
    card
  },
  dicts: ["whether_flag","psn_natu"],
  data() {
    return {
      // 表单输入框统一宽度
      inputWidth: 240,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检查人员名录表格数据
      examPsnInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表格列配置
      columns: columns,
      // 筛选字段配置
      searchFields: searchFields,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        admdvs: null,
        examPsnAcct: null,
        examPsnName: null,
        psnNatu: null,
        locDeptCodg: null,
        locDeptName: null,
        examFlag: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        admdvs: [
          { required: true, message: "检查人员医保区划不能为空", trigger: "blur" }
        ],
        examPsnAcct: [
          { required: true, message: "检查人员帐号不能为空", trigger: "blur" }
        ],
        examPsnName: [
          { required: true, message: "检查人员姓名不能为空", trigger: "blur" }
        ],
        locDeptCodg: [
          { required: true, message: "所属部门编码不能为空", trigger: "blur" }
        ],
        locDeptName: [
          { required: true, message: "所属部门名称不能为空", trigger: "blur" }
        ],
        examFlag: [
          { required: true, message: "抽取标志不能为空", trigger: "blur" }
        ],
        valiFlag: [
          { required: true, message: "有效标志不能为空", trigger: "blur" }
        ]
      },
      admdvsOptions: []
    };
  },
  created() {
    this.getList();
    this.getAdmdvs();
  },
  methods: {
    // 字典加载完成回调
    onDictReady(dict) {
      console.log('字典数据加载完成');
      this.initDictOptions();
    },
    // 初始化字典选项
    initDictOptions() {
      // 检查字段是否存在options属性，没有则添加
      const optionsFields = [
        { index: 2, dict: 'whether_flag', label: '抽取标志' },
      ];

      optionsFields.forEach(item => {
        if (this.searchFields[item.index]) {
          // 确保options属性存在
          if (!this.searchFields[item.index].options) {
            this.$set(this.searchFields[item.index], 'options', []);
          }
          // 获取字典数据
          const dictData = this.dict.type[item.dict];
          if (dictData && dictData.length > 0) {
            // 赋值字典
            this.searchFields[item.index].options = dictData;
          } else {
            console.warn(`字典数据为空: ${item.dict}`);
          }
        } else {
          console.warn(`未找到索引 ${item.index} 的搜索字段`);
        }
      });
    },
    getAdmdvs() {
      admdvsList({}).then(response => {
        if (response && response.data && Array.isArray(response.data)) {
          const processedData = response.data.map(item => {
            return {
              ...item,
              value: item.value || '',
              label: item.label || '',
              children: Array.isArray(item.children) ? item.children : []
            };
          });
          this.searchFields[0].options = processedData;
          this.admdvsOptions = processedData;
        } else {
          this.searchFields[0].options = [];
          this.admdvsOptions = [];
          console.warn('医保区划数据为空或格式不正确');
        }
      }).catch(error => {
        this.searchFields[0].options = [];
        this.admdvsOptions = [];
        console.error('获取医保区划数据出错:', error);
      });
    },
    /** 查询检查人员名录列表 */
    getList() {
      this.loading = true;
      listExamPsnInfo(this.queryParams).then(response => {
        this.examPsnInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取数据出错:', error);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        examPsnListId: null,
        admdvs: null,
        examPsnAcct: null,
        examPsnName: null,
        psnNatu: null,
        locDeptCodg: null,
        locDeptName: null,
        examFlag: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      if (params) {
        Object.keys(params).forEach(key => {
          if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
            this.queryParams[key] = params[key];
          }
        });
      }
      if (this.queryParams.admdvs) {
        this.queryParams.admdvs = this.fixedAdmdvs(this.queryParams.admdvs)
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      const pageNum = this.queryParams.pageNum;
      const pageSize = this.queryParams.pageSize;
      this.queryParams = {
        pageNum: pageNum,
        pageSize: pageSize
      };
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.examPsnListId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检查人员名录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const examPsnListId = row.examPsnListId || this.ids
      getExamPsnInfo(examPsnListId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改检查人员名录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.admdvs = this.fixedAdmdvs(this.form.admdvs)
          if (this.form.examPsnListId != null) {
            updateExamPsnInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addExamPsnInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const examPsnListIds = row.examPsnListId || this.ids;
      this.$modal.confirm('是否确认删除检查人员名录编号为"' + examPsnListIds + '"的数据项？').then(function() {
        return delExamPsnInfo(examPsnListIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('exam/examPsnInfo/export', {
        ...this.queryParams
      }, `examPsnInfo_${new Date().getTime()}.xlsx`)
    },
    fixedAdmdvs(val) {
      if (typeof val == "string" && val.constructor == String) {
        return val;
      }
      if (val && val.length) {
        return val[val.length - 1];
      }
      return null;
    }
  }
};
</script>
