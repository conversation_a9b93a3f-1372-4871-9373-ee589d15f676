<template>
  <div class="app-container">
    <card>
      <search-form v-show="showSearch" :modelValue="queryParams" :fields="searchFields" :show-search="showSearch"
        @search="handleQuery" @reset="resetQuery" @update:model-value="val => queryParams = val" />
    </card>
    <card class="mt10">
      <el-row :gutter="10" class="mb8" style="display: flex;justify-content: flex-end;">
        <el-col :span="1.5">
          <el-button type="danger" size="mini" @click="allInput">全部导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" :disabled="multiple" @click="handleInput">批量导入</el-button>
        </el-col>
      </el-row>
      <common-table :loading="loading" :data="examObjectList" :total="total" :columns="columns" :showOperation="false"
        :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @selection-change="handleSelectionChange"
        @pagination="getList" :dict="dict">
      </common-table>
    </card>
  </div>
</template>

<script>
import { listExamObject } from "@/api/exam/examObject";
import { batchInsertTaskObj,allInsertTaskObj } from "@/api/exam/eaxmPlan";
import { admdvsList } from "@/api/system/admdvs";
import CommonTable from "@/components/CommonTable/index.vue";
import SearchForm from "@/components/SearchForm/index.vue";
import card from '@/components/card/index.vue'
import columns from "./columns";
import searchFields from "./searchFields";
import {getToken} from "@/utils/auth";

export default {
  name: "ExamObject",
  props: {
    examTaskId: {
      type: String,
      default: ''
    },
    examObjType:  {
      type: String,
      default: ''
    }
  },
  components: {
    CommonTable,
    SearchForm,
    card
  },
  dicts: ["whether_flag","legent_lv","exam_obj_type","exam_credit_lv","econ_type"],
  data() {
    return {
      // 表单输入框统一宽度
      inputWidth: 240,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检查对象名录管理表格数据
      examObjectList: [],
      // 弹出层标题
      title: "",
      // 表格列配置
      columns: columns,
      // 筛选字段配置
      searchFields: searchFields(this),
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        examDpotName: null,
        uscc: null,
        admdvs: null,
        legentCode: null,
        legentName: null,
        legentLv: null,
        econType: null,
        aprvEstaDept: null,
        legrepName: null,
        legentAddr: null,
        regRegCode: null,
        aprvEstaDate: null,
        examObjType: null,
        bizScp: null,
        year: null,
        valiFlag: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null,
        importFlag: null,
        chronicFlag: null,
        specialFlag: null,
        desiFlag: null,
        examCreditLv: null,
        admdvsName: null,
        selcSeq: null
      },
      admdvsOptions: []
    };
  },
  created() {
    this.getList();
    this.getAdmdvs();
  },
  methods: {
    // 字典加载完成回调
    onDictReady(dict) {
      console.log('字典数据加载完成');
      this.initDictOptions();
    },
    // 初始化字典选项
    initDictOptions() {
      // 检查字段是否存在options属性，没有则添加
      const optionsFields = [
        { index: 3, dict: 'legent_lv', label: '检查对象等级' },
        { index: 6, dict: 'econ_type', label: '经济类型' },
        { index: 7, dict: 'exam_obj_type', label: '检查对象类型' },
        { index: 8, dict: 'whether_flag', label: '是否慢病' },
        { index: 9, dict: 'whether_flag', label: '是否特病' },
        { index: 10, dict: 'exam_credit_lv', label: '信用等级' }
      ];

      optionsFields.forEach(item => {
        if (this.searchFields[item.index]) {
          // 确保options属性存在
          if (!this.searchFields[item.index].options) {
            this.$set(this.searchFields[item.index], 'options', []);
          }
          // 获取字典数据
          const dictData = this.dict.type[item.dict];
          if (dictData && dictData.length > 0) {
            // 赋值字典
            this.searchFields[item.index].options = dictData;
          } else {
            console.warn(`字典数据为空: ${item.dict}`);
          }
        } else {
          console.warn(`未找到索引 ${item.index} 的搜索字段`);
        }
      });
    },
    getAdmdvs(){
      admdvsList({}).then(response => {
        this.searchFields[0].options = response.data
        this.admdvsOptions = response.data
      }).catch(error => {
        console.error('获取数据出错:', error);
      });
    },
    /** 查询检查对象名录管理列表 */
    getList() {
      this.loading = true;
      this.queryParams.importFlag = 0
      listExamObject(this.queryParams).then(response => {
        this.examObjectList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取数据出错:', error);
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      if (params) {
        // 将搜索表单的数据合并到queryParams中
        Object.keys(params).forEach(key => {
          // 只合并有值的字段，忽略空值
          if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
            this.queryParams[key] = params[key];
          }
        });
      }
      if(this.queryParams.admdvs){
        this.queryParams.admdvs = this.fixedAdmdvs(this.queryParams.admdvs)
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置查询参数（保留分页参数）
      const pageNum = this.queryParams.pageNum;
      const pageSize = this.queryParams.pageSize;
      // 初始化查询参数
      this.queryParams = {
        pageNum: pageNum,
        pageSize: pageSize
      };
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.examObjListId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    // 全部导入
    allInput(){
      const params = {
        ...this.queryParams,
        examTaskId: this.examTaskId,
        importFlag: 0
      }
      this.$modal.confirm('确认导入当前全部检查对象？').then(function () {
        return allInsertTaskObj(params);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("全量导入成功");
      }).catch(() => { });
    },
    /** 批量导入按钮操作 */
    handleInput(row) {
      const examObjListIds = row.examObjListId || this.ids;
      let params = {
        examObjListIds: examObjListIds,
        examTaskId: this.examTaskId,
        importFlag: 0
      }
      this.$modal.confirm('确认导入选中检查对象？').then(function () {
        return batchInsertTaskObj(params);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("导入成功");
      }).catch(() => { });
    },
    fixedAdmdvs(val) {
      if (typeof val == "string" && val.constructor == String) {
        return val;
      }
      if (val && val.length) {
        return val[val.length - 1];
      }
      return null;
    },
  }
};
</script>
