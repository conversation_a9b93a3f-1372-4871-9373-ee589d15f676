import request from '@/utils/request'

// 查询抽查最终结果列表
export function listExamRslt(query) {
  return request({
    url: '/exam/examRslt/list',
    method: 'get',
    params: query
  })
}

// 查询抽查最终结果详细
export function getExamRslt(examTaskRsltId) {
  return request({
    url: '/exam/examRslt/' + examTaskRsltId,
    method: 'get'
  })
}

// 新增抽查最终结果
export function addExamRslt(data) {
  return request({
    url: '/exam/examRslt',
    method: 'post',
    data: data
  })
}

// 修改抽查最终结果
export function updateExamRslt(data) {
  return request({
    url: '/exam/examRslt',
    method: 'put',
    data: data
  })
}

// 删除抽查最终结果
export function delExamRslt(examTaskRsltId) {
  return request({
    url: '/exam/examRslt/' + examTaskRsltId,
    method: 'delete'
  })
}
