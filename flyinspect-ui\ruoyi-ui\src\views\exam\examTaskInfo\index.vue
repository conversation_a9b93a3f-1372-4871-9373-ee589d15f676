<template>
  <div class="app-container">
    <ry-breadcrumb :list="breadList" />
    <card>
      <search-form v-show="showSearch" :modelValue="queryParams" :fields="searchFields" :show-search="showSearch"
        @search="handleQuery" @reset="resetQuery" @update:model-value="val => queryParams = val" />
    </card>
    <card class="mt10">
      <el-row :gutter="10" class="mb8" style="display: flex;justify-content: flex-end;">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['exam:examTaskInfo:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['exam:examTaskInfo:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['exam:examTaskInfo:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['exam:examTaskInfo:export']">导出</el-button>
        </el-col>
      </el-row>

      <common-table :loading="loading" :data="examTaskInfoList" :total="total" :columns="columns"
        :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @selection-change="handleSelectionChange"
        @pagination="getList" :dict="dict">
        <template #operation="{ row }">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(row)"
            v-hasPermi="['exam:examTaskInfo:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(row)"
            v-hasPermi="['exam:examTaskInfo:remove']">删除</el-button>
          <el-button size="mini" type="text" v-hasPermi="['exam:eaxmPlan:remove']"
            @click="switchToExamObj(row)">检查对象</el-button>
        </template>
      </common-table>
    </card>

    <!-- 添加或修改检查任务信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="auto" inline>
        <el-form-item label="检查任务编号" prop="examTaskCode">
          <el-input v-model="form.examTaskCode" placeholder="请输入检查任务编号" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="检查任务名称" prop="examTaskName">
          <el-input v-model="form.examTaskName" placeholder="请输入检查任务名称" :style="`width: ${inputWidth}px`" />
        </el-form-item>
        <el-form-item label="抽取检查对象比例" prop="selcExamObjProp">
          <el-input-number v-model="form.selcExamObjProp" placeholder="请输入抽取检查对象比例" :style="`width: ${inputWidth}px`"
            :min="1" :max="100" />
        </el-form-item>
        <el-form-item label="包含抽取对象标志" prop="contSelcObjFlag">
          <el-select v-model="form.contSelcObjFlag" placeholder="请选择包含抽取对象标志" :style="`width: ${inputWidth}px`">
            <el-option v-for="dict in dict.type.whether_flag" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="优秀抽取比例" prop="examCreditPropA">
          <el-input-number v-model="form.examCreditPropA" placeholder="请输入优秀抽取比例" :style="`width: ${inputWidth}px`"
            :min="0" :max="100" />
        </el-form-item>
        <el-form-item label="良好抽取比例" prop="examCreditPropB">
          <el-input-number v-model="form.examCreditPropB" placeholder="请输入优秀抽取比例" :style="`width: ${inputWidth}px`"
            :min="0" :max="100" />
        </el-form-item>
        <el-form-item label="中等抽取比例" prop="examCreditPropC">
          <el-input-number v-model="form.examCreditPropC" placeholder="请输入优秀抽取比例" :style="`width: ${inputWidth}px`"
            :min="0" :max="100" />
        </el-form-item>
        <el-form-item label="较差抽取比例" prop="examCreditPropD">
          <el-input-number v-model="form.examCreditPropD" placeholder="请输入优秀抽取比例" :style="`width: ${inputWidth}px`"
            :min="0" :max="100" />
        </el-form-item>
        <el-form-item label="慢病抽取数量" prop="examChrSum">
          <el-input-number v-model="form.examChrSum" placeholder="请输入慢病抽取数量" :style="`width: ${inputWidth}px`" :min="0"
            :max="100" />
        </el-form-item>
        <el-form-item label="特病抽取数量" prop="examSpeSum">
          <el-input-number v-model="form.examSpeSum" placeholder="请输入特病抽取数量" :style="`width: ${inputWidth}px`" :min="0"
            :max="100" />
        </el-form-item>
        <el-form-item label="计划检查人数" prop="planExamPsncnt">
          <el-input-number v-model="form.planExamPsncnt" placeholder="请输入计划检查人数" :style="`width: ${inputWidth}px`"
            :min="1" :max="100" />
        </el-form-item>
        <el-form-item label="抽取检查人数" prop="selcExamPsncnt">
          <el-input-number v-model="form.selcExamPsncnt" placeholder="请输入已抽取检查人数" :style="`width: ${inputWidth}px`"
            :min="1" :max="100" />
        </el-form-item>
        <el-form-item label="检查分组人次" prop="groupExamCnt">
          <el-input-number v-model="form.groupExamCnt" placeholder="请输入检查分组人次" :style="`width: ${inputWidth}px`"
            :min="1" :max="100" />
        </el-form-item>
        <el-form-item label="检查事项清单" prop="examMattListId">
          <el-select v-model="form.examMattListId" placeholder="请选择检查事项清单" :style="`width: ${inputWidth}px`">
            <el-option v-for="(dict, index) in examMattList" :key="index" :label="dict.examMatt"
              :value="dict.examMattListId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检查对象类型" prop="examObjType">
          <el-select v-model="form.examObjType" placeholder="请选择检查对象类型" :style="`width: ${inputWidth}px`">
            <el-option v-for="dict in dict.type.exam_obj_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import { listExamTaskInfo, getExamTaskInfo, delExamTaskInfo, addExamTaskInfo, updateExamTaskInfo, eaxmMattGetList } from "@/api/exam/examTaskInfo";
import CommonTable from "@/components/CommonTable";
import SearchForm from "@/components/SearchForm";
import card from '@/components/card'
import columns from "./columns";
import searchFields from "./searchFields";

export default {
  name: "ExamTaskInfo",
  dicts: ["exam_obj_type", "whether_flag"],
  components: {
    CommonTable,
    SearchForm,
    card
  },
  data() {
    return {
      examMattList: [],
      breadList: [
        {
          label: "检查计划管理",
          path: "/exam/eaxmPlan"
        },
        {
          label: "计划任务信息"
        }
      ],
      examPlanId: '',
      // 当前激活的组件
      activeComponent: 'examTaskInfo',
      // 表单输入框统一宽度
      inputWidth: 240,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检查任务信息表格数据
      examTaskInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表格列配置
      columns: columns,
      // 筛选字段配置
      searchFields: searchFields,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        examPlanId: null,
        examTaskCode: null,
        examTaskName: null,
        examMattListId: null,
        selcExamObjProp: null,
        contSelcObjFlag: null,
        planExamPsncnt: null,
        selcExamPsncnt: null,
        groupExamCnt: null,
        valiFlag: null,
        examObjType: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null,
        examTaskStas: null,
        examChrSum: null,
        examSpeSum: null,
        examCreditPropA: null,
        examCreditPropB: null,
        examCreditPropC: null,
        examCreditPropD: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        examPlanId: [
          { required: true, message: "检查计划id不能为空", trigger: "blur" }
        ],
        examTaskCode: [
          { required: true, message: "检查任务编号不能为空", trigger: "blur" }
        ],
        examTaskName: [
          { required: true, message: "检查任务名称不能为空", trigger: "blur" }
        ],
        examMattListId: [
          { required: true, message: "检查事项清单id不能为空", trigger: "blur" }
        ],
        selcExamObjProp: [
          { required: true, message: "抽取检查对象比例不能为空", trigger: "blur" }
        ],
        contSelcObjFlag: [
          { required: true, message: "包含抽取对象标志不能为空", trigger: "blur" }
        ],
        planExamPsncnt: [
          { required: true, message: "计划检查人数不能为空", trigger: "blur" }
        ],
        selcExamPsncnt: [
          { required: true, message: "已抽取检查人数不能为空", trigger: "blur" }
        ],
        groupExamCnt: [
          { required: true, message: "检查分组数不能为空", trigger: "blur" }
        ],
        examObjType: [
          { required: true, message: "检查对象类型不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    // this.searchFields[2].options = this.dict.type.exam_obj_type
    this.getList();
    this.getEaxmMattGetList()
  },
  mounted() {
    this.examPlanId = this.$route.query.examPlanId;
  },
  methods: {
    /** 查询检查任务信息列表 */
    getList() {
      this.loading = true;
      this.queryParams.examPlanId = this.$route.query.examPlanId;
      listExamTaskInfo(this.queryParams).then(response => {
        this.examTaskInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取数据出错:', error);
        this.loading = false;
      });
    },
    getEaxmMattGetList() {
      eaxmMattGetList().then(response => {
        console.log(response, '-response');
        this.examMattList = response.data;
      }).catch(error => {
        console.error('获取数据出错:', error);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        examTaskId: null,
        examPlanId: null,
        examTaskCode: null,
        examTaskName: null,
        examMattListId: null,
        selcExamObjProp: null,
        contSelcObjFlag: null,
        planExamPsncnt: null,
        selcExamPsncnt: null,
        groupExamCnt: null,
        valiFlag: null,
        examObjType: null,
        rid: null,
        crterId: null,
        crterName: null,
        crteTime: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        updtTime: null,
        examTaskStas: null,
        examChrSum: null,
        examSpeSum: null,
        examCreditPropA: null,
        examCreditPropB: null,
        examCreditPropC: null,
        examCreditPropD: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置查询参数（保留分页参数）
      const pageNum = this.queryParams.pageNum;
      const pageSize = this.queryParams.pageSize;

      // 初始化查询参数
      this.queryParams = {
        pageNum: pageNum,
        pageSize: pageSize
      };

      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.examTaskId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检查任务信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const examTaskId = row.examTaskId || this.ids
      getExamTaskInfo(examTaskId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改检查任务信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.examPlanId = this.examPlanId;
          if (this.form.examTaskId != null) {
            updateExamTaskInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addExamTaskInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const examTaskIds = row.examTaskId || this.ids;
      this.$modal.confirm('是否确认删除检查任务信息编号为"' + examTaskIds + '"的数据项？').then(function () {
        return delExamTaskInfo(examTaskIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('exam/examTaskInfo/export', {
        ...this.queryParams
      }, `examTaskInfo_${new Date().getTime()}.xlsx`)
    },
    switchToExamObj(row) {
      this.$router.push({
        path: "/exam/examObjetB",
        query: {
          examTaskId: row.examTaskId,
          examObjType: row.examObjType
        }
      });
    }
  }
};
</script>

<style scoped>
.mt10 {
  margin-top: 10px;
}
</style>
