import request from '@/utils/request'

// 查询医疗机构信息列表
export function listMedins(query) {
  return request({
    url: '/basic/medins/list',
    method: 'get',
    params: query
  })
}

// 查询医疗机构信息详细
export function getMedins(fixmedinsCode) {
  return request({
    url: '/basic/medins/' + fixmedinsCode,
    method: 'get'
  })
}

// 新增医疗机构信息
export function addMedins(data) {
  return request({
    url: '/basic/medins',
    method: 'post',
    data: data
  })
}

// 修改医疗机构信息
export function updateMedins(data) {
  return request({
    url: '/basic/medins',
    method: 'put',
    data: data
  })
}

// 删除医疗机构信息
export function delMedins(fixmedinsCode) {
  return request({
    url: '/basic/medins/' + fixmedinsCode,
    method: 'delete'
  })
}
