<template>
    <div>
        <el-card class="box-card" :body-style="bodyStyle">
            <slot></slot>
        </el-card>
    </div>
</template>

<script>
export default {
    name: 'card',
    props: {
        title: {
            type: String,
            default: ''
        },
        shadow: {
            type: String,
            default: 'never'
        },
        bodyStyle: {
            type: Object,
            default: () => ({ padding: '15px 15px 0px 15px' })
        }
    }
}
</script>
