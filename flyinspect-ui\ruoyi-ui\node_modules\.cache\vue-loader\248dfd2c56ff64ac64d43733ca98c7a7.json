{"remainingRequest": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\basic\\volaData\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\basic\\volaData\\index.vue", "mtime": 1750383268000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0Vm9sYURhdGEsIGdldFZvbGFEYXRhLCBkZWxWb2xhRGF0YSwgYWRkVm9sYURhdGEsIHVwZGF0ZVZvbGFEYXRhIH0gZnJvbSAiQC9hcGkvYmFzaWMvdm9sYURhdGEiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJWb2xhRGF0YSIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDov53op4Tkv6Hmga/ooajmoLzmlbDmja4NCiAgICAgIHZvbGFEYXRhTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgbWR0cnRJZDogbnVsbCwNCiAgICAgICAgc2V0bElkOiBudWxsLA0KICAgICAgICBmaXhtZWRpbnNDb2RlOiBudWxsLA0KICAgICAgICBmaXhtZWRpbnNOYW1lOiBudWxsLA0KICAgICAgICBwc25ObzogbnVsbCwNCiAgICAgICAgQ0VSVE5POiBudWxsLA0KICAgICAgICBwc25OYW1lOiBudWxsLA0KICAgICAgICBHRU5EOiBudWxsLA0KICAgICAgICBBR0U6IG51bGwsDQogICAgICAgIGZ1bmRQYXlTdW1hbXQ6IG51bGwsDQogICAgICAgIGFjY3RQYXk6IG51bGwsDQogICAgICAgIGNhc2hQYXlhbXQ6IG51bGwsDQogICAgICAgIG1lZFR5cGU6IG51bGwsDQogICAgICAgIHNldGxUaW1lOiBudWxsLA0KICAgICAgICBtZWRpbnNMaXN0Q29kZzogbnVsbCwNCiAgICAgICAgbWVkaW5zTGlzdE5hbWU6IG51bGwsDQogICAgICAgIGhpbGlzdENvZGU6IG51bGwsDQogICAgICAgIGhpbGlzdE5hbWU6IG51bGwsDQogICAgICAgIHByb2RlcHROYW1lOiBudWxsLA0KICAgICAgICBTUEVDOiBudWxsLA0KICAgICAgICBkb3Nmb3JtTmFtZTogbnVsbCwNCiAgICAgICAgVU5JVDogbnVsbCwNCiAgICAgICAgQ05UOiBudWxsLA0KICAgICAgICBQUklDOiBudWxsLA0KICAgICAgICBkZXRJdGVtRmVlU3VtYW10OiBudWxsLA0KICAgICAgICBmdWxhbXRPd25wYXlBbXQ6IG51bGwsDQogICAgICAgIHByZXNlbGZwYXlBbXQ6IG51bGwsDQogICAgICAgIGluc2NwQW10OiBudWxsLA0KICAgICAgICBzZWxmcGF5UHJvcDogbnVsbCwNCiAgICAgICAgb3ZlcmxtdFNlbGZwYXk6IG51bGwsDQogICAgICAgIGNocmdpdG1MdjogbnVsbCwNCiAgICAgICAgZGVwdE5hbWU6IG51bGwsDQogICAgICAgIGRpc2VObzogbnVsbCwNCiAgICAgICAgZGlzZU5hbWU6IG51bGwsDQogICAgICAgIHJ1bGVOYW1lOiBudWxsLA0KICAgICAgICB2YWxhRmxhZzogbnVsbCwNCiAgICAgICAgdm9sYVR5cGU6IG51bGwsDQogICAgICAgIHZvbGFEZXNjOiBudWxsLA0KICAgICAgICB2b2xhQWNjb3I6IG51bGwsDQogICAgICAgIERFU0NSSUJFVDogbnVsbCwNCiAgICAgICAgdm9sYUFtdDogbnVsbCwNCiAgICAgICAgY3J0ZXJOYW1lOiBudWxsLA0KICAgICAgICBjcnRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkdE5hbWU6IG51bGwsDQogICAgICAgIHVwZHRUaW1lOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgbWR0cnRJZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlsLHor4pJROS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIHNldGxJZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnu5PnrpdJROS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIGZpeG1lZGluc0NvZGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5a6a54K55Yy76I2v5py65p6E57yW5Y+35LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgZml4bWVkaW5zTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlrprngrnljLvoja/mnLrmnoTlkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBwc25ObzogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkurrlkZjnvJblj7fkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBDRVJUTk86IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+B5Lu25Y+356CB5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgcHNuTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkurrlkZjlp5PlkI3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBmdW5kUGF5U3VtYW10OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWfuumHkeaUr+S7mOaAu+mineS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIGFjY3RQYXk6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Liq5Lq66LSm5oi35pSv5Ye65LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgY2FzaFBheWFtdDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnjrDph5HmlK/ku5jph5Hpop3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBtZWRUeXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWMu+eWl+exu+WIq+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0NCiAgICAgICAgXSwNCiAgICAgICAgc2V0bFRpbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi57uT566X5pe26Ze05LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgaGlsaXN0Q29kZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLljLvkv53nm67lvZXnvJbnoIHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBoaWxpc3ROYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWMu+S/neebruW9leWQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIENOVDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmlbDph4/kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBQUklDOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWNleS7t+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIGRldEl0ZW1GZWVTdW1hbXQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5piO57uG6aG555uu6LS555So5oC76aKd5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgdmFsYUZsYWc6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5pyJ5pWI5qCH5b+X5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgdm9sYVR5cGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6L+d6KeE57G75Z6L5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdLA0KICAgICAgICBjcnRlVGltZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmlbDmja7liJvlu7rml7bpl7TkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICB1cGR0VGltZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmlbDmja7mm7TmlrDml7bpl7TkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdDQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6Lov53op4Tkv6Hmga/liJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RWb2xhRGF0YSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy52b2xhRGF0YUxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBzZXRsRmVlSW5mb0lkOiBudWxsLA0KICAgICAgICBtZHRydElkOiBudWxsLA0KICAgICAgICBzZXRsSWQ6IG51bGwsDQogICAgICAgIGZpeG1lZGluc0NvZGU6IG51bGwsDQogICAgICAgIGZpeG1lZGluc05hbWU6IG51bGwsDQogICAgICAgIHBzbk5vOiBudWxsLA0KICAgICAgICBDRVJUTk86IG51bGwsDQogICAgICAgIHBzbk5hbWU6IG51bGwsDQogICAgICAgIEdFTkQ6IG51bGwsDQogICAgICAgIEFHRTogbnVsbCwNCiAgICAgICAgZnVuZFBheVN1bWFtdDogbnVsbCwNCiAgICAgICAgYWNjdFBheTogbnVsbCwNCiAgICAgICAgY2FzaFBheWFtdDogbnVsbCwNCiAgICAgICAgbWVkVHlwZTogbnVsbCwNCiAgICAgICAgc2V0bFRpbWU6IG51bGwsDQogICAgICAgIG1lZGluc0xpc3RDb2RnOiBudWxsLA0KICAgICAgICBtZWRpbnNMaXN0TmFtZTogbnVsbCwNCiAgICAgICAgaGlsaXN0Q29kZTogbnVsbCwNCiAgICAgICAgaGlsaXN0TmFtZTogbnVsbCwNCiAgICAgICAgcHJvZGVwdE5hbWU6IG51bGwsDQogICAgICAgIFNQRUM6IG51bGwsDQogICAgICAgIGRvc2Zvcm1OYW1lOiBudWxsLA0KICAgICAgICBVTklUOiBudWxsLA0KICAgICAgICBDTlQ6IG51bGwsDQogICAgICAgIFBSSUM6IG51bGwsDQogICAgICAgIGRldEl0ZW1GZWVTdW1hbXQ6IG51bGwsDQogICAgICAgIGZ1bGFtdE93bnBheUFtdDogbnVsbCwNCiAgICAgICAgcHJlc2VsZnBheUFtdDogbnVsbCwNCiAgICAgICAgaW5zY3BBbXQ6IG51bGwsDQogICAgICAgIHNlbGZwYXlQcm9wOiBudWxsLA0KICAgICAgICBvdmVybG10U2VsZnBheTogbnVsbCwNCiAgICAgICAgY2hyZ2l0bUx2OiBudWxsLA0KICAgICAgICBkZXB0TmFtZTogbnVsbCwNCiAgICAgICAgZGlzZU5vOiBudWxsLA0KICAgICAgICBkaXNlTmFtZTogbnVsbCwNCiAgICAgICAgcnVsZU5hbWU6IG51bGwsDQogICAgICAgIHZhbGFGbGFnOiBudWxsLA0KICAgICAgICB2b2xhVHlwZTogbnVsbCwNCiAgICAgICAgdm9sYURlc2M6IG51bGwsDQogICAgICAgIHZvbGFBY2NvcjogbnVsbCwNCiAgICAgICAgREVTQ1JJQkVUOiBudWxsLA0KICAgICAgICB2b2xhQW10OiBudWxsLA0KICAgICAgICBjcnRlck5hbWU6IG51bGwsDQogICAgICAgIGNydGVUaW1lOiBudWxsLA0KICAgICAgICB1cGR0TmFtZTogbnVsbCwNCiAgICAgICAgdXBkdFRpbWU6IG51bGwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLnNldGxGZWVJbmZvSWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDov53op4Tkv6Hmga8iOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IHNldGxGZWVJbmZvSWQgPSByb3cuc2V0bEZlZUluZm9JZCB8fCB0aGlzLmlkcw0KICAgICAgZ2V0Vm9sYURhdGEoc2V0bEZlZUluZm9JZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS56L+d6KeE5L+h5oGvIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLnNldGxGZWVJbmZvSWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlVm9sYURhdGEodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRWb2xhRGF0YSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBzZXRsRmVlSW5mb0lkcyA9IHJvdy5zZXRsRmVlSW5mb0lkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6L+d6KeE5L+h5oGv57yW5Y+35Li6IicgKyBzZXRsRmVlSW5mb0lkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbFZvbGFEYXRhKHNldGxGZWVJbmZvSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnYmFzaWMvdm9sYURhdGEvZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgdm9sYURhdGFfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAolBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/basic/volaData", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"就诊ID\" prop=\"mdtrtId\">\r\n        <el-input\r\n          v-model=\"queryParams.mdtrtId\"\r\n          placeholder=\"请输入就诊ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"结算ID\" prop=\"setlId\">\r\n        <el-input\r\n          v-model=\"queryParams.setlId\"\r\n          placeholder=\"请输入结算ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"定点医药机构编号\" prop=\"fixmedinsCode\">\r\n        <el-input\r\n          v-model=\"queryParams.fixmedinsCode\"\r\n          placeholder=\"请输入定点医药机构编号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"定点医药机构名称\" prop=\"fixmedinsName\">\r\n        <el-input\r\n          v-model=\"queryParams.fixmedinsName\"\r\n          placeholder=\"请输入定点医药机构名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"人员编号\" prop=\"psnNo\">\r\n        <el-input\r\n          v-model=\"queryParams.psnNo\"\r\n          placeholder=\"请输入人员编号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"人员姓名\" prop=\"psnName\">\r\n        <el-input\r\n          v-model=\"queryParams.psnName\"\r\n          placeholder=\"请输入人员姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"性别\" prop=\"GEND\">\r\n        <el-input\r\n          v-model=\"queryParams.GEND\"\r\n          placeholder=\"请输入性别\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"年龄\" prop=\"AGE\">\r\n        <el-input\r\n          v-model=\"queryParams.AGE\"\r\n          placeholder=\"请输入年龄\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"基金支付总额\" prop=\"fundPaySumamt\">\r\n        <el-input\r\n          v-model=\"queryParams.fundPaySumamt\"\r\n          placeholder=\"请输入基金支付总额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"个人账户支出\" prop=\"acctPay\">\r\n        <el-input\r\n          v-model=\"queryParams.acctPay\"\r\n          placeholder=\"请输入个人账户支出\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"现金支付金额\" prop=\"cashPayamt\">\r\n        <el-input\r\n          v-model=\"queryParams.cashPayamt\"\r\n          placeholder=\"请输入现金支付金额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"结算时间\" prop=\"setlTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.setlTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择结算时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"医药机构目录编码\" prop=\"medinsListCodg\">\r\n        <el-input\r\n          v-model=\"queryParams.medinsListCodg\"\r\n          placeholder=\"请输入医药机构目录编码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"医药机构目录名称\" prop=\"medinsListName\">\r\n        <el-input\r\n          v-model=\"queryParams.medinsListName\"\r\n          placeholder=\"请输入医药机构目录名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"医保目录编码\" prop=\"hilistCode\">\r\n        <el-input\r\n          v-model=\"queryParams.hilistCode\"\r\n          placeholder=\"请输入医保目录编码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"医保目录名称\" prop=\"hilistName\">\r\n        <el-input\r\n          v-model=\"queryParams.hilistName\"\r\n          placeholder=\"请输入医保目录名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"药品厂家\" prop=\"prodeptName\">\r\n        <el-input\r\n          v-model=\"queryParams.prodeptName\"\r\n          placeholder=\"请输入药品厂家\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"规格\" prop=\"SPEC\">\r\n        <el-input\r\n          v-model=\"queryParams.SPEC\"\r\n          placeholder=\"请输入规格\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"剂型名称\" prop=\"dosformName\">\r\n        <el-input\r\n          v-model=\"queryParams.dosformName\"\r\n          placeholder=\"请输入剂型名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"单位\" prop=\"UNIT\">\r\n        <el-input\r\n          v-model=\"queryParams.UNIT\"\r\n          placeholder=\"请输入单位\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"数量\" prop=\"CNT\">\r\n        <el-input\r\n          v-model=\"queryParams.CNT\"\r\n          placeholder=\"请输入数量\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"单价\" prop=\"PRIC\">\r\n        <el-input\r\n          v-model=\"queryParams.PRIC\"\r\n          placeholder=\"请输入单价\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"明细项目费用总额\" prop=\"detItemFeeSumamt\">\r\n        <el-input\r\n          v-model=\"queryParams.detItemFeeSumamt\"\r\n          placeholder=\"请输入明细项目费用总额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"全自费金额\" prop=\"fulamtOwnpayAmt\">\r\n        <el-input\r\n          v-model=\"queryParams.fulamtOwnpayAmt\"\r\n          placeholder=\"请输入全自费金额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"先行自付金额\" prop=\"preselfpayAmt\">\r\n        <el-input\r\n          v-model=\"queryParams.preselfpayAmt\"\r\n          placeholder=\"请输入先行自付金额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"符合范围金额\" prop=\"inscpAmt\">\r\n        <el-input\r\n          v-model=\"queryParams.inscpAmt\"\r\n          placeholder=\"请输入符合范围金额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"自付比例\" prop=\"selfpayProp\">\r\n        <el-input\r\n          v-model=\"queryParams.selfpayProp\"\r\n          placeholder=\"请输入自付比例\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"超限价自费费用\" prop=\"overlmtSelfpay\">\r\n        <el-input\r\n          v-model=\"queryParams.overlmtSelfpay\"\r\n          placeholder=\"请输入超限价自费费用\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"收费项目等级\" prop=\"chrgitmLv\">\r\n        <el-input\r\n          v-model=\"queryParams.chrgitmLv\"\r\n          placeholder=\"请输入收费项目等级\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"科室名称\" prop=\"deptName\">\r\n        <el-input\r\n          v-model=\"queryParams.deptName\"\r\n          placeholder=\"请输入科室名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"病种编号\" prop=\"diseNo\">\r\n        <el-input\r\n          v-model=\"queryParams.diseNo\"\r\n          placeholder=\"请输入病种编号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"有效标志\" prop=\"valaFlag\">\r\n        <el-input\r\n          v-model=\"queryParams.valaFlag\"\r\n          placeholder=\"请输入有效标志\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"疑似违规金额\" prop=\"volaAmt\">\r\n        <el-input\r\n          v-model=\"queryParams.volaAmt\"\r\n          placeholder=\"请输入疑似违规金额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"创建人姓名\" prop=\"crterName\">\r\n        <el-input\r\n          v-model=\"queryParams.crterName\"\r\n          placeholder=\"请输入创建人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"数据创建时间\" prop=\"crteTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.crteTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择数据创建时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"更新人姓名\" prop=\"updtName\">\r\n        <el-input\r\n          v-model=\"queryParams.updtName\"\r\n          placeholder=\"请输入更新人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"数据更新时间\" prop=\"updtTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.updtTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择数据更新时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['basic:volaData:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['basic:volaData:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['basic:volaData:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['basic:volaData:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"volaDataList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"结算明细ID\" align=\"center\" prop=\"setlFeeInfoId\" />\r\n      <el-table-column label=\"就诊ID\" align=\"center\" prop=\"mdtrtId\" />\r\n      <el-table-column label=\"结算ID\" align=\"center\" prop=\"setlId\" />\r\n      <el-table-column label=\"定点医药机构编号\" align=\"center\" prop=\"fixmedinsCode\" />\r\n      <el-table-column label=\"定点医药机构名称\" align=\"center\" prop=\"fixmedinsName\" />\r\n      <el-table-column label=\"人员编号\" align=\"center\" prop=\"psnNo\" />\r\n      <el-table-column label=\"证件号码\" align=\"center\" prop=\"CERTNO\" />\r\n      <el-table-column label=\"人员姓名\" align=\"center\" prop=\"psnName\" />\r\n      <el-table-column label=\"性别\" align=\"center\" prop=\"GEND\" />\r\n      <el-table-column label=\"年龄\" align=\"center\" prop=\"AGE\" />\r\n      <el-table-column label=\"基金支付总额\" align=\"center\" prop=\"fundPaySumamt\" />\r\n      <el-table-column label=\"个人账户支出\" align=\"center\" prop=\"acctPay\" />\r\n      <el-table-column label=\"现金支付金额\" align=\"center\" prop=\"cashPayamt\" />\r\n      <el-table-column label=\"医疗类别\" align=\"center\" prop=\"medType\" />\r\n      <el-table-column label=\"结算时间\" align=\"center\" prop=\"setlTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.setlTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"医药机构目录编码\" align=\"center\" prop=\"medinsListCodg\" />\r\n      <el-table-column label=\"医药机构目录名称\" align=\"center\" prop=\"medinsListName\" />\r\n      <el-table-column label=\"医保目录编码\" align=\"center\" prop=\"hilistCode\" />\r\n      <el-table-column label=\"医保目录名称\" align=\"center\" prop=\"hilistName\" />\r\n      <el-table-column label=\"药品厂家\" align=\"center\" prop=\"prodeptName\" />\r\n      <el-table-column label=\"规格\" align=\"center\" prop=\"SPEC\" />\r\n      <el-table-column label=\"剂型名称\" align=\"center\" prop=\"dosformName\" />\r\n      <el-table-column label=\"单位\" align=\"center\" prop=\"UNIT\" />\r\n      <el-table-column label=\"数量\" align=\"center\" prop=\"CNT\" />\r\n      <el-table-column label=\"单价\" align=\"center\" prop=\"PRIC\" />\r\n      <el-table-column label=\"明细项目费用总额\" align=\"center\" prop=\"detItemFeeSumamt\" />\r\n      <el-table-column label=\"全自费金额\" align=\"center\" prop=\"fulamtOwnpayAmt\" />\r\n      <el-table-column label=\"先行自付金额\" align=\"center\" prop=\"preselfpayAmt\" />\r\n      <el-table-column label=\"符合范围金额\" align=\"center\" prop=\"inscpAmt\" />\r\n      <el-table-column label=\"自付比例\" align=\"center\" prop=\"selfpayProp\" />\r\n      <el-table-column label=\"超限价自费费用\" align=\"center\" prop=\"overlmtSelfpay\" />\r\n      <el-table-column label=\"收费项目等级\" align=\"center\" prop=\"chrgitmLv\" />\r\n      <el-table-column label=\"科室名称\" align=\"center\" prop=\"deptName\" />\r\n      <el-table-column label=\"病种编号\" align=\"center\" prop=\"diseNo\" />\r\n      <el-table-column label=\"病种名称\" align=\"center\" prop=\"diseName\" />\r\n      <el-table-column label=\"规则名称\" align=\"center\" prop=\"ruleName\" />\r\n      <el-table-column label=\"有效标志\" align=\"center\" prop=\"valaFlag\" />\r\n      <el-table-column label=\"违规类型\" align=\"center\" prop=\"volaType\" />\r\n      <el-table-column label=\"违规描述\" align=\"center\" prop=\"volaDesc\" />\r\n      <el-table-column label=\"违规依据\" align=\"center\" prop=\"volaAccor\" />\r\n      <el-table-column label=\"问题描述\" align=\"center\" prop=\"DESCRIBET\" />\r\n      <el-table-column label=\"疑似违规金额\" align=\"center\" prop=\"volaAmt\" />\r\n      <el-table-column label=\"创建人姓名\" align=\"center\" prop=\"crterName\" />\r\n      <el-table-column label=\"数据创建时间\" align=\"center\" prop=\"crteTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.crteTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"更新人姓名\" align=\"center\" prop=\"updtName\" />\r\n      <el-table-column label=\"数据更新时间\" align=\"center\" prop=\"updtTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.updtTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['basic:volaData:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['basic:volaData:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改违规信息对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"就诊ID\" prop=\"mdtrtId\">\r\n          <el-input v-model=\"form.mdtrtId\" placeholder=\"请输入就诊ID\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"结算ID\" prop=\"setlId\">\r\n          <el-input v-model=\"form.setlId\" placeholder=\"请输入结算ID\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"定点医药机构编号\" prop=\"fixmedinsCode\">\r\n          <el-input v-model=\"form.fixmedinsCode\" placeholder=\"请输入定点医药机构编号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"定点医药机构名称\" prop=\"fixmedinsName\">\r\n          <el-input v-model=\"form.fixmedinsName\" placeholder=\"请输入定点医药机构名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"人员编号\" prop=\"psnNo\">\r\n          <el-input v-model=\"form.psnNo\" placeholder=\"请输入人员编号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"证件号码\" prop=\"CERTNO\">\r\n          <el-input v-model=\"form.CERTNO\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"人员姓名\" prop=\"psnName\">\r\n          <el-input v-model=\"form.psnName\" placeholder=\"请输入人员姓名\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"性别\" prop=\"GEND\">\r\n          <el-input v-model=\"form.GEND\" placeholder=\"请输入性别\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"年龄\" prop=\"AGE\">\r\n          <el-input v-model=\"form.AGE\" placeholder=\"请输入年龄\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"基金支付总额\" prop=\"fundPaySumamt\">\r\n          <el-input v-model=\"form.fundPaySumamt\" placeholder=\"请输入基金支付总额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"个人账户支出\" prop=\"acctPay\">\r\n          <el-input v-model=\"form.acctPay\" placeholder=\"请输入个人账户支出\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"现金支付金额\" prop=\"cashPayamt\">\r\n          <el-input v-model=\"form.cashPayamt\" placeholder=\"请输入现金支付金额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"结算时间\" prop=\"setlTime\">\r\n          <el-date-picker clearable\r\n            v-model=\"form.setlTime\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择结算时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"医药机构目录编码\" prop=\"medinsListCodg\">\r\n          <el-input v-model=\"form.medinsListCodg\" placeholder=\"请输入医药机构目录编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"医药机构目录名称\" prop=\"medinsListName\">\r\n          <el-input v-model=\"form.medinsListName\" placeholder=\"请输入医药机构目录名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"医保目录编码\" prop=\"hilistCode\">\r\n          <el-input v-model=\"form.hilistCode\" placeholder=\"请输入医保目录编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"医保目录名称\" prop=\"hilistName\">\r\n          <el-input v-model=\"form.hilistName\" placeholder=\"请输入医保目录名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"药品厂家\" prop=\"prodeptName\">\r\n          <el-input v-model=\"form.prodeptName\" placeholder=\"请输入药品厂家\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"规格\" prop=\"SPEC\">\r\n          <el-input v-model=\"form.SPEC\" placeholder=\"请输入规格\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"剂型名称\" prop=\"dosformName\">\r\n          <el-input v-model=\"form.dosformName\" placeholder=\"请输入剂型名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"单位\" prop=\"UNIT\">\r\n          <el-input v-model=\"form.UNIT\" placeholder=\"请输入单位\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"数量\" prop=\"CNT\">\r\n          <el-input v-model=\"form.CNT\" placeholder=\"请输入数量\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"单价\" prop=\"PRIC\">\r\n          <el-input v-model=\"form.PRIC\" placeholder=\"请输入单价\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"明细项目费用总额\" prop=\"detItemFeeSumamt\">\r\n          <el-input v-model=\"form.detItemFeeSumamt\" placeholder=\"请输入明细项目费用总额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"全自费金额\" prop=\"fulamtOwnpayAmt\">\r\n          <el-input v-model=\"form.fulamtOwnpayAmt\" placeholder=\"请输入全自费金额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"先行自付金额\" prop=\"preselfpayAmt\">\r\n          <el-input v-model=\"form.preselfpayAmt\" placeholder=\"请输入先行自付金额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"符合范围金额\" prop=\"inscpAmt\">\r\n          <el-input v-model=\"form.inscpAmt\" placeholder=\"请输入符合范围金额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"自付比例\" prop=\"selfpayProp\">\r\n          <el-input v-model=\"form.selfpayProp\" placeholder=\"请输入自付比例\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"超限价自费费用\" prop=\"overlmtSelfpay\">\r\n          <el-input v-model=\"form.overlmtSelfpay\" placeholder=\"请输入超限价自费费用\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"收费项目等级\" prop=\"chrgitmLv\">\r\n          <el-input v-model=\"form.chrgitmLv\" placeholder=\"请输入收费项目等级\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"科室名称\" prop=\"deptName\">\r\n          <el-input v-model=\"form.deptName\" placeholder=\"请输入科室名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"病种编号\" prop=\"diseNo\">\r\n          <el-input v-model=\"form.diseNo\" placeholder=\"请输入病种编号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"病种名称\" prop=\"diseName\">\r\n          <el-input v-model=\"form.diseName\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"规则名称\" prop=\"ruleName\">\r\n          <el-input v-model=\"form.ruleName\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"有效标志\" prop=\"valaFlag\">\r\n          <el-input v-model=\"form.valaFlag\" placeholder=\"请输入有效标志\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"违规描述\" prop=\"volaDesc\">\r\n          <el-input v-model=\"form.volaDesc\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"违规依据\" prop=\"volaAccor\">\r\n          <el-input v-model=\"form.volaAccor\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"问题描述\" prop=\"DESCRIBET\">\r\n          <el-input v-model=\"form.DESCRIBET\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"疑似违规金额\" prop=\"volaAmt\">\r\n          <el-input v-model=\"form.volaAmt\" placeholder=\"请输入疑似违规金额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"创建人姓名\" prop=\"crterName\">\r\n          <el-input v-model=\"form.crterName\" placeholder=\"请输入创建人姓名\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"数据创建时间\" prop=\"crteTime\">\r\n          <el-date-picker clearable\r\n            v-model=\"form.crteTime\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择数据创建时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"更新人姓名\" prop=\"updtName\">\r\n          <el-input v-model=\"form.updtName\" placeholder=\"请输入更新人姓名\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"数据更新时间\" prop=\"updtTime\">\r\n          <el-date-picker clearable\r\n            v-model=\"form.updtTime\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择数据更新时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listVolaData, getVolaData, delVolaData, addVolaData, updateVolaData } from \"@/api/basic/volaData\";\r\n\r\nexport default {\r\n  name: \"VolaData\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 违规信息表格数据\r\n      volaDataList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        mdtrtId: null,\r\n        setlId: null,\r\n        fixmedinsCode: null,\r\n        fixmedinsName: null,\r\n        psnNo: null,\r\n        CERTNO: null,\r\n        psnName: null,\r\n        GEND: null,\r\n        AGE: null,\r\n        fundPaySumamt: null,\r\n        acctPay: null,\r\n        cashPayamt: null,\r\n        medType: null,\r\n        setlTime: null,\r\n        medinsListCodg: null,\r\n        medinsListName: null,\r\n        hilistCode: null,\r\n        hilistName: null,\r\n        prodeptName: null,\r\n        SPEC: null,\r\n        dosformName: null,\r\n        UNIT: null,\r\n        CNT: null,\r\n        PRIC: null,\r\n        detItemFeeSumamt: null,\r\n        fulamtOwnpayAmt: null,\r\n        preselfpayAmt: null,\r\n        inscpAmt: null,\r\n        selfpayProp: null,\r\n        overlmtSelfpay: null,\r\n        chrgitmLv: null,\r\n        deptName: null,\r\n        diseNo: null,\r\n        diseName: null,\r\n        ruleName: null,\r\n        valaFlag: null,\r\n        volaType: null,\r\n        volaDesc: null,\r\n        volaAccor: null,\r\n        DESCRIBET: null,\r\n        volaAmt: null,\r\n        crterName: null,\r\n        crteTime: null,\r\n        updtName: null,\r\n        updtTime: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        mdtrtId: [\r\n          { required: true, message: \"就诊ID不能为空\", trigger: \"blur\" }\r\n        ],\r\n        setlId: [\r\n          { required: true, message: \"结算ID不能为空\", trigger: \"blur\" }\r\n        ],\r\n        fixmedinsCode: [\r\n          { required: true, message: \"定点医药机构编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        fixmedinsName: [\r\n          { required: true, message: \"定点医药机构名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        psnNo: [\r\n          { required: true, message: \"人员编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        CERTNO: [\r\n          { required: true, message: \"证件号码不能为空\", trigger: \"blur\" }\r\n        ],\r\n        psnName: [\r\n          { required: true, message: \"人员姓名不能为空\", trigger: \"blur\" }\r\n        ],\r\n        fundPaySumamt: [\r\n          { required: true, message: \"基金支付总额不能为空\", trigger: \"blur\" }\r\n        ],\r\n        acctPay: [\r\n          { required: true, message: \"个人账户支出不能为空\", trigger: \"blur\" }\r\n        ],\r\n        cashPayamt: [\r\n          { required: true, message: \"现金支付金额不能为空\", trigger: \"blur\" }\r\n        ],\r\n        medType: [\r\n          { required: true, message: \"医疗类别不能为空\", trigger: \"change\" }\r\n        ],\r\n        setlTime: [\r\n          { required: true, message: \"结算时间不能为空\", trigger: \"blur\" }\r\n        ],\r\n        hilistCode: [\r\n          { required: true, message: \"医保目录编码不能为空\", trigger: \"blur\" }\r\n        ],\r\n        hilistName: [\r\n          { required: true, message: \"医保目录名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        CNT: [\r\n          { required: true, message: \"数量不能为空\", trigger: \"blur\" }\r\n        ],\r\n        PRIC: [\r\n          { required: true, message: \"单价不能为空\", trigger: \"blur\" }\r\n        ],\r\n        detItemFeeSumamt: [\r\n          { required: true, message: \"明细项目费用总额不能为空\", trigger: \"blur\" }\r\n        ],\r\n        valaFlag: [\r\n          { required: true, message: \"有效标志不能为空\", trigger: \"blur\" }\r\n        ],\r\n        volaType: [\r\n          { required: true, message: \"违规类型不能为空\", trigger: \"change\" }\r\n        ],\r\n        crteTime: [\r\n          { required: true, message: \"数据创建时间不能为空\", trigger: \"blur\" }\r\n        ],\r\n        updtTime: [\r\n          { required: true, message: \"数据更新时间不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询违规信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listVolaData(this.queryParams).then(response => {\r\n        this.volaDataList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        setlFeeInfoId: null,\r\n        mdtrtId: null,\r\n        setlId: null,\r\n        fixmedinsCode: null,\r\n        fixmedinsName: null,\r\n        psnNo: null,\r\n        CERTNO: null,\r\n        psnName: null,\r\n        GEND: null,\r\n        AGE: null,\r\n        fundPaySumamt: null,\r\n        acctPay: null,\r\n        cashPayamt: null,\r\n        medType: null,\r\n        setlTime: null,\r\n        medinsListCodg: null,\r\n        medinsListName: null,\r\n        hilistCode: null,\r\n        hilistName: null,\r\n        prodeptName: null,\r\n        SPEC: null,\r\n        dosformName: null,\r\n        UNIT: null,\r\n        CNT: null,\r\n        PRIC: null,\r\n        detItemFeeSumamt: null,\r\n        fulamtOwnpayAmt: null,\r\n        preselfpayAmt: null,\r\n        inscpAmt: null,\r\n        selfpayProp: null,\r\n        overlmtSelfpay: null,\r\n        chrgitmLv: null,\r\n        deptName: null,\r\n        diseNo: null,\r\n        diseName: null,\r\n        ruleName: null,\r\n        valaFlag: null,\r\n        volaType: null,\r\n        volaDesc: null,\r\n        volaAccor: null,\r\n        DESCRIBET: null,\r\n        volaAmt: null,\r\n        crterName: null,\r\n        crteTime: null,\r\n        updtName: null,\r\n        updtTime: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.setlFeeInfoId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加违规信息\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const setlFeeInfoId = row.setlFeeInfoId || this.ids\r\n      getVolaData(setlFeeInfoId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改违规信息\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.setlFeeInfoId != null) {\r\n            updateVolaData(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addVolaData(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const setlFeeInfoIds = row.setlFeeInfoId || this.ids;\r\n      this.$modal.confirm('是否确认删除违规信息编号为\"' + setlFeeInfoIds + '\"的数据项？').then(function() {\r\n        return delVolaData(setlFeeInfoIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('basic/volaData/export', {\r\n        ...this.queryParams\r\n      }, `volaData_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}