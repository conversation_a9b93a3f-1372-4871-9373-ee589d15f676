<template>
    <div class="app-container" ref="appContainer" :class="{ 'fullscreen-mode': isFullscreen }">
        <div class="random-header">
            <div class="title">{{ deptName }}检查对象随机抽取结果</div>
            <!-- 添加导出和保存按钮 -->
            <div class="export-save-buttons">
                <el-button type="warning" plain @click="saveData">保存(V)</el-button>
                <el-button type="success" plain @click="exportData">导出(D)</el-button>
            </div>
        </div>

        <!-- 全屏按钮 -->
        <div class="fullscreen-btn" @click="toggleFullScreen">
            <i :class="isFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"></i>
        </div>

        <!-- 添加右侧操作按钮 -->
        <div class="action-button" @click="handleAction">
            <i :class="showFilter ? 'el-icon-arrow-left' : 'el-icon-arrow-right'"></i>
        </div>

        <div class="main-content">
            <div class="content-wrapper">
                <!-- 筛选项区域，添加v-show控制显示/隐藏 -->
                <div class="info-section" v-show="showFilter">
                    <div class="task-info">
                        <div class="info-item">
                            <div class="label">检查任务:</div>
                            <el-select :zIndex="10000" v-model="form.option" multiple placeholder="请选择检查任务" clearable
                                class="task-select">
                                <el-option v-for="item in form.nameOptions" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                            <div style="flex:1;display:flex;justify-content:flex-end;">
                                <el-button plain @click="resetForm()">重置</el-button>
                                <el-button type="primary" plain @click="getTaskList">查询</el-button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="result-table" :class="{ 'expanded': !showFilter }">
                    <div class="table-header">
                        <span class="header-text">抽取结果</span>
                        <div class="header-line"></div>
                    </div>
                    <el-table :loading="loading" :cell-style="{ padding: '0px' }" :row-style="{ height: '85px' }" stripe
                        :data="displayList" style="width: 100%;" border>
                        <el-table-column type="index" label="序号" align="center" width="80">
                            <template slot-scope="scope">
                                <span style="font-size: 25px">{{ (queryParams.pageNum - 1) * queryParams.pageSize +
                                    scope.$index + 1 }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="admdvsName" show-overflow-tooltip label="区划名称" width="220"
                            align="center"></el-table-column>
                        <el-table-column prop="legentName" label="检查对象名称" align="center"></el-table-column>
                        <el-table-column prop="legentLv" label="机构等级" align="center" width="200">
                            <template slot-scope="scope">
                                <dict-tag :options="dict.type.legent_lv" :value="scope.row.legentLv" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="examCreditLv" label="信用等级" align="center" width="200">
                            <template slot-scope="scope">
                                <dict-tag :options="dict.type.exam_credit_lv" :value="scope.row.examCreditLv" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="legentAddr" label="检查对象地址" align="center"></el-table-column>
                        <template slot="empty">
                            <el-empty description="暂无数据"></el-empty>
                        </template>
                    </el-table>

                    <!-- 添加分页组件 -->
                    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                        :limit.sync="queryParams.pageSize" @pagination="getTaskList" :size="'small'" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { queryExamResultList, resultSave } from '@/api/exam/examRandomResult'
import { quickSearchTask } from "@/api/exam/examRandom";
import Pagination from '@/components/Pagination'
import { addUser } from "@/api/system/user";

export default {
    name: "ExamRandom",
    components: {
        Pagination
    },
    dicts: ["legent_lv", "exam_credit_lv"],
    data() {
        return {
            form: {
                nameOptions: [],
                option: "",
                scope: "",
                ratio: "",
                isContain: "",
                planSum: 0,
                examSum: 0,
                examObjSum: 0,
                examChrSum: 0,
                examSpeSum: 0
            },
            loading: false,
            // 添加分页相关数据
            total: 0,
            displayList: [],
            queryParams: {
                pageNum: 1,
                pageSize: 10
            },
            isFullscreen: false, // 是否全屏状态
            showFilter: true // 添加showFilter属性
        };
    },
    created() {
        // 监听退出全屏事件
        document.addEventListener('fullscreenchange', this.fullscreenChangeHandler);
        document.addEventListener('webkitfullscreenchange', this.fullscreenChangeHandler);
        document.addEventListener('mozfullscreenchange', this.fullscreenChangeHandler);
        document.addEventListener('MSFullscreenChange', this.fullscreenChangeHandler);

        this.getTaskList(null, true);
        this.getQuickSearch();
    },
    computed: {
        deptName() {
            return this.$store.state.user.deptName
        }
    },
    beforeDestroy() {
        // 组件销毁前移除事件监听
        document.removeEventListener('fullscreenchange', this.fullscreenChangeHandler);
        document.removeEventListener('webkitfullscreenchange', this.fullscreenChangeHandler);
        document.removeEventListener('mozfullscreenchange', this.fullscreenChangeHandler);
        document.removeEventListener('MSFullscreenChange', this.fullscreenChangeHandler);

        // 确保退出全屏
        if (this.isFullscreen) {
            this.exitFullscreen();
        }
    },
    methods: {
        getQuickSearch() {
            this.loading = true;
            const params = {
                examTaskName: null
            };
            quickSearchTask(params).then((res) => {
                if (res.code === 200) {
                    this.form.nameOptions = res.data;
                }
                this.loading = false;
            })
        },
        async getTaskList(val, isInit = false) {
            if (!isInit && (!this.form.option || (Array.isArray(this.form.option) && this.form.option.length === 0))) {
                this.$message.error('请先选择检查任务');
                this.displayList = [];
                this.total = 0;
                return;
            }

            // 如果是首次加载且没有选择任务，直接返回不执行查询
            if (isInit && (!this.form.option || (Array.isArray(this.form.option) && this.form.option.length === 0))) {
                this.displayList = [];
                this.total = 0;
                return;
            }

            this.loading = true;
            this.taskId = val;

            // 如果是分页触发
            if (typeof val === 'object' && val.page) {
                this.queryParams.pageNum = val.page;
                this.queryParams.pageSize = val.limit;
            }

            let params = {
                examTaskListId: this.form.option,
                pageNum: this.queryParams.pageNum,
                pageSize: this.queryParams.pageSize
            }
            const result = await queryExamResultList(params);
            if (result.code === 200) {
                this.displayList = result.rows;
                this.total = result.total;
                this.loading = false;
            } else {
                this.$message.error(result.msg || '查询失败');
                this.loading = false;
            }
        },
        resetForm() {
            this.form.option = "";
            this.queryParams.pageNum = 1;
            this.queryParams.pageSize = 10;
            this.getTaskList(null, true);
        },
        toggleFullScreen() {
            if (!this.isFullscreen) {
                this.enterFullscreen();
            } else {
                this.exitFullscreen();
            }
        },

        // 进入全屏模式，结合浏览器API和CSS增强效果
        enterFullscreen() {
            const container = this.$refs.appContainer;
            if (!container) return;

            // 先设置CSS增强样式
            this.isFullscreen = true;

            // 使用Document的原生全屏API
            try {
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen();
                } else if (document.documentElement.webkitRequestFullscreen) { // Chrome, Safari
                    document.documentElement.webkitRequestFullscreen();
                } else if (document.documentElement.mozRequestFullScreen) { // Firefox
                    document.documentElement.mozRequestFullScreen();
                } else if (document.documentElement.msRequestFullscreen) { // IE/Edge
                    document.documentElement.msRequestFullscreen();
                }
            } catch (e) {
                console.error('全屏请求失败:', e);
                // 尝试后备方法 - 对组件应用全屏样式
                if (container) {
                    // 阻止页面滚动
                    document.body.style.overflow = 'hidden';
                }
            }
        },

        // 退出全屏模式，处理两种全屏方式
        exitFullscreen() {
            // 先复位CSS状态
            this.isFullscreen = false;

            // 恢复页面滚动
            document.body.style.overflow = '';

            // 退出浏览器原生全屏
            try {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) { // Chrome, Safari
                    document.webkitExitFullscreen();
                } else if (document.mozCancelFullScreen) { // Firefox
                    document.mozCancelFullScreen();
                } else if (document.msExitFullscreen) { // IE/Edge
                    document.msExitFullscreen();
                }
            } catch (e) {
                console.error('退出全屏失败:', e);
                // 这里不需要处理，因为CSS状态已经恢复
            }
        },

        fullscreenChangeHandler() {
            // 检测浏览器全屏状态并同步到CSS全屏状态
            const fullscreenElement = document.fullscreenElement ||
                document.webkitFullscreenElement ||
                document.mozFullScreenElement ||
                document.msFullscreenElement;

            // 如果浏览器退出全屏，我们也需要确保CSS状态同步
            this.isFullscreen = !!fullscreenElement;
        },
        // 保存
        saveData() {
            if (!this.form.option || this.form.option.length === 0) {
                this.$message.error('请先选择检查任务');
                return;
            }
            if (this.displayList.length === 0) {
                this.$message.error('请先查询检查任务');
                return;
            }
            resultSave({ examTaskListId: this.form.option }).then(response => {
                this.$modal.msgSuccess("保存成功");
            });
        },
        // 导出数据方法
        exportData() {
            if (!this.form.option || this.form.option.length === 0) {
                this.$message.error('请先选择检查任务');
                return;
            }
            // 处理单选和多选情况
            let titles = [];
            if (Array.isArray(this.form.option)) {
                // 多选情况
                this.form.option.forEach(optionValue => {
                    const found = this.form.nameOptions.find(item => item.value === optionValue);
                    if (found) {
                        titles.push(found.label);
                    }
                });
            } else {
                // 单选情况
                const found = this.form.nameOptions.find(item => item.value === this.form.option);
                if (found) {
                    titles.push(found.label);
                }
            }
            const title = titles.join(',');
            // 调用下载方法
            this.download('/exam/examTask/exportResult', {
                examTaskListId: this.form.option
            }, `${title}抽查结果.xlsx`);
        },

        // 右侧操作按钮方法
        handleAction() {
            this.showFilter = !this.showFilter;
        },

        // 获取字典标签
        getDictLabel(dict, value) {
            if (!dict || !value) return false;
            for (let i = 0; i < dict.length; i++) {
                if (dict[i].value === value) {
                    return true;
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.app-container {
    background: linear-gradient(135deg, #1a4a8d 0%, #3183c7 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    color: #fff;
    position: relative;
    overflow: hidden;
    transition: all 0.5s ease;

    &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        // background: url('@/assets/images/bg-pattern.png') repeat;
        opacity: 0.05;
        pointer-events: none;
    }

    /* 全屏模式的CSS增强 */
    &.fullscreen-mode {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 2000 !important;
        background: linear-gradient(135deg, #0c325e 0%, #1665b8 100%) !important;
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        overflow: scroll;
    }
}

.fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    z-index: 100;

    &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
    }

    &:active {
        transform: scale(0.98);
    }

    i {
        color: #fff;
        font-size: 20px;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }
}

.random-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px 0;
    // background: rgba(0, 0, 0, 0.2);
    // box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    margin-bottom: 30px;
    position: relative;

    &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 10%;
        right: 10%;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    }

    .logo {
        width: 70px;
        height: 70px;
        margin-right: 20px;
        filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.3));
    }

    .title {
        font-size: 50px;
        color: #fff;
        font-weight: 600;
        padding: 15px 80px;
        letter-spacing: 5px;
        background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 216, 255, 0)), color-stop(rgba(0, 216, 255, .6)), to(rgba(0, 216, 255, 0)));
        background-image: linear-gradient(90deg, rgba(0, 216, 255, 0), rgba(0, 216, 255, .6), rgba(0, 216, 255, 0));
    }

    .export-save-buttons {
        position: absolute;
        right: 80px;
        top: 70px;

        .el-button {
            margin-left: 10px;
        }
    }
}

.main-content {
    flex: 1;
    padding: 0 40px 30px;
}

.content-wrapper {
    background: rgba(26, 74, 141, 0.6);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.info-section {
    padding: 30px;
    background: rgba(19, 71, 129, 0.5);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.task-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;

    .info-item {
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.1);
        padding: 15px 20px;
        border-radius: 8px;
        flex: 1;
        margin: 0 10px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);

        &:first-child {
            margin-left: 0;
        }

        &:last-child {
            margin-right: 0;
        }

        .label {
            margin-right: 15px;
            font-size: 30px;
            color: rgba(255, 255, 255, 0.8);
        }

        .value {
            font-size: 22px;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            color: #fff;
        }

        .task-select {
            width: 350px;
        }
    }
}

.result-table {
    padding: 30px;
    transition: all 0.3s ease;
    scroll-margin-top: 20px;

    &.expanded {
        padding-top: 50px;
    }

    .table-header {
        margin-bottom: 20px;
        display: flex;
        align-items: center;

        .header-text {
            font-size: 22px;
            font-weight: bold;
            margin-right: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #fff;
            text-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);
        }

        .header-line {
            flex: 1;
            height: 1px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.5), transparent);
        }
    }

    .empty-text {
        text-align: center;
        padding: 50px 0;
        color: rgba(255, 255, 255, 0.5);
        font-size: 16px;
        font-style: italic;
    }
}

.footer {
    padding: 20px 0;
    text-align: center;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.5);
    margin-top: 20px;
}

::v-deep .el-select .el-input__inner {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.2);
    color: #fff;
    height: 50px;
    font-size: 16px;
    border-radius: 8px;

    &::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }
}

::v-deep .el-select-dropdown {
    background-color: rgba(19, 71, 129, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);

    .el-select-dropdown__item {
        color: rgba(255, 255, 255, 0.8);

        &.selected,
        &.hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        &.selected {
            font-weight: bold;
        }
    }
}

/*****************表格样式覆盖 ********************/
::v-deep .el-table {
    border: 1px solid #397bd8;
    background: transparent !important;

    tr {
        background: transparent;
    }

    th.el-table__cell {
        background: #215eb4 !important;
        font-size: 20px !important;
        color: #fff !important;
        text-align: center;
        line-height: 60px;
    }

    th.el-table__cell.is-leaf,
    td.el-table__cell {
        border: none;
    }

    .el-table__cell {
        border-bottom: none;
        font-size: 18px;
        color: #fff;
        line-height: 60px;
    }

    .cell {
        line-height: 20px !important;
    }
}

::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background: rgba(33, 94, 180, 0.8);
}

::v-deep .el-table__body tr:hover>td.el-table__cell {
    background: transparent;
}

::v-deep .el-table::before,
.el-table--group::after,
.el-table--border::after {
    background-color: transparent;
}

::v-deep .el-table__body-wrapper {
    overflow-x: hidden;
    overflow-y: auto;
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 4px;
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
    background-color: rgba(144, 147, 153, 0.5);
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

/* 分页样式 */
::v-deep .pagination-container {
    padding: 15px 0 25px;
    background: transparent !important;
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

::v-deep .el-pagination {
    padding: 8px 15px;
    background: rgba(26, 74, 141, 0.7);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

::v-deep .el-pagination:hover {
    background: rgba(26, 74, 141, 0.85);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

::v-deep .el-pagination button,
::v-deep .el-pagination span:not([class*=suffix]),
::v-deep .el-pagination .el-select .el-input .el-input__inner,
::v-deep .el-pagination .el-pagination__jump {
    color: #fff;
    font-weight: normal;
    background: transparent;
    font-size: 12px;
}

::v-deep .el-pagination .el-pagination__total,
::v-deep .el-pagination .el-pagination__sizes {
    margin-right: 15px;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border-radius: 4px;
    padding: 0 8px;
    min-width: 24px;
    height: 24px;
    line-height: 24px;
    transition: all 0.2s ease;
}

::v-deep .el-pagination .btn-prev:hover,
::v-deep .el-pagination .btn-next:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

::v-deep .el-pagination .el-pager li {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border-radius: 4px;
    min-width: 24px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    margin: 0 3px;
    transition: all 0.2s ease;
}

::v-deep .el-pagination .el-pager li.active {
    background: #1890ff;
    color: #fff;
    font-weight: bold;
    transform: scale(1.05);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

::v-deep .el-pagination .el-pager li:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

::v-deep .el-pagination .el-pager li.disabled {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;
}

::v-deep .el-pagination button:disabled {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;
}

::v-deep .el-pagination button:disabled:hover {
    transform: none;
}

::v-deep .el-pagination__editor.el-input {
    width: 40px;
    margin: 0 5px;
}

::v-deep .el-pagination__editor.el-input .el-input__inner {
    height: 24px;
    line-height: 24px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
    text-align: center;
    padding: 0 5px;
}

::v-deep .el-pagination__editor.el-input .el-input__inner:focus,
::v-deep .el-pagination__editor.el-input .el-input__inner:hover {
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

::v-deep .el-pagination .el-select .el-input .el-input__inner {
    height: 24px;
    line-height: 24px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
}

::v-deep .el-pagination .el-select .el-input .el-input__inner:focus,
::v-deep .el-pagination .el-select .el-input .el-input__inner:hover {
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

::v-deep .el-pagination .el-pagination__sizes .el-input .el-input__suffix {
    top: 0;
    height: 24px;
    line-height: 24px;
}

::v-deep .el-pagination .el-pagination__sizes .el-input .el-input__suffix .el-select__caret {
    color: #fff;
    line-height: 24px;
}

/* 分页下拉框样式 */
::v-deep .el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
    background-color: rgba(24, 144, 255, 0.1);
    color: #1890ff;
}

::v-deep .el-select-dropdown.el-popper {
    background: rgba(19, 71, 129, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

::v-deep .el-select-dropdown__item {
    color: #fff;
    font-size: 12px;
    height: 30px;
    line-height: 30px;
}

::v-deep .el-select-dropdown__item.hover,
::v-deep .el-select-dropdown__item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

::v-deep .el-select-dropdown__item.selected {
    color: #1890ff;
    font-weight: bold;
}

::v-deep .el-popper .popper__arrow {
    border-bottom-color: rgba(19, 71, 129, 0.95);
}

::v-deep .el-popper .popper__arrow::after {
    border-bottom-color: rgba(19, 71, 129, 0.95);
}

/* 浏览器原生全屏状态下的额外样式 */
:fullscreen .app-container,
:-webkit-full-screen .app-container,
:-moz-full-screen .app-container,
:-ms-fullscreen .app-container {
    background: linear-gradient(135deg, #0c325e 0%, #1665b8 100%);
}

:fullscreen .fullscreen-btn,
:-webkit-full-screen .fullscreen-btn,
:-moz-full-screen .fullscreen-btn,
:-ms-fullscreen .fullscreen-btn {
    top: 30px;
    right: 30px;
}

.action-button {
    position: absolute;
    top: 180px;
    right: 0;
    width: 24px;
    height: 60px;
    border-radius: 4px 0 0 4px;
    background: rgba(26, 74, 141, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;

    &:hover {
        background: rgba(30, 93, 161, 0.9);
    }

    i {
        color: #fff;
        font-size: 16px;
    }
}
</style>
