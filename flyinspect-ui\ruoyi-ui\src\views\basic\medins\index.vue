<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="定点医药机构编号" prop="fixmedinsCode">
        <el-input
          v-model="queryParams.fixmedinsCode"
          placeholder="请输入定点医药机构编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="定点医药机构名称" prop="fixmedinsName">
        <el-input
          v-model="queryParams.fixmedinsName"
          placeholder="请输入定点医药机构名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医保区划" prop="admdvs">
        <el-input
          v-model="queryParams.admdvs"
          placeholder="请输入医保区划"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医疗机构等级" prop="medinsLv">
        <el-input
          v-model="queryParams.medinsLv"
          placeholder="请输入医疗机构等级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="有效标志" prop="valiFlag">
        <el-input
          v-model="queryParams.valiFlag"
          placeholder="请输入有效标志"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['basic:medins:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['basic:medins:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['basic:medins:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['basic:medins:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="medinsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="定点医药机构编号" align="center" prop="fixmedinsCode" />
      <el-table-column label="定点医药机构名称" align="center" prop="fixmedinsName" />
      <el-table-column label="医保区划" align="center" prop="admdvs" />
      <el-table-column label="医疗机构等级" align="center" prop="medinsLv" />
      <el-table-column label="有效标志" align="center" prop="valiFlag" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['basic:medins:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['basic:medins:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改医疗机构信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="定点医药机构编号" prop="fixmedinsCode">
          <el-input v-model="form.fixmedinsCode" placeholder="请输入定点医药机构编号" />
        </el-form-item>
        <el-form-item label="定点医药机构名称" prop="fixmedinsName">
          <el-input v-model="form.fixmedinsName" placeholder="请输入定点医药机构名称" />
        </el-form-item>
        <el-form-item label="医保区划" prop="admdvs">
          <el-input v-model="form.admdvs" placeholder="请输入医保区划" />
        </el-form-item>
        <el-form-item label="医疗机构等级" prop="medinsLv">
          <el-input v-model="form.medinsLv" placeholder="请输入医疗机构等级" />
        </el-form-item>
        <el-form-item label="有效标志" prop="valiFlag">
          <el-input v-model="form.valiFlag" placeholder="请输入有效标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMedins, getMedins, delMedins, addMedins, updateMedins } from "@/api/basic/medins";

export default {
  name: "Medins",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 医疗机构信息表格数据
      medinsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fixmedinsCode: null,
        fixmedinsName: null,
        admdvs: null,
        medinsLv: null,
        valiFlag: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        fixmedinsCode: [
          { required: true, message: "定点医药机构编号不能为空", trigger: "blur" }
        ],
        fixmedinsName: [
          { required: true, message: "定点医药机构名称不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询医疗机构信息列表 */
    getList() {
      this.loading = true;
      listMedins(this.queryParams).then(response => {
        this.medinsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        fixmedinsCode: null,
        fixmedinsName: null,
        admdvs: null,
        medinsLv: null,
        valiFlag: null,
        createBy: null,
        createTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.fixmedinsCode)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加医疗机构信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const fixmedinsCode = row.fixmedinsCode || this.ids
      getMedins(fixmedinsCode).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改医疗机构信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.fixmedinsCode != null) {
            updateMedins(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMedins(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const fixmedinsCodes = row.fixmedinsCode || this.ids;
      this.$modal.confirm('是否确认删除医疗机构信息编号为"' + fixmedinsCodes + '"的数据项？').then(function() {
        return delMedins(fixmedinsCodes);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('basic/medins/export', {
        ...this.queryParams
      }, `medins_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
