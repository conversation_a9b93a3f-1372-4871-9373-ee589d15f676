{"remainingRequest": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\dataAnalysis\\volaTask\\index.vue?vue&type=style&index=0&id=38f28935&scoped=true&lang=css", "dependencies": [{"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\src\\views\\dataAnalysis\\volaTask\\index.vue", "mtime": 1750390981849}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\WorkSpace\\workspace-random\\flyinspect\\flyinspect-ui\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgcGFkZGluZzogMjBweDsNCn0NCg0KLyog6KGo5qC86Ieq6YCC5bqU5qC35byPICovDQouZWwtdGFibGUgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLyog56Gu5L+d6KGo5qC85Zyo5bCP5bGP5bmV5LiK55qE5pi+56S6ICovDQpAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsNCiAgLmVsLXRhYmxlIHsNCiAgICBmb250LXNpemU6IDEycHg7DQogIH0NCg0KICAuZWwtdGFibGUgLmNlbGwgew0KICAgIHBhZGRpbmctbGVmdDogNXB4Ow0KICAgIHBhZGRpbmctcmlnaHQ6IDVweDsNCiAgfQ0KfQ0KDQovKiDooajmoLzooYzpq5jkvJjljJYgKi8NCi5lbC10YWJsZSAuZWwtdGFibGVfX3JvdyB7DQogIGhlaWdodDogYXV0bzsNCn0NCg0KLyog5pON5L2c5oyJ6ZKu5qC35byP5LyY5YyWICovDQouZWwtdGFibGUgLmVsLWJ1dHRvbi0tbWluaSB7DQogIG1hcmdpbjogMCAycHg7DQp9DQoNCi8qIOatpemqpOadoeagt+W8j+S8mOWMliAqLw0KLmN1c3RvbS1zdGVwcyB7DQogIG1hcmdpbi1ib3R0b206IDQwcHg7DQogIHBhZGRpbmc6IDAgMjBweDsNCn0NCg0KLmN1c3RvbS1zdGVwcyAuZWwtc3RlcF9fdGl0bGUgew0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi5jdXN0b20tc3RlcHMgLmVsLXN0ZXBfX2Rlc2NyaXB0aW9uIHsNCiAgZm9udC1zaXplOiAxM3B4Ow0KICBjb2xvcjogIzkwOTM5OTsNCn0NCg0KLyog5q2l6aqk5YaF5a655Yy65Z+fICovDQouc3RlcC1jb250ZW50IHsNCiAgbWluLWhlaWdodDogNDAwcHg7DQogIHBhZGRpbmc6IDAgMjBweDsNCn0NCg0KLnN0ZXAtaGVhZGVyIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAzMHB4Ow0KICBwYWRkaW5nLWJvdHRvbTogMjBweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjU7DQp9DQoNCi5zdGVwLWhlYWRlciBoMyB7DQogIGZvbnQtc2l6ZTogMjBweDsNCiAgY29sb3I6ICMzMDMxMzM7DQogIG1hcmdpbjogMCAwIDhweCAwOw0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQouc3RlcC1oZWFkZXIgaDMgaSB7DQogIG1hcmdpbi1yaWdodDogOHB4Ow0KICBjb2xvcjogIzQwOWVmZjsNCn0NCg0KLnN0ZXAtaGVhZGVyIHAgew0KICBjb2xvcjogIzYwNjI2NjsNCiAgbWFyZ2luOiAwOw0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi8qIOihqOWNleagt+W8j+S8mOWMliAqLw0KLnN0ZXAtZm9ybSB7DQogIG1heC13aWR0aDogMTAwJTsNCn0NCg0KLmZvcm0tc2VjdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDMwcHg7DQogIGJhY2tncm91bmQ6ICNmYWZhZmE7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgcGFkZGluZzogMjBweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2ViZWVmNTsNCn0NCg0KLnNlY3Rpb24tdGl0bGUgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQouc2VjdGlvbi10aXRsZSBpIHsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogIGNvbG9yOiAjNDA5ZWZmOw0KICBmb250LXNpemU6IDE4cHg7DQp9DQoNCi5zdGVwLWZvcm0gLmVsLWZvcm0taXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5zdGVwLWZvcm0gLmVsLWZvcm0taXRlbV9fbGFiZWwgew0KICBmb250LXdlaWdodDogNTAwOw0KICBjb2xvcjogIzYwNjI2NjsNCn0NCg0KLyog56m/5qKt5qGG5a655Zmo5qC35byPICovDQoudHJhbnNmZXItY29udGFpbmVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLnRyYW5zZmVyLXRpcHMgew0KICB3aWR0aDogMTAwJTsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLnRyYW5zZmVyLXdyYXBwZXIgew0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KfQ0KDQouY3VzdG9tLXRyYW5zZmVyIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQouY3VzdG9tLXRyYW5zZmVyIC5lbC10cmFuc2Zlci1wYW5lbCB7DQogIHdpZHRoOiAyODBweDsNCiAgaGVpZ2h0OiAzNTBweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZGNkZmU2Ow0KICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KfQ0KDQouY3VzdG9tLXRyYW5zZmVyIC5lbC10cmFuc2Zlci1wYW5lbF9faGVhZGVyIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsNCiAgY29sb3I6IHdoaXRlOw0KICBib3JkZXItcmFkaXVzOiA4cHggOHB4IDAgMDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLmN1c3RvbS10cmFuc2ZlciAuZWwtdHJhbnNmZXItcGFuZWxfX2hlYWRlciAuZWwtY2hlY2tib3ggew0KICBjb2xvcjogd2hpdGU7DQp9DQoNCi5jdXN0b20tdHJhbnNmZXIgLmVsLXRyYW5zZmVyLXBhbmVsX19maWx0ZXIgew0KICBwYWRkaW5nOiAxMnB4Ow0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KfQ0KDQouY3VzdG9tLXRyYW5zZmVyIC5lbC10cmFuc2Zlci1wYW5lbF9fbGlzdCB7DQogIGhlaWdodDogMjQwcHg7DQp9DQoNCi5jdXN0b20tdHJhbnNmZXIgLmVsLXRyYW5zZmVyLXBhbmVsX19pdGVtIHsNCiAgcGFkZGluZzogOHB4IDEycHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zczsNCn0NCg0KLmN1c3RvbS10cmFuc2ZlciAuZWwtdHJhbnNmZXItcGFuZWxfX2l0ZW06aG92ZXIgew0KICBiYWNrZ3JvdW5kOiAjZjVmN2ZhOw0KfQ0KDQouY3VzdG9tLXRyYW5zZmVyIC5lbC10cmFuc2Zlci1wYW5lbF9faXRlbSBpIHsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogIGNvbG9yOiAjNDA5ZWZmOw0KfQ0KDQovKiDlt7LpgInop4TliJnmkZjopoEgKi8NCi5zZWxlY3RlZC1zdW1tYXJ5IHsNCiAgd2lkdGg6IDEwMCU7DQogIG1heC13aWR0aDogNjAwcHg7DQogIGJhY2tncm91bmQ6ICNmMGY5ZmY7DQogIGJvcmRlcjogMXB4IHNvbGlkICNiM2Q4ZmY7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgcGFkZGluZzogMTZweDsNCn0NCg0KLnN1bW1hcnktaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTJweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgY29sb3I6ICM0MDllZmY7DQp9DQoNCi5zdW1tYXJ5LWhlYWRlciBpIHsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLnN1bW1hcnktdGFncyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtd3JhcDogd3JhcDsNCiAgZ2FwOiA0cHg7DQp9DQoNCi8qIOWvueivneahhuW6lemDqOaMiemSruagt+W8jyAqLw0KLmRpYWxvZy1mb290ZXIgew0KICB0ZXh0LWFsaWduOiByaWdodDsNCiAgcGFkZGluZzogMjBweCAyNHB4Ow0KICBiYWNrZ3JvdW5kOiAjZmFmYWZhOw0KICBib3JkZXItdG9wOiAxcHggc29saWQgI2ViZWVmNTsNCn0NCg0KLmRpYWxvZy1mb290ZXIgLmVsLWJ1dHRvbiB7DQogIG1hcmdpbi1sZWZ0OiAxMnB4Ow0KICBtaW4td2lkdGg6IDgwcHg7DQp9DQoNCi8qIOWTjeW6lOW8j+S8mOWMliAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5zdGVwLWNvbnRlbnQgew0KICAgIHBhZGRpbmc6IDAgMTBweDsNCiAgfQ0KDQogIC5jdXN0b20tdHJhbnNmZXIgLmVsLXRyYW5zZmVyLXBhbmVsIHsNCiAgICB3aWR0aDogMjQwcHg7DQogICAgaGVpZ2h0OiAzMDBweDsNCiAgfQ0KDQogIC5mb3JtLXNlY3Rpb24gew0KICAgIHBhZGRpbmc6IDE1cHg7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAytBA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dataAnalysis/volaTask", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\r\n      <el-form-item label=\"调度任务名称\" prop=\"volaTaskInfoName\">\r\n        <el-input\r\n          v-model=\"queryParams.volaTaskInfoName\"\r\n          placeholder=\"请输入调度任务名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"医疗机构等级\" prop=\"medinsLv\">\r\n        <el-input\r\n          v-model=\"queryParams.medinsLv\"\r\n          placeholder=\"请输入医疗机构等级\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"结算开始时间\" prop=\"setlTimeStart\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.setlTimeStart\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择结算开始时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"结算结束时间\" prop=\"setlTimeEnd\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.setlTimeEnd\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择结算结束时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"规则ID集合\" prop=\"ruleIds\">\r\n        <el-input\r\n          v-model=\"queryParams.ruleIds\"\r\n          placeholder=\"请输入规则ID集合\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"建立任务时间\" prop=\"taskTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.taskTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择建立任务时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['dataAnalysis:volaTask:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"volaTaskList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      fit\r\n      table-layout=\"auto\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column\r\n        label=\"调度任务名称\"\r\n        align=\"center\"\r\n        prop=\"volaTaskInfoName\"\r\n        min-width=\"150\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"筛查医疗机构集合\"\r\n        align=\"center\"\r\n        prop=\"medinsInfo\"\r\n        min-width=\"180\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"医疗机构等级\"\r\n        align=\"center\"\r\n        prop=\"medinsLv\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"医疗类别\"\r\n        align=\"center\"\r\n        prop=\"medType\"\r\n        width=\"100\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"结算开始时间\"\r\n        align=\"center\"\r\n        prop=\"setlTimeStart\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.setlTimeStart, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"结算结束时间\"\r\n        align=\"center\"\r\n        prop=\"setlTimeEnd\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.setlTimeEnd, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"规则ID集合\"\r\n        align=\"center\"\r\n        prop=\"ruleIds\"\r\n        min-width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"规则名称集合\"\r\n        align=\"center\"\r\n        prop=\"ruleNames\"\r\n        min-width=\"150\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"建立任务时间\"\r\n        align=\"center\"\r\n        prop=\"taskTime\"\r\n        width=\"120\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.taskTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"备注\"\r\n        align=\"center\"\r\n        prop=\"remark\"\r\n        min-width=\"120\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        width=\"120\"\r\n        fixed=\"right\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['dataAnalysis:volaTask:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['dataAnalysis:volaTask:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改调度任务信息对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <!-- 步骤条 -->\r\n      <el-steps\r\n        :active=\"currentStep\"\r\n        finish-status=\"success\"\r\n        align-center\r\n        class=\"custom-steps\">\r\n        <el-step title=\"基本信息\" description=\"填写调度任务基本信息\">\r\n          <i slot=\"icon\" class=\"el-icon-edit-outline\"></i>\r\n        </el-step>\r\n        <el-step title=\"规则选择\" description=\"选择检查规则\">\r\n          <i slot=\"icon\" class=\"el-icon-setting\"></i>\r\n        </el-step>\r\n      </el-steps>\r\n\r\n      <!-- 第一步：基本信息 -->\r\n      <div v-show=\"currentStep === 0\" class=\"step-content\">\r\n        <div class=\"step-header\">\r\n          <h3><i class=\"el-icon-edit-outline\"></i> 基本信息</h3>\r\n          <p>请填写调度任务的基本信息</p>\r\n        </div>\r\n\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"140px\" class=\"step-form\">\r\n          <div class=\"form-section\">\r\n            <div class=\"section-title\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>任务信息</span>\r\n            </div>\r\n            <el-row :gutter=\"24\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"调度任务名称\" prop=\"volaTaskInfoName\">\r\n                  <el-input\r\n                    v-model=\"form.volaTaskInfoName\"\r\n                    placeholder=\"请输入调度任务名称\"\r\n                    prefix-icon=\"el-icon-edit\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"医疗机构等级\" prop=\"medinsLv\">\r\n                  <el-select\r\n                    v-model=\"form.medinsLv\"\r\n                    placeholder=\"请选择医疗机构等级\"\r\n                    clearable\r\n                    style=\"width: 100%\">\r\n                    <el-option\r\n                      v-for=\"dict in dict.type.legent_lv\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.label\"\r\n                      :value=\"dict.value\">\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n\r\n            <el-form-item label=\"筛查医疗机构集合\" prop=\"medinsInfo\">\r\n              <el-select\r\n                v-model=\"form.medinsInfo\"\r\n                multiple\r\n                filterable\r\n                remote\r\n                reserve-keyword\r\n                placeholder=\"请选择筛查医疗机构\"\r\n                :remote-method=\"searchMedicalInstitutions\"\r\n                :loading=\"medListLoading\"\r\n                style=\"width: 100%\"\r\n                @focus=\"getMedicalInstitutionList\">\r\n                <el-option\r\n                  v-for=\"item in medicalInstitutionList\"\r\n                  :key=\"item.fixmedinsCode\"\r\n                  :label=\"item.fixmedinsName\"\r\n                  :value=\"item.fixmedinsCode\">\r\n                  <span style=\"float: left\">{{ item.fixmedinsName }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ item.fixmedinsCode }}</span>\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <div class=\"form-section\">\r\n            <div class=\"section-title\">\r\n              <i class=\"el-icon-date\"></i>\r\n              <span>结算时间</span>\r\n            </div>\r\n            <el-row :gutter=\"24\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"结算开始时间\" prop=\"setlTimeStart\">\r\n                  <el-date-picker\r\n                    clearable\r\n                    v-model=\"form.setlTimeStart\"\r\n                    type=\"date\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"请选择结算开始时间\"\r\n                    prefix-icon=\"el-icon-date\"\r\n                    style=\"width: 100%\">\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"结算结束时间\" prop=\"setlTimeEnd\">\r\n                  <el-date-picker\r\n                    clearable\r\n                    v-model=\"form.setlTimeEnd\"\r\n                    type=\"date\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"请选择结算结束时间\"\r\n                    prefix-icon=\"el-icon-date\"\r\n                    style=\"width: 100%\">\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 第二步：规则选择 -->\r\n      <div v-show=\"currentStep === 1\" class=\"step-content\">\r\n        <div class=\"step-header\">\r\n          <h3><i class=\"el-icon-setting\"></i> 规则选择</h3>\r\n          <p>请选择需要应用的检查规则</p>\r\n        </div>\r\n\r\n        <div class=\"transfer-container\">\r\n          <div class=\"transfer-tips\">\r\n            <el-alert\r\n              title=\"操作提示\"\r\n              type=\"info\"\r\n              :closable=\"false\"\r\n              show-icon>\r\n              <div slot=\"description\">\r\n                <p>• 从左侧选择需要的检查规则，点击右箭头添加到已选规则</p>\r\n                <p>• 支持搜索功能，可快速定位所需规则</p>\r\n                <p>• 至少需要选择一个规则才能完成创建</p>\r\n              </div>\r\n            </el-alert>\r\n          </div>\r\n\r\n          <div class=\"transfer-wrapper\">\r\n            <el-transfer\r\n              v-model=\"transferValue\"\r\n              :data=\"allRules\"\r\n              :titles=\"['可选规则', '已选规则']\"\r\n              :button-texts=\"['移除', '添加']\"\r\n              :format=\"{\r\n                noChecked: '共 ${total} 项',\r\n                hasChecked: '已选 ${checked}/${total} 项'\r\n              }\"\r\n              filterable\r\n              filter-placeholder=\"搜索规则名称\"\r\n              class=\"custom-transfer\">\r\n              <span slot-scope=\"{ option }\">\r\n                <i class=\"el-icon-document-checked\"></i>\r\n                {{ option.label }}\r\n              </span>\r\n            </el-transfer>\r\n          </div>\r\n\r\n          <div class=\"selected-summary\" v-if=\"transferValue.length > 0\">\r\n            <div class=\"summary-header\">\r\n              <i class=\"el-icon-success\"></i>\r\n              <span>已选择 {{ transferValue.length }} 个规则</span>\r\n            </div>\r\n            <div class=\"summary-tags\">\r\n              <el-tag\r\n                v-for=\"ruleId in transferValue\"\r\n                :key=\"ruleId\"\r\n                type=\"success\"\r\n                size=\"small\"\r\n                style=\"margin: 2px 4px 2px 0;\">\r\n                {{ getRuleName(ruleId) }}\r\n              </el-tag>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n        <el-button v-if=\"currentStep > 0\" @click=\"prevStep\">上一步</el-button>\r\n        <el-button v-if=\"currentStep < 1\" type=\"primary\" @click=\"nextStep\">下一步</el-button>\r\n        <el-button v-if=\"currentStep === 1\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listVolaTask, getVolaTask, delVolaTask, addVolaTask, updateVolaTask } from \"@/api/dataAnalysis/volaTask\";\r\nimport request from '@/utils/request';\r\n\r\nexport default {\r\n  name: \"VolaTask\",\r\n  dicts: ['legent_lv'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 调度任务信息表格数据\r\n      volaTaskList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 当前步骤\r\n      currentStep: 0,\r\n      // 穿梭框数据\r\n      transferData: [],\r\n      // 穿梭框选中的值\r\n      transferValue: [],\r\n      // 所有可选规则列表\r\n      allRules: [\r\n        { key: '1', label: '规则1：医保目录外用药检查' },\r\n        { key: '2', label: '规则2：超量用药检查' },\r\n        { key: '3', label: '规则3：重复用药检查' },\r\n        { key: '4', label: '规则4：配伍禁忌检查' },\r\n        { key: '5', label: '规则5：适应症检查' },\r\n        { key: '6', label: '规则6：诊疗项目合理性检查' },\r\n        { key: '7', label: '规则7：医疗服务价格检查' },\r\n        { key: '8', label: '规则8：住院天数合理性检查' }\r\n      ],\r\n      // 医疗机构列表\r\n      medicalInstitutionList: [],\r\n      // 医疗机构列表加载状态\r\n      medListLoading: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        volaTaskInfoName: null,\r\n        medinsInfo: null,\r\n        medinsLv: null,\r\n        medType: null,\r\n        setlTimeStart: null,\r\n        setlTimeEnd: null,\r\n        ruleIds: null,\r\n        ruleNames: null,\r\n        taskTime: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        volaTaskInfoName: [\r\n          { required: true, message: \"调度任务名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        medinsInfo: [\r\n          { required: true, message: \"筛查医疗机构集合不能为空\", trigger: \"blur\" }\r\n        ],\r\n        setlTimeStart: [\r\n          { required: true, message: \"结算开始时间不能为空\", trigger: \"blur\" }\r\n        ],\r\n        setlTimeEnd: [\r\n          { required: true, message: \"结算结束时间不能为空\", trigger: \"blur\" }\r\n        ],\r\n        // 注意：ruleIds、ruleNames、taskTime 由穿梭框和系统自动生成，不需要在第一步验证\r\n        // delFlag、createBy、createTime、updateBy、updateTime 由系统自动处理，不需要验证\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询调度任务信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listVolaTask(this.queryParams).then(response => {\r\n        this.volaTaskList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.currentStep = 0;\r\n      this.transferValue = [];\r\n      this.reset();\r\n    },\r\n    // 下一步\r\n    nextStep() {\r\n      if (this.currentStep === 0) {\r\n        // 验证第一步表单\r\n        this.$refs[\"form\"].validate(valid => {\r\n          if (valid) {\r\n            this.currentStep = 1;\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 上一步\r\n    prevStep() {\r\n      if (this.currentStep > 0) {\r\n        this.currentStep--;\r\n      }\r\n    },\r\n    // 获取规则名称\r\n    getRuleName(ruleId) {\r\n      const rule = this.allRules.find(r => r.key === ruleId);\r\n      return rule ? rule.label : '';\r\n    },\r\n    // 获取医疗机构列表\r\n    getMedicalInstitutionList() {\r\n      if (this.medicalInstitutionList.length > 0) {\r\n        return; // 如果已经有数据，不重复请求\r\n      }\r\n      this.medListLoading = true;\r\n      request({\r\n        url: '/dataAnalysis/volaTask/medList',\r\n        method: 'get'\r\n      }).then(response => {\r\n        if (response.code === 200) {\r\n          this.medicalInstitutionList = response.data || [];\r\n        } else {\r\n          this.$modal.msgError(response.msg || '获取医疗机构列表失败');\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取医疗机构列表失败:', error);\r\n        this.$modal.msgError('获取医疗机构列表失败');\r\n      }).finally(() => {\r\n        this.medListLoading = false;\r\n      });\r\n    },\r\n    // 搜索医疗机构\r\n    searchMedicalInstitutions(query) {\r\n      if (query !== '') {\r\n        this.medListLoading = true;\r\n        request({\r\n          url: '/dataAnalysis/volaTask/medList',\r\n          method: 'get',\r\n          params: { keyword: query }\r\n        }).then(response => {\r\n          if (response.code === 200) {\r\n            this.medicalInstitutionList = response.data || [];\r\n          }\r\n        }).catch(error => {\r\n          console.error('搜索医疗机构失败:', error);\r\n        }).finally(() => {\r\n          this.medListLoading = false;\r\n        });\r\n      } else {\r\n        this.getMedicalInstitutionList();\r\n      }\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.currentStep = 0;\r\n      this.transferValue = [];\r\n      this.form = {\r\n        volaTaskInfoId: null,\r\n        volaTaskInfoName: null,\r\n        medinsInfo: [], // 改为数组类型，支持多选\r\n        medinsLv: null,\r\n        medType: null,\r\n        setlTimeStart: null,\r\n        setlTimeEnd: null,\r\n        ruleIds: null,\r\n        ruleNames: null,\r\n        taskTime: null,\r\n        delFlag: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.volaTaskInfoId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加调度任务信息\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const volaTaskInfoId = row.volaTaskInfoId || this.ids\r\n      getVolaTask(volaTaskInfoId).then(response => {\r\n        this.form = response.data;\r\n        // 回显穿梭框数据\r\n        if (this.form.ruleIds) {\r\n          this.transferValue = this.form.ruleIds.split(',');\r\n        }\r\n        // 回显医疗机构数据\r\n        if (this.form.medinsInfo) {\r\n          // 如果是字符串，转换为数组\r\n          if (typeof this.form.medinsInfo === 'string') {\r\n            this.form.medinsInfo = this.form.medinsInfo.split(',');\r\n          }\r\n        } else {\r\n          this.form.medinsInfo = [];\r\n        }\r\n        this.open = true;\r\n        this.title = \"修改调度任务信息\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      // 处理穿梭框选中的规则\r\n      if (this.transferValue.length === 0) {\r\n        this.$modal.msgWarning(\"请至少选择一个检查规则\");\r\n        return;\r\n      }\r\n\r\n      // 处理医疗机构数据\r\n      if (!this.form.medinsInfo || this.form.medinsInfo.length === 0) {\r\n        this.$modal.msgWarning(\"请至少选择一个医疗机构\");\r\n        return;\r\n      }\r\n\r\n      // 创建提交数据的副本\r\n      const submitData = { ...this.form };\r\n\r\n      // 将医疗机构数组转换为逗号分隔的字符串\r\n      if (Array.isArray(submitData.medinsInfo)) {\r\n        submitData.medinsInfo = submitData.medinsInfo.join(',');\r\n      }\r\n\r\n      // 将选中的规则ID和名称设置到表单中\r\n      submitData.ruleIds = this.transferValue.join(',');\r\n      const selectedRuleNames = this.allRules\r\n        .filter(rule => this.transferValue.includes(rule.key))\r\n        .map(rule => rule.label)\r\n        .join(',');\r\n      submitData.ruleNames = selectedRuleNames;\r\n\r\n      // 设置当前时间为建立任务时间\r\n      submitData.taskTime = new Date().toISOString().split('T')[0];\r\n\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (submitData.volaTaskInfoId != null) {\r\n            updateVolaTask(submitData).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.currentStep = 0;\r\n              this.transferValue = [];\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addVolaTask(submitData).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.currentStep = 0;\r\n              this.transferValue = [];\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const volaTaskInfoIds = row.volaTaskInfoId || this.ids;\r\n      this.$modal.confirm('是否确认删除调度任务信息编号为\"' + volaTaskInfoIds + '\"的数据项？').then(function() {\r\n        return delVolaTask(volaTaskInfoIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('dataAnalysis/volaTask/export', {\r\n        ...this.queryParams\r\n      }, `volaTask_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 表格自适应样式 */\r\n.el-table {\r\n  width: 100%;\r\n}\r\n\r\n/* 确保表格在小屏幕上的显示 */\r\n@media (max-width: 768px) {\r\n  .el-table {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .el-table .cell {\r\n    padding-left: 5px;\r\n    padding-right: 5px;\r\n  }\r\n}\r\n\r\n/* 表格行高优化 */\r\n.el-table .el-table__row {\r\n  height: auto;\r\n}\r\n\r\n/* 操作按钮样式优化 */\r\n.el-table .el-button--mini {\r\n  margin: 0 2px;\r\n}\r\n\r\n/* 步骤条样式优化 */\r\n.custom-steps {\r\n  margin-bottom: 40px;\r\n  padding: 0 20px;\r\n}\r\n\r\n.custom-steps .el-step__title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.custom-steps .el-step__description {\r\n  font-size: 13px;\r\n  color: #909399;\r\n}\r\n\r\n/* 步骤内容区域 */\r\n.step-content {\r\n  min-height: 400px;\r\n  padding: 0 20px;\r\n}\r\n\r\n.step-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  padding-bottom: 20px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.step-header h3 {\r\n  font-size: 20px;\r\n  color: #303133;\r\n  margin: 0 0 8px 0;\r\n  font-weight: 600;\r\n}\r\n\r\n.step-header h3 i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n.step-header p {\r\n  color: #606266;\r\n  margin: 0;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.step-form {\r\n  max-width: 100%;\r\n}\r\n\r\n.form-section {\r\n  margin-bottom: 30px;\r\n  background: #fafafa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.section-title i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.step-form .el-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.step-form .el-form-item__label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n/* 穿梭框容器样式 */\r\n.transfer-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.transfer-tips {\r\n  width: 100%;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.transfer-wrapper {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.custom-transfer {\r\n  text-align: center;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel {\r\n  width: 280px;\r\n  height: 350px;\r\n  border-radius: 8px;\r\n  border: 1px solid #dcdfe6;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  border-radius: 8px 8px 0 0;\r\n  font-weight: 600;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__header .el-checkbox {\r\n  color: white;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__filter {\r\n  padding: 12px;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__list {\r\n  height: 240px;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__item {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__item:hover {\r\n  background: #f5f7fa;\r\n}\r\n\r\n.custom-transfer .el-transfer-panel__item i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n/* 已选规则摘要 */\r\n.selected-summary {\r\n  width: 100%;\r\n  max-width: 600px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n}\r\n\r\n.summary-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.summary-header i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.summary-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 4px;\r\n}\r\n\r\n/* 对话框底部按钮样式 */\r\n.dialog-footer {\r\n  text-align: right;\r\n  padding: 20px 24px;\r\n  background: #fafafa;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n  margin-left: 12px;\r\n  min-width: 80px;\r\n}\r\n\r\n/* 响应式优化 */\r\n@media (max-width: 768px) {\r\n  .step-content {\r\n    padding: 0 10px;\r\n  }\r\n\r\n  .custom-transfer .el-transfer-panel {\r\n    width: 240px;\r\n    height: 300px;\r\n  }\r\n\r\n  .form-section {\r\n    padding: 15px;\r\n  }\r\n}\r\n</style>\r\n"]}]}