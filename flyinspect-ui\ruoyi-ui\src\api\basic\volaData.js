import request from '@/utils/request'

// 查询违规信息列表
export function listVolaData(query) {
  return request({
    url: '/basic/volaData/list',
    method: 'get',
    params: query
  })
}

// 查询违规信息详细
export function getVolaData(setlFeeInfoId) {
  return request({
    url: '/basic/volaData/' + setlFeeInfoId,
    method: 'get'
  })
}

// 新增违规信息
export function addVolaData(data) {
  return request({
    url: '/basic/volaData',
    method: 'post',
    data: data
  })
}

// 修改违规信息
export function updateVolaData(data) {
  return request({
    url: '/basic/volaData',
    method: 'put',
    data: data
  })
}

// 删除违规信息
export function delVolaData(setlFeeInfoId) {
  return request({
    url: '/basic/volaData/' + setlFeeInfoId,
    method: 'delete'
  })
}
