// 定义筛选字段配置
export default [
  {
    label: '医保区划',
    prop: 'admdvs',
    component: 'el-cascader',
    multiple: false,
    checkStrictly: false,
    options: [],
    attrs: {
      placeholder: '请选择医保区划',
      clearable: true
    }
  },
  {
    label: '检查计划编号',
    prop: 'examPlanCode',
    component: 'el-input',
    attrs: {
      placeholder: '请输入检查计划编号',
      clearable: true
    }
  },
  {
    label: '检查计划名称',
    prop: 'examPlanName',
    component: 'el-input',
    attrs: {
      placeholder: '请输入检查计划名称',
      clearable: true
    }
  },
  {
    label: '计划开始日期',
    prop: 'planBegndate',
    component: 'el-date-picker',
    attrs: {
      placeholder: '请选择计划开始日期',
      clearable: true,
      type: 'date',
      valueFormat: 'yyyy-MM-dd'
    }
  },
  {
    label: '计划结束日期',
    prop: 'planEnddate',
    component: 'el-date-picker',
    attrs: {
      placeholder: '请选择计划结束日期',
      clearable: true,
      type: 'date',
      valueFormat: 'yyyy-MM-dd'
    }
  },
  {
    label: '所属部门名称',
    prop: 'locDeptName',
    component: 'el-input',
    attrs: {
      placeholder: '请输入所属部门名称',
      clearable: true
    }
  }
];
