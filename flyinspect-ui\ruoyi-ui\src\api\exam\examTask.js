import request from '@/utils/request'

// 查询检查任务结果列表
export function listExamTask(query) {
  return request({
    url: '/exam/examTask/list',
    method: 'get',
    params: query
  })
}

// 查询检查任务结果详细
export function getExamTask(examTaskRsltId) {
  return request({
    url: '/exam/examTask/' + examTaskRsltId,
    method: 'get'
  })
}

// 新增检查任务结果
export function addExamTask(data) {
  return request({
    url: '/exam/examTask',
    method: 'post',
    data: data
  })
}

// 修改检查任务结果
export function updateExamTask(data) {
  return request({
    url: '/exam/examTask',
    method: 'put',
    data: data
  })
}

// 删除检查任务结果
export function delExamTask(examTaskRsltId) {
  return request({
    url: '/exam/examTask/' + examTaskRsltId,
    method: 'delete'
  })
}
