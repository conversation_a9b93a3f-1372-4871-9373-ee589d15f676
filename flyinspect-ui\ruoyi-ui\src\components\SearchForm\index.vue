<template>
  <div>
    <el-form :model="formData" ref="searchForm" size="small" :inline="true" v-show="showSearch" label-width="auto">
      <div v-if="!showMore">
        <!-- 始终显示的筛选项 -->
        <el-form-item v-for="(item, index) in visibleFields" :key="index" :label="item.label" :prop="item.prop">
          <el-input v-if="item.component === 'el-input'" v-model="formData[item.prop]" v-bind="item.attrs"
            :style="`width: ${inputWidth}px`"></el-input>
          <el-select v-else-if="item.component === 'el-select'" v-model="formData[item.prop]" v-bind="item.attrs"
            :style="`width: ${inputWidth}px`">
            <el-option v-for="(option, optionIndex) in item.options"
              :key="`${item.prop}-${option.value}-${optionIndex}`" :label="option.label || option.dictLabel"
              :value="option.value || option.dictValue"></el-option>
          </el-select>
          <el-cascader v-else-if="item.component === 'el-cascader'" v-model="formData[item.prop]" v-bind="item.attrs"
            :style="`width: ${inputWidth}px`" :options="item.options || []" filterable
            :props="{ multiple: item.multiple, checkStrictly: item.checkStrictly }"></el-cascader>
          <el-date-picker v-else-if="item.component === 'el-date-picker'" v-model="formData[item.prop]"
            v-bind="item.attrs" :style="`width: ${inputWidth}px`" :type="item.type"></el-date-picker>
          <component v-else :is="item.component" v-bind="item.attrs" v-model="formData[item.prop]"
            :style="`width: ${inputWidth}px`"></component>
        </el-form-item>
      </div>
      <div v-else>
        <!-- 所有筛选项 -->
        <el-form-item v-for="(item, index) in fields" :key="index" :label="item.label" :prop="item.prop">
          <el-input v-if="item.component === 'el-input'" v-model="formData[item.prop]" v-bind="item.attrs"
            :style="`width: ${inputWidth}px`"></el-input>
          <el-select v-else-if="item.component === 'el-select'" v-model="formData[item.prop]" v-bind="item.attrs"
            :style="`width: ${inputWidth}px`">
            <el-option v-for="(option, optionIndex) in item.options"
              :key="`${item.prop}-${option.value}-${optionIndex}`" :label="option.label"
              :value="option.value || option.dictValue"></el-option>
          </el-select>
          <el-cascader v-else-if="item.component === 'el-cascader'" v-model="formData[item.prop]" v-bind="item.attrs"
            :style="`width: ${inputWidth}px`" :options="item.options || []" filterable
            :props="{ multiple: item.multiple, checkStrictly: item.checkStrictly }"></el-cascader>
          <el-date-picker v-else-if="item.component === 'el-date-picker'" v-model="formData[item.prop]"
            v-bind="item.attrs" :style="`width: ${inputWidth}px`" :type="item.type"></el-date-picker>
          <component v-else :is="item.component" v-bind="item.attrs" v-model="formData[item.prop]"
            :style="`width: ${inputWidth}px`"></component>
        </el-form-item>
      </div>
      <el-form-item style="display: flex;justify-content: flex-end;">
        <el-button @click="toggleMore" v-if="fields.length > defaultVisibleCount">
          <i :class="showMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          {{ showMore ? '收起' : '更多' }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'SearchForm',
  props: {
    // 表单数据
    modelValue: {
      type: Object,
      required: true
    },
    // 表单字段配置
    fields: {
      type: Array,
      required: true
    },
    // 默认显示的字段数量
    defaultVisibleCount: {
      type: Number,
      default: 4
    },
    // 是否显示搜索表单
    showSearch: {
      type: Boolean,
      default: true
    },
    // 输入框和选择器的默认宽度
    inputWidth: {
      type: Number,
      default: 240
    }
  },
  data() {
    return {
      formData: {},
      showMore: false
    }
  },
  computed: {
    // 默认显示的字段
    visibleFields() {
      return this.fields.slice(0, this.defaultVisibleCount)
    }
  },
  watch: {
    modelValue: {
      handler(val) {
        this.formData = { ...val }
      },
      deep: true,
      immediate: true
    },
    formData: {
      handler(val) {
        this.$emit('update:modelValue', val)
      },
      deep: true
    }
  },
  methods: {
    // 切换显示更多筛选项
    toggleMore() {
      this.showMore = !this.showMore
    },
    // 搜索
    handleSearch() {
      this.$emit('search', this.formData)
    },
    // 重置
    handleReset() {
      this.$refs.searchForm.resetFields()
      this.$emit('reset')
    },
    // 重置表单
    resetForm() {
      this.$refs.searchForm.resetFields()
    }
  }
}
</script>
