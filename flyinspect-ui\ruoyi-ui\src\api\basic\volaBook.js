import request from '@/utils/request'

// 查询物价书知识信息列表
export function listVolaBook(query) {
  return request({
    url: '/basic/volaBook/list',
    method: 'get',
    params: query
  })
}

// 查询物价书知识信息详细
export function getVolaBook(volaBookId) {
  return request({
    url: '/basic/volaBook/' + volaBookId,
    method: 'get'
  })
}

// 新增物价书知识信息
export function addVolaBook(data) {
  return request({
    url: '/basic/volaBook',
    method: 'post',
    data: data
  })
}

// 修改物价书知识信息
export function updateVolaBook(data) {
  return request({
    url: '/basic/volaBook',
    method: 'put',
    data: data
  })
}

// 删除物价书知识信息
export function delVolaBook(volaBookId) {
  return request({
    url: '/basic/volaBook/' + volaBookId,
    method: 'delete'
  })
}
