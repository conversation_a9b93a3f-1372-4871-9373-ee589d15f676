<template>
  <div class="warpper">
    <el-breadcrumb separator="/" class="breadcrumb_item_Warpper">
      <span
        class="el-breadcrumb__item"
        v-for="(item, index) in list"
        :key="index"
      >
        <span
          role="link"
          class="el-breadcrumb__inner"
          :class="innerStyle(item, index, list)"
          @click="toRouter(item, index)"
        >{{ item.label }}</span
        >
        <span role="presentation" class="el-breadcrumb__separator">/</span>
      </span>
    </el-breadcrumb>
    <span v-if="isShowTitle" class="breadcrumb_title">{{
        title ? title : list[list.length - 1].label
      }}</span>
  </div>
</template>

<script>
import constans from "../json/constans";
export default {
  name: "neu-breadcrumb",
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      }
    },
    title: {
      type: String,
      default: ""
    },
    isShowTitle: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  data() {
    return {};
  },
  methods: {
    toRouter(item, index) {
      if (item.onclick) {
        item.onclick();
      }
      if (!item.path) {
        return;
      }
      const routerPush = () => {
        let obj = item.params ? item.params(this) : null;
        if (this.list.length - 1 !== index) {
          this.$router.push({
            path: item.path,
            query: obj
          });
        }
      };
      if (item.ishasPreservation && item.ishasPreservation()) {
        const type = "warning";
        this.$confirm(
          `<i class='el-icon-${type} msg-icon'></i><span class='msg-title'>${constans.neuBreadcrumb_confirm_title}</span>
          <div class='msg-content'>${constans.neuBreadcrumb_confirm}</div>`,
          "",
          {
            confirmButtonText: constans.neuBreadcrumb_confirm_btn_confirm,
            cancelButtonText: constans.neuBreadcrumb_confirm_btn_cancel,
            dangerouslyUseHTMLString: true
          }
        )
          .then(() => {
            routerPush();
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: constans.neuBreadcrumb_error_message
            });
          });
      } else {
        routerPush();
      }
    },
    innerStyle(item, index, list) {
      if (index + 1 === list.length) {
        return "active";
      }
      if (!item.path && !item.onclick) {
        return "noPath";
      }
      return "";
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .el-breadcrumb .el-breadcrumb__item .el-breadcrumb__inner a {
  font-weight: normal;
}

::v-deep .el-breadcrumb .el-breadcrumb__item .el-breadcrumb__separator {
  color: #606266;
}

.breadcrumb_item_Warpper {
  height: 40px;
  line-height: 40px;
  padding-left: 16px;
  padding-bottom: 8px;
  background-color: white;
}

.breadcrumb_title {
  height: 39px;
  padding-bottom: 16px;
  padding-left: 16px;
  display: block;
  background-color: white;
  font-size: 16px;
  font-weight: 700 !important;
}

.noPath:hover {
  color: #606266 !important;
  cursor: default !important;
}

.el-breadcrumb__item:last-child .el-breadcrumb__inner:hover,
.el-breadcrumb__separator:hover {
  cursor: default;
}

.el-breadcrumb__inner:hover {
  color: #3ca0f6;
  cursor: pointer;
}

.active {
  color: #1b65b9 !important;
}
</style>
