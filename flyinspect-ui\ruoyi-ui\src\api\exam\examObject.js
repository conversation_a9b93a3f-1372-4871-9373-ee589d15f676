import request from '@/utils/request'

// 查询检查对象名录管理列表
export function listExamObject(query) {
  return request({
    url: '/exam/examObject/list',
    method: 'get',
    params: query
  })
}

// 查询检查对象名录管理详细
export function getExamObject(examObjListId) {
  return request({
    url: '/exam/examObject/' + examObjListId,
    method: 'get'
  })
}

// 新增检查对象名录管理
export function addExamObject(data) {
  return request({
    url: '/exam/examObject',
    method: 'post',
    data: data
  })
}

// 修改检查对象名录管理
export function updateExamObject(data) {
  return request({
    url: '/exam/examObject',
    method: 'put',
    data: data
  })
}

// 删除检查对象名录管理
export function delExamObject(examObjListId) {
  return request({
    url: '/exam/examObject/' + examObjListId,
    method: 'delete'
  })
}


